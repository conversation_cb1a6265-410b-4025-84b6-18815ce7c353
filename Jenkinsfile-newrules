// #!groovy

// import groovy.transform.Field
// import groovy.json.JsonOutput
// import groovy.json.JsonSlurper

// // path
// @Field def dataPathDev = 'newrules/dev/tidb-cloud/data-plane/'
// @Field def controlPathDev = 'newrules/dev/tidb-cloud/control-plane/'
// @Field def dataPathStaging = 'newrules/staging/tidb-cloud/data-plane/'
// @Field def controlPathStaging = 'newrules/staging/tidb-cloud/control-plane/'
// @Field def dataPathProd = 'newrules/prod/tidb-cloud/data-plane/'
// @Field def controlPathProd = 'newrules/prod/tidb-cloud/control-plane/'

// // region
// @Field def awsRegionProd = ['us-west-2', 'us-east-1', 'ap-northeast-1']
// @Field def gcpRegionProd = ['us-west1', 'asia-northeast1']

// @Field def vendorAWS = 'aws'
// @Field def vendorGCP = 'gcp'
// @Field def stackEKS = 'eks'
// @Field def stackGKE = 'gke'

// @Field def podYaml = '''
// apiVersion: v1
// kind: Pod
// spec:
//   containers:
//   - name: main
//     image: gcr.io/pingcap-public/runbooks-pulumi:v3.38.0
//     command:
//     - cat
//     tty: true
//     resources:
//       requests:
//         memory: "128Mi"
//         cpu: 100m
//       limits:
//         memory: "2Gi"
//         cpu: 2
//     env:
//     - name: DOCKER_HOST
//       value: tcp://localhost:2375
//   - name: dind
//     image: docker:18.09-dind
//     securityContext:
//       privileged: true
//   restartPolicy: Never
// '''

// pipeline {
//     agent any

//     options {
//         disableConcurrentBuilds()
//     }
//     environment {
//         // token
//         GET_TOKEN_URL = credentials('get_token_url')//"https://tidb-soc2.us.auth0.com/oauth/token"
//         AUTH0_CLIENT_ID = credentials('auth0_client_id')
//         AUTH0_CLIENT_SECRET = credentials('auth0_client_secret')
//         AUTH0_AUDIENCE = credentials('auth0_audience')//"https://tidb-soc2.us.auth0.com/api/v2/"//
//         // rules_id
//         DATA_PLANE_DEV_TIER_RULES_ID = 'tidb-cloud-data-plane-dev-tier-rule'
//         DATA_PLANE_DEDICATED_TIER_RULES_ID = '95072d4e-5676-4084-bdf1-09aca85881f2'
//         DATA_PLANE_DATA_MIGRATION_RULES_ID = 'data-migration-data-plane-rule'
//         DATA_PLANE_SERVERLESS_RULES_ID = 'tidb-cloud-data-plane-serverless-rule'
//         CONTROL_PLANE_DEV_TIER_RULES_ID = 'tidb-cloud-control-plane-dev-tier-rule'
//         CONTROL_PLANE_DEDICATED_TIER_RULES_ID = 'd5025c09-d861-417c-9cfe-797bd97ce0c3'
//         CONTROL_PLANE_BASE_RULES_ID = 'tidb-cloud-control-plane-base-rule'
//         CONTROL_PLANE_CLINIC_RULES_ID = 'clinic-control-plane-clinic-rule'
//         // domain
//         GLOBAL_DOMAIN = credentials('global_domain_prod')
//         GIT_COMMIT_MSG = sh(returnStdout: true, script: 'git log --oneline -n 1 --pretty=%B HEAD | head -n 1').trim()
//     }
//     stages {
//         stage('Delivery') {
//             when {
//                 not { changeRequest() }
//             }
//             agent {
//                 kubernetes {
//                     yaml podYaml
//                     defaultContainer 'main'
//                     customWorkspace '/home/<USER>/agent/workspace/go/src/github.com/tidbcloud/runbooks'
//                 }
//             }
//             stages {
//                 stage('Prepare') {
//                     steps {
//                         script{
//                             token = getToken()
//                         }
//                     }
//                 }
//                 // for simplicity, we use the same rule template for dev & staging
//                 stage('Apply dev & staging rules template') {
//                     when {
//                         not { changeRequest() }
//                     }
//                     steps {
//                         script {
//                             renderRules(stackEKS)
//                             writeRules("${DATA_PLANE_DEV_TIER_RULES_ID}-dev", getRules("${dataPathDev}dev-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${DATA_PLANE_DEDICATED_TIER_RULES_ID}-dev", getRules("${dataPathDev}dedicated-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${DATA_PLANE_DATA_MIGRATION_RULES_ID}-dev", getRules("${dataPathDev}data-migration/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${DATA_PLANE_SERVERLESS_RULES_ID}-dev", getRules("${dataPathDev}serverless/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${CONTROL_PLANE_DEV_TIER_RULES_ID}-dev", getRules("${controlPathDev}serverless/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${CONTROL_PLANE_DEDICATED_TIER_RULES_ID}-dev", getRules("${controlPathDev}dedicated-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${CONTROL_PLANE_BASE_RULES_ID}-dev", getRules("${controlPathDev}base/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${CONTROL_PLANE_CLINIC_RULES_ID}-dev", getRules("${controlPathDev}clinic/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                         }
//                         script {
//                             renderRules(stackGKE)
//                             writeRules("${DATA_PLANE_DEV_TIER_RULES_ID}-dev", getRules("${dataPathDev}dev-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${DATA_PLANE_DEDICATED_TIER_RULES_ID}-dev", getRules("${dataPathDev}dedicated-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${DATA_PLANE_DATA_MIGRATION_RULES_ID}-dev", getRules("${dataPathDev}data-migration/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${DATA_PLANE_SERVERLESS_RULES_ID}-dev", getRules("${dataPathDev}serverless/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${CONTROL_PLANE_DEV_TIER_RULES_ID}-dev", getRules("${controlPathDev}serverless/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${CONTROL_PLANE_DEDICATED_TIER_RULES_ID}-dev", getRules("${controlPathDev}dedicated-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${CONTROL_PLANE_BASE_RULES_ID}-dev", getRules("${controlPathDev}base/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${CONTROL_PLANE_CLINIC_RULES_ID}-dev", getRules("${controlPathDev}clinic/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                         }
//                     }
//                 }
//                 stage('Apply prod rules template') {
//                     when {
//                         branch 'master'
//                         not { changeRequest() }
//                     }
//                     steps {
//                         script {
//                             renderRules(stackEKS)
//                             writeRules("${DATA_PLANE_DEV_TIER_RULES_ID}", getRules("${dataPathProd}dev-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${DATA_PLANE_DEDICATED_TIER_RULES_ID}", getRules("${dataPathProd}dedicated-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${DATA_PLANE_DATA_MIGRATION_RULES_ID}", getRules("${dataPathProd}data-migration/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${DATA_PLANE_SERVERLESS_RULES_ID}", getRules("${dataPathDev}serverless/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${CONTROL_PLANE_DEV_TIER_RULES_ID}", getRules("${controlPathProd}serverless/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${CONTROL_PLANE_DEDICATED_TIER_RULES_ID}", getRules("${controlPathProd}dedicated-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${CONTROL_PLANE_BASE_RULES_ID}", getRules("${controlPathProd}base/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                             writeRules("${CONTROL_PLANE_CLINIC_RULES_ID}", getRules("${controlPathStaging}clinic/"), "${GLOBAL_DOMAIN}", "${token}", vendorAWS)
//                         }
//                         script {
//                             renderRules(stackGKE)
//                             writeRules("${DATA_PLANE_DEV_TIER_RULES_ID}", getRules("${dataPathProd}dev-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${DATA_PLANE_DEDICATED_TIER_RULES_ID}", getRules("${dataPathProd}dedicated-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${DATA_PLANE_DATA_MIGRATION_RULES_ID}", getRules("${dataPathProd}data-migration/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${DATA_PLANE_SERVERLESS_RULES_ID}", getRules("${dataPathDev}serverless/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${CONTROL_PLANE_DEV_TIER_RULES_ID}", getRules("${controlPathProd}serverless/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${CONTROL_PLANE_DEDICATED_TIER_RULES_ID}", getRules("${controlPathProd}dedicated-tier/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${CONTROL_PLANE_BASE_RULES_ID}", getRules("${controlPathProd}base/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                             writeRules("${CONTROL_PLANE_CLINIC_RULES_ID}", getRules("${controlPathStaging}clinic/"), "${GLOBAL_DOMAIN}", "${token}", vendorGCP)
//                         }
//                     }
//                 }
//             }
//             post {
//                 unsuccessful {
//                     script {
//                         sendNotification('Failure')
//                     }
//                 }
//                 success {
//                     echo 'done applying rules template.'
//                 }
//             }
//         }
//     }
// }

// def getRules(path) {
//     def files
//     dir(path) {
//         files = findFiles(glob: '**/*.yaml')
//     }
//     String rules = ''
//     for (file in files) {
//         content = readYaml file : "${path}${file.path}"
//         json = JsonOutput.toJson(content)
//         rules = "${rules}${json.substring(11, json.length()-2)},"
//     }
//     rules = "{\"groups\":[${rules.substring(0, rules.length()-1)}]}"
//     return rules
// }

// def getToken() {
//     def response = sh returnStdout: true, script: """
//     curl --location --request POST '${GET_TOKEN_URL}' \
//     --header 'content-type: application/x-www-form-urlencoded' \
//     --data-urlencode 'grant_type=client_credentials' \
//     --data-urlencode 'client_id=${AUTH0_CLIENT_ID}' \
//     --data-urlencode 'client_secret=${AUTH0_CLIENT_SECRET}' \
//     --data-urlencode 'audience=${AUTH0_AUDIENCE}'
//     """
//     def jsonSlurper = new JsonSlurper()
//     def object = jsonSlurper.parseText(response)
//     return object.access_token
// }

// def writeRules(RULES_ID, RULES, DOMAIN, TOKEN, VENDOR) {
//     if (awsRegionProd.size() > 0 && VENDOR == vendorAWS ) {
//         for (int i = 0; i < awsRegionProd.size(); i++) {
//             echo "write rules: ${RULES_ID} to prod/aws, region: ${awsRegionProd[i]}"
//             write(vendorAWS, awsRegionProd[i], RULES_ID, RULES, DOMAIN, TOKEN)
//             echo "write rule: ${RULES_ID} to prod/aws, region: ${awsRegionProd[i]}, done"
//         }
//     }
//     if (gcpRegionProd.size() > 0 && VENDOR == vendorGCP) {
//         for (int i = 0; i < gcpRegionProd.size(); i++) {
//             echo "write rules: ${RULES_ID} to prod/gcp, region: ${gcpRegionProd[i]}"
//             write(vendorGCP, gcpRegionProd[i], RULES_ID, RULES, DOMAIN, TOKEN)
//             echo "write rules: ${RULES_ID} to prod/gcp, region: ${gcpRegionProd[i]}, done"
//         }
//     }
// }

// def write(VENDOR, REGION, RULES_ID, RULES, DOMAIN, TOKEN) {
//     url = "http://${DOMAIN}/api/v1/alertrule-template"
//     data = """
//     {
//       "vendor": "${VENDOR}",
//       "region": "${REGION}",
//       "templateID": "${RULES_ID}",
//       "monitorPromRules": ${RULES}
//     }
//     """
//     httpPost(url, data, TOKEN)
// }

// def httpPost(URL, DATA = null, TOKEN) {
//     def conn = new URL(URL).openConnection()
//     conn.setRequestMethod('POST')

//     conn.setRequestProperty('Authorization', "Bearer ${TOKEN}")
//     conn.setRequestProperty('Content-Type', 'application/json')

//     conn.doOutput = true
//     def writer = new OutputStreamWriter(conn.outputStream)
//     writer.write(DATA)
//     writer.flush()
//     writer.close()

//     def json = new JsonSlurper()
//     def respText = json.parseText(conn.content.text)
//     echo "${respText}"
//     assert respText.code == 0
// }

// def renderRules(STACK) {
//     sh """
//     STACK=${STACK} make o11y-rules
//     """
// }

// def sendNotification(STATUS='SUCCESS') {
//     def WEBHOOK = 'https://open.feishu.cn/open-apis/bot/v2/hook/1482ebac-fcf1-4e1a-9f5b-78ff9ede9854'
//     def GIT_AUTHOR_NAME = sh(returnStdout: true, script: "git log -1 --pretty=format:'%an'").trim()

//     def DATA = """
// {
//     "msg_type": "post",
//     "content": {
//         "post": {
//             "zh_cn": {
//                 "title": "Update runbooks rules template in branch [${env.BRANCH_NAME}]: ${STATUS}",
//                 "content": [
//                     [{
//                         "tag": "text",
//                         "text": "Commit: "
//                     },
//                     {
//                         "tag": "a",
//                         "text": "${env.GIT_COMMIT_MSG}",
//                         "href": "https://github.com/tidbcloud/runbooks/commit/${env.GIT_COMMIT}"
//                     }],[{
//                         "tag": "text",
//                         "text": "Authored by: ${GIT_AUTHOR_NAME}"
//                     }]
//                 ]
//             }
//         }
//     }
// }
// """
//     sh "curl -X POST -H 'Content-Type: application/json' -d '${DATA.replaceAll('[\r\n]+', ' ')}' ${WEBHOOK}"
// }
