{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 52, "iteration": 1601016208704, "links": [], "panels": [{"datasource": "loki", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "gridPos": {"h": 18, "w": 24, "x": 0, "y": 0}, "id": 4, "options": {"showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{cluster=\"$cluster\",hostname=\"$hostname\",syslog=~\"$service\",stream=~\"$stream\"} |~ \"$search\"", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Logs Panel", "type": "logs"}], "refresh": "10s", "schemaVersion": 25, "style": "dark", "tags": ["kubelet", "dockerd", "containerd", "logs"], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "base/eks/us-west-2", "value": "base/eks/us-west-2"}, "datasource": "loki", "definition": "label_values({},cluster)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster", "options": [], "query": "label_values({},cluster)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "ip-10-0-126-151.us-west-2.compute.internal", "value": "ip-10-0-126-151.us-west-2.compute.internal"}, "datasource": "loki", "definition": "label_values({cluster=~\"$cluster\"}, hostname)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "hostname", "options": [], "query": "label_values({cluster=~\"$cluster\"}, hostname)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "kubelet", "value": "kubelet"}, "datasource": "loki", "definition": "label_values({cluster=~\"$cluster\",hostname=~\"$hostname\"}, syslog)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "service", "options": [], "query": "label_values({cluster=~\"$cluster\",hostname=~\"$hostname\"}, syslog)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "loki", "definition": "label_values({cluster=~\"$cluster\",hostname=~\"$hostname\",syslog=~\"service\"}, stream)", "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "stream", "options": [], "query": "label_values({cluster=~\"$cluster\",hostname=~\"$hostname\",syslog=~\"service\"}, stream)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": "", "value": ""}, "hide": 0, "label": null, "name": "search", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Syslog", "uid": "XuIKcMFGz", "version": 3}