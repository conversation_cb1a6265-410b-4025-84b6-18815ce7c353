{"annotations": {"list": [{"$$hashKey": "object:75", "builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Loki logs panel with prometheus variables ", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 12019, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "loki", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 0}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "loki", "expr": "sum(count_over_time({cluster=~\"$cluster\", namespace=~\"$namespace\", container=~\"$container\",stream=~\"$stream\"} |~ \"$search\"[$__interval]))", "refId": "A"}], "thresholds": [], "timeRegions": [], "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:168", "format": "short", "logBase": 1, "show": false}, {"$$hashKey": "object:169", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"datasource": "loki", "gridPos": {"h": 25, "w": 24, "x": 0, "y": 3}, "id": 2, "maxDataPoints": "", "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": true}, "targets": [{"datasource": "loki", "expr": "{cluster=\"$cluster\",namespace=\"$namespace\",container=~\"$container\",stream=~\"$stream\"} |~ \"$search\"", "refId": "A"}], "title": "Logs Panel", "type": "logs"}], "refresh": "30s", "schemaVersion": 37, "style": "dark", "tags": ["logs", "kubernetes"], "templating": {"list": [{"current": {"selected": false, "text": "base/eks/us-west-2", "value": "base/eks/us-west-2"}, "datasource": "loki", "definition": "label_values({},cluster)", "hide": 0, "includeAll": false, "multi": false, "name": "cluster", "options": [], "query": "label_values({},cluster)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "type": "query"}, {"current": {"selected": false, "text": "logging", "value": "logging"}, "datasource": "loki", "definition": "label_values({cluster=~\"$cluster\"}, namespace)", "hide": 0, "includeAll": false, "multi": false, "name": "namespace", "options": [], "query": "label_values({cluster=~\"$cluster\"}, namespace)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "loki", "value": "loki"}, "datasource": "loki", "definition": "label_values({cluster=~\"$cluster\", namespace=\"$namespace\"},container)", "hide": 0, "includeAll": false, "multi": false, "name": "container", "options": [], "query": "label_values({cluster=~\"$cluster\", namespace=\"$namespace\"},container)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {"selected": true, "text": "stderr", "value": "stderr"}, "datasource": "loki", "definition": "label_values({},stream)", "hide": 0, "includeAll": true, "multi": false, "name": "stream", "options": [], "query": "label_values({},stream)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "", "value": ""}, "hide": 0, "name": "search", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Loki dashboard quick search", "uid": "liz0yRCZz", "version": 31, "weekStart": ""}