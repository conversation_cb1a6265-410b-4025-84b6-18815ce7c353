apiVersion: v1
stringData:
  datasources.yaml: |-
    {
      "apiVersion":1,
      "datasources":[
        {
           "access":"proxy",
           "editable":false,
           "name":"loki",
           "orgId":1,
           "type":"loki",
           "url":"http://loki.logging.svc:3100",
           "jsonData": {
             "manageAlerts": false
           },
           "version":1
        },
        {
           "access":"proxy",
           "editable":false,
           "name":"prometheus",
           "orgId":1,
           "type":"prometheus",
           "url":"http://prometheus-k8s.monitoring.svc:9090",
           "jsonData": {
             "manageAlerts": false
           },
           "version":1
        },
        {
           "access":"proxy",
           "editable":false,
           "name":"tidb-cluster",
           "orgId":1,
           "type":"prometheus",
           "url":"http://thanos-query.monitoring.svc:9090",
           "jsonData": {
             "manageAlerts": false
           },
           "version":1
        },
        {
           "isDefault":true,
           "access":"proxy",
           "editable":false,
           "name":"thanos",
           "orgId":1,
           "type":"prometheus",
           "url":"http://thanos-query.monitoring.svc:9090",
           "jsonData": {
             "manageAlerts": false
           },
           "version":1
        },
        {
           "orgId":1,
           "name":"pingcap-loki",
           "type":"customized-loki-datasource",
           "access":"proxy",
           "isDefault":false,
           "jsonData":{
              "lokiDataSourceName":"loki",
              "promDataSourceName":"thanos"
           },
           "version":1,
           "readOnly":false
        }
      ]
    }
kind: Secret
metadata:
  name: grafana-datasources-v2
  namespace: monitoring
type: Opaque
