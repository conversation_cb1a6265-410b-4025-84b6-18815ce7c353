apiVersion: v1
kind: Service
metadata:
  name: mysql
  namespace: monitoring
spec:
  ports:
  - port: 3306
  selector:
    app: mysql
  clusterIP: None
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mysql
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: mysql
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
       - image: mysql:5.6
         name: mysql
        #  lifecycle:
        #    postStart:
        #      exec:
        #        command: 
        #        - "/bin/sh"
        #        - "-c"
        #        - "mysql -ppassword -e 'CREATE DATABASE IF NOT EXISTS grafana;'"
         env:
           # Use secret in real usage
         - name: MYSQL_ROOT_PASSWORD
           value: password
         ports:
         - containerPort: 3306
           name: mysql
         volumeMounts:
         - name: mysql-persistent-storage
           mountPath: /var/lib/mysql
         resources:
          requests:
            cpu: 500m
            memory: 1024Mi
          limits:
            cpu: 500m
            memory: 1024Mi
      volumes:
      - name: mysql-persistent-storage
        persistentVolumeClaim:
          claimName: mysql-pv-claim
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mysql-pv-claim
  namespace: monitoring
spec:
  storageClassName: prometheus.cloud-fast
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
