{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": ""}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "panel", "id": "heatmap", "name": "Heatmap", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "tidb-cluster", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": null, "iteration": 1626834446186, "links": [], "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The storage size per TiFlash instance.\n(Not including some disk usage of TiFlash-Proxy by now)", "editable": true, "error": false, "fill": 5, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 1}, "id": 53, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tiflash_system_current_metric_StoreSizeUsed{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Store size", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The available capacity size per TiFlash instance", "editable": true, "error": false, "fill": 5, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 1}, "id": 54, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tiflash_system_current_metric_StoreSizeAvailable{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Available size", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The capacity size per TiFlash instance", "editable": true, "error": false, "fill": 5, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 1}, "id": 55, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tiflash_system_current_metric_StoreSizeCapacity{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Capacity size", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiFlash uptime since last restart", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 21, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tiflash_system_asynchronous_metric_Uptime{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Uptime", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "dtdurations", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The memory usage per TiFlash instance", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tiflash_system_asynchronous_metric_jemalloc_retained{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "retained", "refId": "A"}, {"expr": "sum(tiflash_system_asynchronous_metric_jemalloc_mapped{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "mapped", "refId": "B"}, {"expr": "sum(tiflash_system_asynchronous_metric_jemalloc_resident{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "resident", "refId": "C"}, {"expr": "sum(tiflash_system_asynchronous_metric_jemalloc_allocated{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "allocated", "refId": "D"}, {"expr": "sum(tiflash_system_asynchronous_metric_jemalloc_active{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "active", "refId": "E"}, {"expr": "sum(tiflash_system_asynchronous_metric_jemalloc_metadata_thp{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "metadata_thp", "refId": "F"}, {"expr": "sum(tiflash_system_asynchronous_metric_jemalloc_metadata{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "metadata", "refId": "G"}, {"expr": "sum(tiflash_system_asynchronous_metric_mimalloc_current_rss{cluster_id=~\".*$cluster_id\", component=\"tiflash\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "mimalloc_rss", "refId": "H"}, {"expr": "sum(tiflash_system_asynchronous_metric_mimalloc_current_commit{cluster_id=~\".*$cluster_id\", component=\"tiflash\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "mimalloc_commit", "refId": "I"}, {"expr": "sum(tiflash_system_asynchronous_metric_mmap_alive{cluster_id=~\".*$cluster_id\", component=\"tiflash\"})", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "mmap", "refId": "J"}, {"expr": "tiflash_proxy_process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"tiflash\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "K"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiFlash CPU usage calculated with process CPU running seconds.", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 17}, "id": 51, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tiflash_proxy_process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tiflash\"}[2m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 1, "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of fsync operations.\n(Only counting storage engine of TiFlash by now. Not including TiFlash-Proxy)", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 17}, "id": 52, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_system_profile_event_FileFSync{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "FSync OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of open file descriptors action.\n(Only counting storage engine of TiFlash by now. Not including TiFlash-Proxy)", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 22, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_system_profile_event_FileOpen{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "Newly Open-{{instance}}", "refId": "A"}, {"expr": "sum(rate(tiflash_system_profile_event_FileOpenFailed{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "Newly Open Failed-{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "File Open OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of currently opened file descriptors.\n(Only counting storage engine of TiFlash by now. Not including TiFlash-Proxy)", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 50, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tiflash_proxy_process_open_fds{cluster_id=~\".*$cluster_id\", component=\"tiflash\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}, {"expr": "sum(tiflash_system_current_metric_OpenFileForWrite{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Write File-{{instance}}", "refId": "B"}, {"expr": "sum(tiflash_system_current_metric_OpenFileForRead{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Read File-{{instance}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Opened File Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Server", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 6, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 2}, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_coprocessor_request_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 2}, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_coprocessor_executor_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Executor QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 9}, "id": 11, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tiflash_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "999", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tiflash_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(tiflash_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tiflash_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "80", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 9}, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_coprocessor_request_error{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (reason)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{reason}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Error <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "id": 13, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tiflash_coprocessor_request_handle_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "999", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tiflash_coprocessor_request_handle_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(tiflash_coprocessor_request_handle_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tiflash_coprocessor_request_handle_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "80", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request Handle Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_coprocessor_response_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Response Bytes/Seconds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 23}, "id": 63, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tiflash_coprocessor_request_memory_usage_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "999-{{type}}", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tiflash_coprocessor_request_memory_usage_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99-{{type}}", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(tiflash_coprocessor_request_memory_usage_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95-{{type}}", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tiflash_coprocessor_request_memory_usage_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "80-{{type}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Cop task memory usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 23}, "id": 77, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tiflash_coprocessor_handling_request_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Handling Request Number", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Coprocessor", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 16, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 3}, "id": 17, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tiflash_schema_version{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Schema Version", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Executed DDL apply jobs per minute", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 3}, "id": 18, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(increase(tiflash_schema_apply_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}, {"expr": "avg(increase(tiflash_schema_trigger_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "triggle-by-{{type}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Schema Apply OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "opm", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Executed DDL jobs per minute", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 10}, "id": 19, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(increase(tiflash_schema_internal_ddl_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}, {"expr": "sum(increase(tiflash_schema_internal_ddl_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "total", "refId": "B"}, {"expr": "sum(increase(tiflash_schema_internal_ddl_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type,instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{type}} - {{instance}}", "refId": "C"}, {"expr": "sum(increase(tiflash_schema_internal_ddl_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "total - {{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Schema Internal DDL OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "opm", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 10}, "id": 20, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideZero": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/^applying/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tiflash_schema_apply_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "999", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tiflash_schema_apply_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(tiflash_schema_apply_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tiflash_schema_apply_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "80", "refId": "D"}, {"expr": "sum(tiflash_schema_applying{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "applying-{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Schema Apply Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": "2", "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "DDL", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 25, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The total count of different kinds of commands received", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 4}, "id": 41, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/delete_range|ingest/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tiflash_storage_command_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}, {"expr": "sum(rate(tiflash_system_profile_event_DMWriteBlock{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "write block", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Write Command OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "opm", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 4}, "id": 38, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/5min-write/", "yaxis": 2}, {"alias": "/5min-all/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(tiflash_storage_write_amplification{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "total-{{instance}}", "refId": "A"}, {"expr": "sum((rate(tiflash_system_profile_event_PSMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m]) + rate(tiflash_system_profile_event_WriteBufferFromFileDescriptorWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m]) + rate(tiflash_system_profile_event_WriteBufferAIOWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m])) / (rate(tiflash_system_profile_event_DMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m]))) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "5min-{{instance}}", "refId": "B"}, {"expr": "sum((rate(tiflash_system_profile_event_PSMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[10m]) + rate(tiflash_system_profile_event_WriteBufferFromFileDescriptorWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[10m]) + rate(tiflash_system_profile_event_WriteBufferAIOWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[10m])) / (rate(tiflash_system_profile_event_DMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[10m]))) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "10min-{{instance}}", "refId": "C"}, {"expr": "sum((rate(tiflash_system_profile_event_PSMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[30m]) + rate(tiflash_system_profile_event_WriteBufferFromFileDescriptorWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[30m]) + rate(tiflash_system_profile_event_WriteBufferAIOWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[30m])) / (rate(tiflash_system_profile_event_DMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[30m]))) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "30min-{{instance}}", "refId": "D"}, {"expr": "sum((rate(tiflash_system_profile_event_PSMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m]) + rate(tiflash_system_profile_event_WriteBufferFromFileDescriptorWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m]) + rate(tiflash_system_profile_event_WriteBufferAIOWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m]))) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "5min-all-{{instance}}", "refId": "E"}, {"expr": "sum(rate(tiflash_system_profile_event_DMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m])) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "5min-write-{{instance}}", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Write Amplification", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Total number of storage engine read tasks", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 11}, "id": 40, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_storage_read_tasks_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Read Tasks OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 11}, "id": 61, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/^RS Filter/", "yaxis": 2}, {"alias": "/^PK/", "yaxis": 2}, {"alias": "/^No Filter/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg((rate(tiflash_system_profile_event_DMFileFilterAftPKAndPackSet{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]) - rate(tiflash_system_profile_event_DMFileFilterAftRoughSet{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) / (rate(tiflash_system_profile_event_DMFileFilterAftPKAndPackSet{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "1min-{{instance}}", "refId": "B"}, {"expr": "avg((rate(tiflash_system_profile_event_DMFileFilterAftPKAndPackSet{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m]) - rate(tiflash_system_profile_event_DMFileFilterAftRoughSet{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m])) / (rate(tiflash_system_profile_event_DMFileFilterAftPKAndPackSet{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[5m]))) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "5min-{{instance}}", "refId": "C"}, {"expr": "sum(rate(tiflash_system_profile_event_DMFileFilterNoFilter{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "hide": true, "instant": false, "intervalFactor": 1, "legendFormat": "No Filter-{{instance}}", "refId": "A"}, {"expr": "sum(rate(tiflash_system_profile_event_DMFileFilterAftPKAndPackSet{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "hide": true, "instant": false, "intervalFactor": 1, "legendFormat": "PK Filter-{{instance}}", "refId": "D"}, {"expr": "sum(rate(tiflash_system_profile_event_DMFileFilterAftRoughSet{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "RS Filter-{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rough Set Filter Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": null, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Total number of storage's internal sub tasks", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 18}, "id": 39, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/(delta_merge)|(seg_)/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_storage_subtask_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type!~\"delta_merge|delta_merge_fg|delta_merge_bg_gc|seg_merge|seg_split\"}[2m])) by (type)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}, {"expr": "sum(increase(tiflash_storage_subtask_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=~\"delta_merge|delta_merge_fg|delta_merge_bg_gc|seg_merge|seg_split\"}[2m])) by (type)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Internal Tasks OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "opm", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Duration of storage's internal sub tasks", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 18}, "id": 42, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/^.*-delta_merge/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tiflash_storage_subtask_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,type))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "max-{{type}}", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tiflash_storage_subtask_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,type))", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "99-{{type}}", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(tiflash_storage_subtask_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,type))", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "95-{{type}}", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tiflash_storage_subtask_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,type))", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "80-{{type}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Internal Tasks Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Total number of storage's internal page gc tasks", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 25}, "id": 43, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tiflash_storage_page_gc_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Page GC Tasks OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "opm", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of storage's internal page gc tasks", "gridPos": {"h": 7, "w": 12, "x": 12, "y": 25}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 44, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(delta(tiflash_storage_page_gc_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Page GC Tasks Duration", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of different kinds of read operations", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 32}, "id": 46, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_system_profile_event_PSMWriteIOCalls{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "Page", "refId": "A"}, {"expr": "sum(rate(tiflash_system_profile_event_PSMWriteCalls{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "Page Calls", "refId": "B"}, {"expr": "sum(rate(tiflash_system_profile_event_PSMWritePages{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "PageFile", "refId": "C"}, {"expr": "sum(rate(tiflash_system_profile_event_WriteBufferFromFileDescriptorWrite{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "File Descriptor", "refId": "D"}, {"expr": "sum(rate(tiflash_system_profile_event_WriteBufferAIOWrite{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "AIO", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Write OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of different kinds of read operations", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 32}, "id": 47, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_system_profile_event_PSMReadIOCalls{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "Page", "refId": "A"}, {"expr": "sum(rate(tiflash_system_profile_event_PSMReadCalls{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "Page Calls", "refId": "B"}, {"expr": "sum(rate(tiflash_system_profile_event_PSMReadPages{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "PageFile", "refId": "C"}, {"expr": "sum(rate(tiflash_system_profile_event_ReadBufferFromFileDescriptorRead{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "File Descriptor", "refId": "D"}, {"expr": "sum(rate(tiflash_system_profile_event_ReadBufferAIORead{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "AIO", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Read OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The flow of different kinds of write operations", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 39}, "height": "", "id": 60, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeatedByRow": true, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_system_profile_event_WriteBufferFromFileDescriptorWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "File Descriptor", "refId": "A", "step": 10}, {"expr": "sum(rate(tiflash_system_profile_event_PSMWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Page", "refId": "B"}, {"expr": "sum(rate(tiflash_system_profile_event_WriteBufferAIOWriteBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "AIO", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Write flow", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The flow of different kinds of read operations", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 39}, "height": "", "id": 59, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeatedByRow": true, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_system_profile_event_ReadBufferFromFileDescriptorReadBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "File Descriptor", "refId": "A", "step": 10}, {"expr": "sum(rate(tiflash_system_profile_event_PSMReadBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "Page", "refId": "B"}, {"expr": "sum(rate(tiflash_system_profile_event_ReadBufferAIOReadBytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "AIO", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Read flow", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 47}, "id": 88, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/max_snapshot_lifetime/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tiflash_system_current_metric_DT_SegmentReadTasks{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "read_tasks-{{instance}}", "refId": "I"}, {"expr": "tiflash_system_current_metric_PSMVCCSnapshotsList{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "snapshot_list-{{instance}}", "refId": "A"}, {"expr": "tiflash_system_current_metric_PSMVCCNumSnapshots{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "heatmap", "hide": true, "intervalFactor": 1, "legendFormat": "num_snapshot-{{instance}}", "refId": "B"}, {"expr": "tiflash_system_current_metric_DT_SnapshotOfRead{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "read-{{instance}}", "refId": "C"}, {"expr": "tiflash_system_current_metric_DT_SnapshotOfReadRaw{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "read_raw-{{instance}}", "refId": "D"}, {"expr": "tiflash_system_current_metric_DT_SnapshotOfDeltaMerge{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "delta_merge-{{instance}}", "refId": "E"}, {"expr": "tiflash_system_current_metric_DT_SnapshotOfDeltaCompact{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "delta_compact-{{instance}}", "refId": "J"}, {"expr": "tiflash_system_current_metric_DT_SnapshotOfSegmentMerge{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "seg_merge-{{instance}}", "refId": "F"}, {"expr": "tiflash_system_current_metric_DT_SnapshotOfSegmentSplit{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "seg_split-{{instance}}", "refId": "G"}, {"expr": "tiflash_system_current_metric_DT_SnapshotOfPlaceIndex{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "place_index-{{instance}}", "refId": "H"}, {"expr": "tiflash_system_asynchronous_metric_MaxDTDeltaOldestSnapshotLifetime{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "max_snapshot_lifetime-{{instance}}", "refId": "K"}, {"expr": "tiflash_system_asynchronous_metric_MaxDTStableOldestSnapshotLifetime{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "max_snapshot_lifetime_stable-{{instance}}", "refId": "L"}, {"expr": "tiflash_system_asynchronous_metric_MaxDTMetaOldestSnapshotLifetime{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "max_snapshot_lifetime_meta-{{instance}}", "refId": "M"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "MVCC Snapshots", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The current processing number of  segments' background management", "fill": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 47}, "id": 67, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tiflash_system_current_metric_DT_DeltaMerge{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "delta_merge-{{instance}}", "refId": "A"}, {"expr": "avg(tiflash_system_current_metric_DT_SegmentSplit{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "seg_split-{{instance}}", "refId": "B"}, {"expr": "avg(tiflash_system_current_metric_DT_SegmentMerge{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "seg_merge-{{instance}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Current Data Management Tasks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The storage I/O limiter metrics.", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 55}, "id": 84, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_storage_io_limiter{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "I/O Limiter", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "I/O Limiter pending tasks.", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 55}, "id": 86, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tiflash_system_current_metric_RateLimiterPendingWriteRequest{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "other-{{instance}}", "refId": "A"}, {"exemplar": true, "expr": "avg(tiflash_system_current_metric_IOLimiterPendingBgWriteReq{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "hide": false, "interval": "", "legendFormat": "bgwrite-{{instance}}", "refId": "B"}, {"exemplar": true, "expr": "avg(tiflash_system_current_metric_IOLimiterPendingFgWriteReq{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "hide": false, "interval": "", "legendFormat": "fgwrite-{{instance}}", "refId": "C"}, {"exemplar": true, "expr": "avg(tiflash_system_current_metric_IOLimiterPendingBgReadReq{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "hide": false, "interval": "", "legendFormat": "bgread-{{instance}}", "refId": "D"}, {"exemplar": true, "expr": "avg(tiflash_system_current_metric_IOLimiterPendingFgReadReq{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "hide": false, "interval": "", "legendFormat": "fgread-{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "I/O Limiter Pending Tasks", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Storage", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 64, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The throughput of write and  delta's background management", "fill": 1, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 5}, "height": "", "id": 70, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeatedByRow": true, "seriesOverrides": [{"alias": "/total/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_storage_throughput_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=~\"write|ingest\"}[2m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "throughput_write", "refId": "A", "step": 10}, {"expr": "sum(rate(tiflash_storage_throughput_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type!~\"write|ingest\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "throughput_delta-management", "refId": "B"}, {"expr": "sum(tiflash_storage_throughput_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=~\"write|ingest\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "total_write", "refId": "C"}, {"expr": "sum(tiflash_storage_throughput_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type!~\"write|ingest\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "total_delta-management", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Write & Delta Management Throughput", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The stall duration of write and delete range", "fill": 1, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 14}, "id": 62, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "99-delta_merge", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tiflash_storage_write_stall_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, type, instance))", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "99-{{type}}-{{instance}}", "refId": "B"}, {"expr": "histogram_quantile(1, sum(rate(tiflash_storage_write_stall_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, type, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "max-{{type}}-{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Write Stall Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The throughput of write by instance", "fill": 1, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 22}, "height": "", "id": 89, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeatedByRow": true, "seriesOverrides": [{"alias": "/total/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_storage_throughput_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=~\"write|ingest\"}[2m])) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "throughput_write-{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Write Throughput By Instance", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The total count of different kinds of commands received", "fill": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 31}, "id": 90, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/delete_range|ingest/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_system_profile_event_DMWriteBlock{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance, type)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "write block-{{instance}}", "refId": "C"}, {"expr": "sum(increase(tiflash_storage_command_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance, type)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{type}}-{{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Write Command OPS By Instance", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "opm", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Storage Write Stall", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 34, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 100}, "id": 35, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_raft_read_index_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft Read Index OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 100}, "id": 36, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1.00, sum(rate(tiflash_raft_read_index_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "max", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tiflash_raft_read_index_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(tiflash_raft_read_index_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tiflash_raft_read_index_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "80", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft Batch Read Index Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 107}, "id": 37, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1.00, sum(rate(tiflash_raft_wait_index_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "max", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tiflash_raft_wait_index_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(tiflash_raft_wait_index_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tiflash_raft_wait_index_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "80", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft Wait Index Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of currently applying snapshots.", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 107}, "id": 75, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tiflash_system_current_metric_RaftNumSnapshotsPendingApply{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "Pending-{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Applying snapshots Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Duration of applying Raft write logs", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 114}, "id": 82, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tiflash_raft_apply_write_command_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": " 99%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tiflash_raft_apply_write_command_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tiflash_raft_apply_write_command_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) / sum(rate(tiflash_raft_apply_write_command_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Apply Raft write logs Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of applying Raft write logs", "gridPos": {"h": 7, "w": 12, "x": 12, "y": 114}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 81, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(delta(tiflash_raft_apply_write_command_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Apply Raft write logs Duration [Heatmap]", "tooltip": {"show": true, "showHistogram": true}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of pre-decode when applying region snapshot", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 121}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 72, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(delta(tiflash_raft_command_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"snapshot_predecode\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Snapshot Predecode Duration", "tooltip": {"show": true, "showHistogram": true}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of pre-decode when applying region snapshot", "gridPos": {"h": 7, "w": 12, "x": 12, "y": 121}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 73, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(delta(tiflash_raft_command_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"snapshot_flush\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Snapshot Flush Duration", "tooltip": {"show": true, "showHistogram": true}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The keys flow of different kinds of Raft operations", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 128}, "height": "", "id": 71, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeatedByRow": true, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tiflash_raft_process_keys{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (type)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Keys flow", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of ingesting SST", "gridPos": {"h": 7, "w": 12, "x": 12, "y": 128}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 74, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(delta(tiflash_raft_command_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"ingest_sst\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Ingest SST Duration", "tooltip": {"show": true, "showHistogram": true}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of decoding Region data into blocks when writing Region data to the storage layer. (Mixed with \"write logs\" and \"apply Snapshot\" operations)", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 135}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 76, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(delta(tiflash_raft_write_data_to_storage_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"decode\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Region write Duration (decode)", "tooltip": {"show": true, "showHistogram": true}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of writing Region data blocks to the storage layer (Mixed with \"write logs\" and \"apply Snapshot\" operations)", "gridPos": {"h": 7, "w": 12, "x": 12, "y": 135}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 87, "legend": {"show": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(delta(tiflash_raft_write_data_to_storage_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Region write Duration (write blocks)", "tooltip": {"show": true, "showHistogram": true}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}], "repeat": null, "title": "Raft", "type": "row"}], "refresh": "30s", "schemaVersion": 18, "style": "dark", "tags": ["tidb-mixin", "tiflash"], "templating": {"list": [{"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{status=\"active\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_name", "query": "label_values(dbaas_tenant_info{status=\"active\"},name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_name", "options": [{"selected": true, "text": "<PERSON><PERSON>-<PERSON>", "value": "<PERSON><PERSON>-<PERSON>"}], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "tidb-cluster", "definition": "label_values(tiflash_system_profile_event_Query{cluster_id=~\".*$cluster_id\"}, instance)", "hide": 0, "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": "label_values(tiflash_system_profile_event_Query{cluster_id=~\".*$cluster_id\"}, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "TiFlash-<PERSON><PERSON><PERSON>", "uid": "SVbh2xUWk", "version": 2}