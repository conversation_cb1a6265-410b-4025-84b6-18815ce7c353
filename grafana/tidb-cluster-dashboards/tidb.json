{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": ""}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "panel", "id": "heatmap", "name": "Heatmap", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "tidb-cluster", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": 28, "iteration": 1617019008967, "links": [], "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 138, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB query durations by histogram buckets with different percents", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 1}, "id": 80, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "MySQL commands processing numbers per second. See https://dev.mysql.com/doc/internals/en/text-protocol.html and https://dev.mysql.com/doc/internals/en/prepared-statements.html", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 1}, "id": 42, "legend": {"alignAsTable": false, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 1, "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_server_query_total{cluster_id=~\".*$cluster_id\"}[2m])) by (result)", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "query {{result}}", "refId": "A", "step": 60}, {"expr": "sum(rate(tidb_server_query_total{cluster_id=~\".*$cluster_id\", result=\"OK\"}[2m]  offset 1d))", "format": "time_series", "hide": true, "instant": false, "intervalFactor": 2, "legendFormat": "yesterday", "refId": "B", "step": 90}, {"expr": "sum(tidb_server_connections{cluster_id=~\".*$cluster_id\"}) * sum(rate(tidb_server_handle_query_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) / sum(rate(tidb_server_handle_query_duration_seconds_sum{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "hide": true, "instant": false, "intervalFactor": 2, "legendFormat": "ideal CPS", "refId": "C", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Command Per Second", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB statement statistics", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 7}, "id": 21, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_executor_statement_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}, {"expr": "sum(rate(tidb_executor_statement_total{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "total", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB command total statistics including both successful and failed ones", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 7}, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 1, "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tidb_server_query_total{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} {{type}} {{result}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPS By Instance", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB failed query statistics by query type", "fill": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 13}, "id": 137, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_server_execute_error_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type, instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": " {{type}}-{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Failed Query OPM", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB slow query statistics with slow query durations and coprocessor waiting/executing durations", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 25}, "id": 112, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.90, sum(rate(tidb_server_slow_query_process_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,sql_type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "all_proc_{{sql_type}}", "refId": "A"}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_server_slow_query_cop_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,sql_type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "all_cop_proc_{{sql_type}}", "refId": "B"}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_server_slow_query_wait_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,sql_type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "all_cop_wait_{{sql_type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Slow query", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB connection idle durations", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 25}, "id": 218, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_server_conn_idle_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", in_txn='1'}[2m])) by (le,in_txn))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "99-in-txn", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_server_conn_idle_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", in_txn='0'}[2m])) by (le,in_txn))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "99-not-in-txn", "refId": "B"}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_server_conn_idle_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", in_txn='1'}[2m])) by (le,in_txn))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "90-in-txn", "refId": "C"}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_server_conn_idle_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", in_txn='0'}[2m])) by (le,in_txn))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "90-not-in-txn", "refId": "D"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_server_conn_idle_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", in_txn='1'}[2m])) by (le,in_txn))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "80-in-txn", "refId": "E"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_server_conn_idle_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", in_txn='0'}[2m])) by (le,in_txn))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "80-not-in-txn", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connection Idle Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB durations for different query types with 99.9 percent buckets", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 25}, "id": 136, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,sql_type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{sql_type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "999 Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB durations for different query types with 99 percent buckets", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 25}, "id": 134, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,sql_type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{sql_type}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "99 Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB durations for different query types with 95 percent buckets", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 31}, "id": 132, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,sql_type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{sql_type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "95 Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB durations for different query types with 80 percent buckets", "fill": 1, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 31}, "id": 130, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.80, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,sql_type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{sql_type}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "80 Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Query Summary", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 139, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB durations with 80 percent buckets by instance", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 2}, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.80, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "B", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration 80 By Instance", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": "0.001", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB durations with 95 percent buckets by instance", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 2}, "id": 1, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ instance }}", "refId": "B", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration 95 By Instance", "tooltip": {"msResolution": true, "shared": false, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": ["max"]}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": "0.001", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB durations with 99 percent buckets by instance", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 9}, "id": 25, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration 99 By Instance", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": "0.001", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB durations with 99.9 percent buckets by instance", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 9}, "id": 81, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration 999 By Instance", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": "0.001", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB failed query statistics with failing information ", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "id": 94, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(tidb_server_execute_error_total{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}} @ {{instance}}", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Failed Query OPM Detail", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 2, "max": null, "min": "0.001", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The internal SQL is used by TiDB itself.", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "id": 68, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_session_restricted_sql_total{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Internal SQL OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Query Detail", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 140, "panels": [{"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB uptime since last restart", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 3}, "id": 184, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(time() - process_start_time_seconds{cluster_id=~\".*$cluster_id\", component=\"tidb\"})", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Uptime", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "dtdurations", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB process rss memory usage. TiDB heap memory size in use ", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 3}, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "process-{{instance}}", "refId": "A"}, {"expr": "go_memstats_heap_sys_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "HeapSys-{{instance}}", "refId": "B"}, {"expr": "go_memstats_heap_inuse_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "HeapInuse-{{instance}}", "refId": "C"}, {"expr": "go_memstats_heap_alloc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "HeapAlloc-{{instance}}", "refId": "D"}, {"expr": "go_memstats_heap_idle_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "HeapIdle-{{instance}}", "refId": "E"}, {"expr": "go_memstats_heap_released_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "hide": true, "interval": "", "legendFormat": "HeapReleased-{{instance}}", "refId": "F"}, {"expr": "go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "hide": true, "interval": "", "legendFormat": "GCTrigger-{{instance}}", "refId": "G"}, {"expr": "tidb_server_memory_usage{cluster_id=~\".*$cluster_id\", job=\"tidb\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{module}}-{{type}}-{{instance}}", "refId": "H"}, {"expr": "sum(tidb_server_memory_usage{cluster_id=~\".*$cluster_id\", job=\"tidb\"}) by (module, instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{module}}-{{instance}}", "refId": "I"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB cpu usage calculated with process cpu running seconds", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 10}, "id": 168, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}, {"alias": "/limit/", "color": "#C4162A", "fill": 0, "nullPointMode": "null"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[2m])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 40}, {"expr": "tidb_server_maxprocs{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "legendFormat": "limit-{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB current connection counts", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 10}, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "tidb_server_connections{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 40}, {"expr": "sum(tidb_server_connections{cluster_id=~\".*$cluster_id\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "total", "refId": "B", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connection Count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB process opened file descriptors count", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 17}, "id": 188, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_open_fds{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Open FD Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB connection disconnected counts", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 17}, "id": 205, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tidb_server_disconnection_total{cluster_id=~\".*$cluster_id\"}) by (instance, result)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{result}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disconnection Count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB process current goroutines count", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 24}, "id": 61, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": " go_goroutines{cluster_id=~\".*$cluster_id\", component=~\"tidb.*\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Goroutine Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB Server critical events total, including start/close/shutdown/hang etc", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 38}, "id": 49, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(tidb_server_event_total{cluster_id=~\".*$cluster_id\"}[10m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-server {{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Events OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB instance prepare statements count", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 45}, "id": 165, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tidb_server_prepared_stmts{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 40}, {"expr": "sum(tidb_server_prepared_stmts{cluster_id=~\".*$cluster_id\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "total", "refId": "B", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Prepare Statement Count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB instance monitor average keep alive times", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 45}, "id": 82, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_monitor_keep_alive_total{cluster_id=~\".*$cluster_id\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Keep Alive OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB instance critical errors count including panic etc", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 52}, "id": 54, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(tidb_server_panic_total{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "panic-{{instance}}", "refId": "A"}, {"expr": "increase(tidb_server_critical_error_total{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "critical-{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Panic And Critial Error", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB monitor time jump back count", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 52}, "id": 166, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_monitor_time_jump_back_total{cluster_id=~\".*$cluster_id\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Time Jump Back OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Duration (us) for getting token, it should be small until concurrency limit is reached.", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 59}, "id": 111, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_server_get_token_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Get Token Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB instance critical errors count including panic etc", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 59}, "id": 191, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tidb_server_critical_error_total{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Data traffic statistics between TiDB and the client.", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 66}, "id": 211, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/total/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tidb_server_packet_io_bytes_sum{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-{{type}}-rate", "refId": "A"}, {"expr": "tidb_server_packet_io_bytes_sum{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-{{type}}-total", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Client Data Traffic", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB processing handshake error count", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 66}, "id": 167, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_server_handshake_error_total{cluster_id=~\".*$cluster_id\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Handshake Error O<PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Server", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 141, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB transaction processing counts by type and source.", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 5}, "id": 69, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_session_transaction_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type, txn_mode)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-{{txn_mode}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Bucketed histogram of transaction execution durations, including retry", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 5}, "id": 72, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, txn_mode))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99-{{txn_mode}}", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, txn_mode))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95-{{txn_mode}}", "refId": "B"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, txn_mode))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80-{{txn_mode}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "TiDB statements numbers within one transaction.", "gridPos": {"h": 7, "w": 8, "x": 16, "y": 5}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 74, "legend": {"show": false}, "links": [], "options": {}, "reverseYBuckets": false, "targets": [{"expr": "sum(increase(tidb_session_transaction_statement_num_bucket[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Transaction Statement Num", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "short", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "TiDB transaction retry histogram bucket statistics", "gridPos": {"h": 7, "w": 8, "x": 0, "y": 12}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 67, "legend": {"show": false}, "links": [], "options": {}, "reverseYBuckets": false, "targets": [{"expr": "sum(increase(tidb_session_retry_num_bucket[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Transaction Retry Num", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "short", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Error numbers of transaction retry", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 12}, "id": 36, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_session_retry_error_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type, sql_type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-{{sql_type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Session Retry Error <PERSON>", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The duration of a transaction waits for a token when committing.", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 12}, "id": 196, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_batch_executor_token_wait_duration_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_tikvclient_batch_executor_token_wait_duration_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95", "refId": "B"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_tikvclient_batch_executor_token_wait_duration_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Commit Token Wait Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ns", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB total kv transaction counts", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 19}, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_count{cluster_id=~\".*$cluster_id\", type=\"commit\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Transaction OPS", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The duration of the transaction commit/rollback on TiKV.", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 19}, "id": 193, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99-{{type}}", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95-{{type}}", "refId": "B"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80-{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Transaction Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "regions transaction operates on count", "gridPos": {"h": 7, "w": 8, "x": 16, "y": 19}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 44, "legend": {"show": false}, "links": [], "options": {}, "reverseYBuckets": false, "targets": [{"expr": "sum(increase(tidb_tikvclient_txn_regions_num_bucket[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Transaction Regions Num", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "short", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv write times per transaction execution", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 26}, "id": 33, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/-sum/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tidb_tikvclient_txn_write_kv_num_sum{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-rate", "refId": "A"}, {"expr": "tidb_tikvclient_txn_write_kv_num_sum{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-sum", "refId": "B", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction Write KV Num Rate and Sum", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "kv write times per transaction execution", "gridPos": {"h": 7, "w": 8, "x": 8, "y": 26}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 209, "legend": {"show": false}, "links": [], "options": {}, "reverseYBuckets": false, "targets": [{"expr": "sum(increase(tidb_tikvclient_txn_write_kv_num_bucket[2m])) by (le)", "format": "heatmap", "intervalFactor": 1, "legendFormat": "{{le}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Transaction Write KV Num", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "short", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "The number of statement acquires locks.", "gridPos": {"h": 7, "w": 8, "x": 16, "y": 26}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 201, "legend": {"show": false}, "links": [], "options": {}, "reverseYBuckets": false, "targets": [{"expr": "sum(increase(tidb_session_statement_lock_keys_count_bucket[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Statement Lock Keys", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "short", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "When the pessimistic transaction begins to work, it will send heartbeat requests to update its TTL. \nThis metric is the latency of the send heartbeat operation.", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 33}, "id": 194, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.80, sum(rate(tidb_tikvclient_txn_heart_beat_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80-{{type}}", "refId": "B", "step": 40}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_tikvclient_txn_heart_beat_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95-{{type}}", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_txn_heart_beat_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99-{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Send HeartBeat Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv write size per transaction execution", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 33}, "id": 34, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/-sum/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tidb_tikvclient_txn_write_size_bytes_sum{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-rate", "refId": "A", "step": 40}, {"expr": "tidb_tikvclient_txn_write_size_bytes_sum{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-sum", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction Write Size Bytes Rate and Sum", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "kv write size per transaction execution", "gridPos": {"h": 7, "w": 8, "x": 16, "y": 33}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 210, "legend": {"show": false}, "links": [], "options": {}, "reverseYBuckets": false, "targets": [{"expr": "sum(increase(tidb_tikvclient_txn_write_size_bytes_bucket[2m])) by (le)", "format": "heatmap", "intervalFactor": 1, "legendFormat": "{{le}}", "refId": "A", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Transaction Write Size Bytes", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "decbytes", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The duration of a statement acquiring all pessimistic locks at a time.", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 40}, "id": 197, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.80, sum(rate(tidb_tikvclient_pessimistic_lock_keys_duration_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80", "refId": "B", "step": 40}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_tikvclient_pessimistic_lock_keys_duration_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_pessimistic_lock_keys_duration_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Acquire Pessimistic Locks Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "This metric means the pessimistic lives too long which is abnormal.", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 40}, "id": 195, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_ttl_lifetime_reach_total{cluster_id=~\".*$cluster_id\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TTL Lifetime Reach Counter", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "safe point loading times", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 40}, "id": 83, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_load_safepoint_total{cluster_id=~\".*$cluster_id\", type=\"ok\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "B", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Load Safepoint OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateSpectral", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "When the pessimistic statement is executed, the lock fails and it can retry automatically. The number of times the statement is retried is recorded.", "gridPos": {"h": 7, "w": 8, "x": 0, "y": 47}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 199, "legend": {"show": false}, "links": [], "options": {}, "reverseYBuckets": false, "targets": [{"expr": "sum(increase(tidb_session_statement_pessimistic_retry_count_bucket[2m])) by (le)", "format": "heatmap", "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A", "step": 10}], "timeFrom": null, "timeShift": null, "title": "Pessimistic Statement Retry OPS", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 0, "format": "short", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "This metric shows the OPS of different types of transactions.", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 46}, "id": 212, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_commit_txn_counter{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "2PC-{{type}}", "refId": "C"}, {"expr": "sum(rate(tidb_tikvclient_async_commit_txn_counter{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "async commit-{{type}}", "refId": "A"}, {"expr": "sum(rate(tidb_tikvclient_one_pc_txn_counter{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "1PC-{{type}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction Types Per Second", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "99th percentile of backoff count and duration in a transaction commit", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 46}, "id": 224, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/count.*/", "yaxis": 1}, {"alias": "/duration.*/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, rate(tidb_tikvclient_txn_commit_backoff_count_bucket{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "count - {{instance}}", "refId": "A", "step": 40}, {"expr": "histogram_quantile(0.99, rate(tidb_tikvclient_txn_commit_backoff_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "duration - {{instance}}", "refId": "B", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction Commit .99 Backoff", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": "count", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "s", "label": "duration", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "This metric refers to the SafeTS update status count.", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 53}, "id": 226, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_safets_update_counter{cluster_id=~\".*$cluster_id\"}[2m])) by (result, store)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{result}}-store-{{store}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "SafeTS Update Conuter", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The gap between SafeTS and current time", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 53}, "id": 225, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tidb_tikvclient_safets_gap_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance, store)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-store-{{store}}", "refId": "A", "step": 60}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Max SafeTS gap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Transaction", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 142, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time cost of parsing SQL to AST", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 82}, "id": 156, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_session_parse_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, sql_type))", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{sql_type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Parse Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time cost of building the query plan", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 82}, "id": 154, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_session_compile_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, sql_type))", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{sql_type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Compile Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time cost of executing the SQL which does not include the time to get the results of the query .", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 90}, "id": 169, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_session_execute_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, sql_type))", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{sql_type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Execution Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB executors using more cpu and memory resources", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 90}, "id": 76, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_executor_expensive_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Expensive Executors OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB plan cache hit total", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 98}, "id": 91, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_server_plan_cache_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Queries Using Plan Cache OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Executor", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 143, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "durations of distsql execution by type", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 122}, "id": 12, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [{"type": "dashboard"}], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_distsql_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999-{{type}}", "refId": "D"}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_distsql_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "99-{{type}}", "metric": "tidb_distsql_handle_query_duration_seconds_bucket{}", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_distsql_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90-{{type}}", "refId": "B"}, {"expr": "histogram_quantile(0.50, sum(rate(tidb_distsql_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "50-{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Distsql Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": "0.0005", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "distsql query handling durations per second", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 122}, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_distsql_handle_query_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "tidb_distsql_query_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Distsql QPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "the numebr of distsql partial scan numbers", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 129}, "id": 60, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_distsql_scan_keys_partial_num_count{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "tidb_distsql_query_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Distsql Partial QPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "the numebr of distsql scan numbers", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 129}, "id": 57, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tidb_distsql_scan_keys_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "100", "refId": "A"}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_distsql_scan_keys_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90", "refId": "B"}, {"expr": "histogram_quantile(0.50, sum(rate(tidb_distsql_scan_keys_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "50", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "the numebr of distsql partial scan key numbers", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 129}, "id": 58, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tidb_distsql_scan_keys_partial_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "100", "refId": "A"}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_distsql_scan_keys_partial_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90", "refId": "B"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_distsql_scan_keys_partial_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "50", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON>an <PERSON> Partial Num", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "distsql partial numbers per query", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 136}, "id": 59, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tidb_distsql_partial_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "100", "refId": "A"}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_distsql_partial_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90", "refId": "B"}, {"expr": "histogram_quantile(0.50, sum(rate(tidb_distsql_partial_num_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "50", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Partial Num", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB coprocessor cache hit, evict and miss number", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 163}, "id": 175, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_distsql_copr_cache_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor Cache", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage coprocessor processing durations", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 136}, "id": 41, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_tikvclient_cop_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor Seconds 999", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Distsql", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 144, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv backoff time durations by type", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "id": 6, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": true, "min": false, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_tikvclient_backoff_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999", "refId": "A", "step": 40}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_backoff_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_tikvclient_backoff_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Backoff Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 2, "description": "kv region error times", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_region_err_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tidb_server_session_execute_parse_duration_count", "refId": "A", "step": 40}, {"expr": "sum(rate(tidb_tikvclient_region_err_total{cluster_id=~\".*$cluster_id\"}{EXTERNAL_LABELtype=\"server_is_busy\"}[2m]))", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "sum", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TiClient Region Error OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage backoff times", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "id": 53, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_backoff_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Backoff OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "lock resolve times", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_lock_resolver_actions_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tidb_tikvclient_lock_resolver_actions_total{}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Lock Resolve OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "lock cleanup failed times and safe point update times", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 21}, "id": 84, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_lock_cleanup_task_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "cleanup_secondary_failure_{{type}}", "metric": "tidb_tikvclient_lock_resolver_actions_total{}", "refId": "A", "step": 40}, {"expr": "sum(rate(tidb_tikvclient_load_safepoint_total{cluster_id=~\".*$cluster_id\", type=\"fail\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "load_safepoint_failure", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Other Errors OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "This metric shows the reasons of replica selector failure (which needs a backoff).", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 21}, "id": 223, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_replica_selector_failure_counter{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Replica Selector Failure Per Second", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "KV Errors", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 145, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv request total by instance and command type", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 9}, "id": 172, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_request_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (instance, type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{type}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Request OPS", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv requests durations by store", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 9}, "id": 48, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_request_seconds_bucket{cluster_id=~\".*$cluster_id\", store!=\"0\"}[2m])) by (le, store))", "format": "time_series", "intervalFactor": 2, "legendFormat": "store-{{store}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Request Duration 99 by store", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv request durations by request type", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 9}, "id": 30, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_request_seconds_bucket{cluster_id=~\".*$cluster_id\", store!=\"0\"}[2m])) by (le,type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Request Duration 99 by type", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv requests that's forwarded by different stores", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 15}, "id": 219, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_forward_request_counter{cluster_id=~\".*$cluster_id\"}[2m])) by (from_store, to_store, result)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{from_store}}-to-{{to_store}}-{{result}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Request Forwarding OPS", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv requests that's forwarded by different stores, grouped by request type", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 15}, "id": 220, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_forward_request_counter{cluster_id=~\".*$cluster_id\"}[2m])) by (type, result)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-{{result}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Request Forwarding OPS by Type", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "KV Request", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 147, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "pd command count by type", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 10}, "id": 20, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(pd_client_cmd_handle_cmds_duration_seconds_count{cluster_id=~\".*$cluster_id\", type!=\"tso\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD Client CMD OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "pd client command durations by type within 99.9 percent buckets", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 10}, "id": 35, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type!~\"tso|tso_async_wait\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999-{{type}}", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type!~\"tso|tso_async_wait\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99-{{type}}", "refId": "B"}, {"expr": "histogram_quantile(0.90, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type!~\"tso|tso_async_wait\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90-{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD Client CMD Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "pd client command fail count by type", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 10}, "id": 43, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(pd_client_cmd_handle_failed_cmds_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD Client CMD Fail OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The duration of a client calling GetTSAsync until received the TS result.", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 17}, "id": 79, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(pd_client_cmd_handle_cmds_duration_seconds_count{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "cmd", "refId": "C"}, {"expr": "sum(rate(pd_client_request_handle_requests_duration_seconds_count{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "request", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD TSO OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The duration of a client starting to wait for the TS until received the TS result.", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 17}, "id": 77, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"wait\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"wait\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.90, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"wait\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD TSO Wait Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The duration of a client sending TSO request until received the response.", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 17}, "id": 78, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(pd_client_request_handle_requests_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(pd_client_request_handle_requests_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.90, sum(rate(pd_client_request_handle_requests_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD TSO RPC Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The duration of the waiting time for getting the start timestamp oracle", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 24}, "id": 159, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_pdclient_ts_future_wait_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_pdclient_ts_future_wait_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_pdclient_ts_future_wait_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Start TSO Wait Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 23}, "id": 222, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "pd_client_request_forwarded_status", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{delegate}}-{{host}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request forwarded status", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "PD Client", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 148, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB loading schema time durations by instance", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 47}, "id": 27, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_domain_load_schema_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB loading schema times including both failed and successful ones", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 47}, "id": 28, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*failed/", "bars": true}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_domain_load_schema_total{cluster_id=~\".*$cluster_id\"}[2m])) by (instance,type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{type}}", "metric": "tidb_domain_load_schema_duration_count", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Load Schema OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 2, "description": "TiDB schema lease error counts", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 54}, "id": 29, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_session_schema_lease_error_total{cluster_id=~\".*$cluster_id\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "tidb_server_", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON><PERSON> Lea<PERSON> Error <PERSON>", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB load privilege counts", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 54}, "id": 157, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*failed/", "bars": true}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_domain_load_privilege_total{cluster_id=~\".*$cluster_id\"}[2m])) by (instance,type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{type}}", "metric": "tidb_domain_load_schema_duration_count", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Load Privilege OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 149, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB DDL duration statistics", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 12}, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_ddl_handle_job_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DDL Duration 95", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB batch add index durations by histogram buckets", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 12}, "id": 63, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tidb_ddl_batch_add_idx_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}, {"expr": "sum(rate(tidb_ddl_add_index_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Batch Add Index Duration 100", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB ddl request in queue", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 12}, "id": 62, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tidb_ddl_waiting_jobs{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DDL Waiting Jobs Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB different ddl worker numbers", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 19}, "id": 55, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(tidb_ddl_worker_operation_total{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DDL META OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB worker duration by type, action, results", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 19}, "id": 56, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(increase(tidb_ddl_worker_operation_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type, action, result))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-{{action}}-{{result}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DDL Worker Duration 99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB ddl schema syncer statistics, including init, start, watch, clear function call time cost", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 26}, "id": 64, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tidb_ddl_deploy_syncer_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type, result))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-{{result}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Deploy Syncer Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB ddl owner time operations on etcd duration statistics ", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 26}, "id": 65, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tidb_ddl_owner_handle_syncer_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type, result))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-{{result}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Owner <PERSON><PERSON>er <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB schema syncer version update time duration", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 26}, "id": 66, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tidb_ddl_update_self_ver_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, result))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{result}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Update Self Version Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "executed DDL jobs per minute", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 190, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_ddl_handle_job_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{ type }}", "refId": "A"}, {"expr": "sum(rate(tidb_ddl_handle_job_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "total", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DDL OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB DDL backfill progress in percentage. The value is [0,100]", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "id": 192, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tidb_ddl_backfill_percentage_progress{cluster_id=~\".*$cluster_id\", type=\"add_index\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-{{type}}", "refId": "A"}, {"expr": "tidb_ddl_backfill_percentage_progress{cluster_id=~\".*$cluster_id\", type=\"modify_column\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-{{type}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "DDL backfill progress in percentage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "DDL", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 150, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB auto analyze time durations within 95 percent histogram buckets", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 149}, "id": 46, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_statistics_auto_analyze_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "auto analyze duration", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Auto Analyze Duration 95", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB auto analyze query per second", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 149}, "id": 47, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_statistics_auto_analyze_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Auto Analyze QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB statistics inaccurate rate", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 149}, "id": 70, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_statistics_stats_inaccuracy_rate_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "A", "step": 30}, {"expr": "histogram_quantile(0.90, sum(rate(tidb_statistics_stats_inaccuracy_rate_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90", "refId": "B"}, {"expr": "histogram_quantile(0.50, sum(rate(tidb_statistics_stats_inaccuracy_rate_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "50", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Stats Inaccuracy Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB optimizer using pseudo estimation counts", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 156}, "id": 71, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_statistics_pseudo_estimation_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pseudo Estimation OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB dumping statistics back to kv storage times", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 156}, "id": 92, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_statistics_dump_feedback_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB store quering feedback counts", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 156}, "id": 170, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_statistics_store_query_feedback_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Store Query Feedback QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Counter of query feedback whose actual count is much different than calculated by current statistics", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 163}, "id": 113, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_statistics_high_error_rate_feedback_total{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "Significant Feedback", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Significant Feedback", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB updating statistics using feed back counts", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 163}, "id": 93, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_statistics_update_stats_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Update Stats OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB fast analyze statistics ", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 163}, "id": 173, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(1, sum(rate(tidb_statistics_fast_analyze_status_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Fast Analyze Status 100", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 170}, "hiddenSeries": false, "id": 229, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_statistics_sync_load_total{cluster_id=~\".*$cluster_id\"}[1m])) by (type)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "sync-load", "refId": "A", "step": 30}, {"exemplar": true, "expr": "sum(rate(tidb_statistics_sync_load_timeout_total{cluster_id=~\".*$cluster_id\"}[1m])) by (type)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "timeout", "refId": "B", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sync Load QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 170}, "hiddenSeries": false, "id": 230, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(tidb_statistics_sync_load_latency_millis_bucket{cluster_id=~\".*$cluster_id\"}[1m])) by (le))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "sync-load", "refId": "A", "step": 30}, {"exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(tidb_statistics_read_stats_latency_millis_bucket{cluster_id=~\".*$cluster_id\"}[1m])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "read-stats", "refId": "B", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sync Load Latency 95 (ms)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB table stats healthy distribution", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 170}, "hiddenSeries": false, "id": 233, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "avg(tidb_statistics_stats_healthy{cluster_id=~\".*$cluster_id\"}) by (type)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Stats Healthy Distribution", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB managing stats cache by lru", "editable": true, "error": false, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 177}, "hiddenSeries": false, "id": 234, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.11", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "tidb_statistics_stats_cache_lru_val{k8s_cluster=\"$k8s_cluster\", tidb_cluster=\"$tidb_cluster\", type=\"track\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "track-{{instance}}", "refId": "A"}, {"exemplar": true, "expr": "tidb_statistics_stats_cache_lru_val{k8s_cluster=\"$k8s_cluster\", tidb_cluster=\"$tidb_cluster\", type=\"capacity\"}", "hide": true, "interval": "", "legendFormat": "capacity--{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Stats Cache LRU Cost", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB managing stats cache by lru", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 177}, "hiddenSeries": false, "id": 235, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.11", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_statistics_stats_cache_lru_op{k8s_cluster=\"$k8s_cluster\", tidb_cluster=\"$tidb_cluster\"}[1m])) by (type)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Stats Cache LRU OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:90", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:91", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Statistics", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 161, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB new session durations for new etcd sessions", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 150}, "id": 162, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_owner_new_session_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance, result))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{result}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "New ETCD Session Duration 95", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB owner  watcher counts", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 150}, "id": 163, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_owner_watch_owner_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type, result, instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-{{result}}-{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Owner Watcher O<PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Owner", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 151, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB auto id requests per second including  single table/global auto id processing and single table auto id rebase processing", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "id": 50, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_autoid_operation_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "AutoID QPS", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "AutoID QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB auto id requests durations", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "id": 51, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_autoid_operation_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99-{{type}}", "refId": "B"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_autoid_operation_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80-{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "AutoID Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": "0.001", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB region cache operations count", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 21}, "id": 164, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_region_cache_operations_total{cluster_id=~\".*$cluster_id\", result=\"err\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Region Cache Error OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB meta operation durations including get/set schema and ddl jobs", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 21}, "id": 52, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_meta_operation_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Meta Operations Duration 99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Meta", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 152, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage garbage collection counts by type", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 15}, "id": 85, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_tikvclient_gc_worker_actions_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Worker Action OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 2, "description": "kv storage garbage collection time durations", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 15}, "id": 86, "legend": {"avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_gc_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration 99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage garbage collection config including gc_life_time and gc_run_interval", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 15}, "id": 87, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(tidb_tikvclient_gc_config{cluster_id=~\".*$cluster_id\"}) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Config", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage garbage collection failing counts", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 22}, "id": 88, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_tikvclient_gc_failure{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC Failure OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage unsafe destroy range failed counts", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 22}, "id": 158, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_tikvclient_gc_unsafe_destroy_range_failures{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Delete Range Failure OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage region garbage collection clean too many locks count", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 22}, "id": 90, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_tikvclient_gc_region_too_many_locks{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "Locks Error <PERSON>", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Too Many Locks Error OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage garbage collection results including failed and successful ones", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 29}, "id": 89, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_tikvclient_gc_action_result{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Action Result OPM", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage delete range task execution status by type", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 29}, "id": 181, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tidb_tikvclient_range_task_stats{cluster_id=~\".*$cluster_id\"}) by (type, result)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-{{result}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Delete Range Task Status", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage range worker processing one task duration", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 29}, "id": 182, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_tikvclient_range_task_push_duration_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Push Task Duration 95", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "GC", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 178, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Metrics for 'no available connection'.\nThere should be no data here if the connection between TiDB and TiKV is healthy.", "fill": 1, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 16}, "id": 203, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "delta(tidb_tikvclient_batch_client_no_available_connection_total{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "No Available Connection Counter", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage batch processing unvailable durations", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 16}, "id": 180, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_tikvclient_batch_client_unavailable_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Batch Client Unavailable Duration 95", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "kv storage batch client wait new connection establish duration", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 16}, "id": 204, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tidb_tikvclient_batch_client_wait_connection_establish_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Wait Connection Establish Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Batch Client", "type": "row"}], "refresh": "30s", "schemaVersion": 18, "style": "dark", "tags": ["tidb-mixin", "tidb"], "templating": {"list": [{"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{status=\"active\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_name", "query": "label_values(dbaas_tenant_info{status=\"active\"},name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_name", "options": [{"selected": true, "text": "<PERSON><PERSON>-<PERSON>", "value": "<PERSON><PERSON>-<PERSON>"}], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "TiDB-Details", "uid": "000000011", "version": 1}