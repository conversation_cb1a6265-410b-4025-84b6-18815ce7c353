{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": ""}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "tidb-cluster", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": null, "iteration": 1577357354898, "links": [], "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 81, "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "tidb-cluster", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 2}, "id": 29, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "pd_tso_role{cluster_id=~\".*$cluster_id\", instance=\"$instance\", dc=\"global\"}", "format": "time_series", "instant": true, "intervalFactor": 2, "legendFormat": "", "metric": "pd_server_tso", "refId": "A", "step": 60}], "thresholds": "0,2", "title": "PD role", "type": "singlestat", "valueFontSize": "50%", "valueMaps": [{"op": "=", "text": "Follower", "value": "null"}, {"op": "=", "text": "Follower", "value": "0"}, {"op": "=", "text": "Leader", "value": "1"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "tidb-cluster", "decimals": null, "editable": true, "error": false, "format": "decbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": false}, "gridPos": {"h": 7, "w": 4, "x": 4, "y": 2}, "id": 27, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(77, 135, 25, 0.18)", "full": true, "lineColor": "rgb(21, 179, 65)", "show": true}, "tableColumn": "", "targets": [{"expr": "pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\",type=\"storage_capacity\"}", "format": "time_series", "intervalFactor": 2, "refId": "A", "step": 60}], "thresholds": "", "title": "Storage capacity", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "format": "decbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 4, "x": 8, "y": 2}, "hideTimeOverride": false, "id": 28, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\",type=\"storage_size\"}", "intervalFactor": 2, "refId": "A", "step": 60}], "thresholds": "", "title": "Current storage size", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "tidb-cluster", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 4, "x": 12, "y": 2}, "id": 30, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": true}, "tableColumn": "", "targets": [{"expr": "pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"leader_count\"}", "intervalFactor": 2, "refId": "A", "step": 60}], "thresholds": "", "title": "Number of Regions", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgb(255, 255, 255)", "rgba(255, 255, 255, 0.89)", "rgb(255, 255, 255)"], "datasource": "tidb-cluster", "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 7, "w": 4, "x": 16, "y": 2}, "id": 65, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"store_up_count\"})", "format": "time_series", "interval": "15s", "intervalFactor": 2, "refId": "A"}], "thresholds": "", "title": "Normal stores", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"columns": [{"text": "Current", "value": "current"}], "datasource": "tidb-cluster", "editable": true, "error": false, "fontSize": "100%", "gridPos": {"h": 7, "w": 4, "x": 20, "y": 2}, "hideTimeOverride": true, "id": 18, "links": [], "pageSize": null, "scroll": false, "showHeader": true, "sort": {"col": null, "desc": false}, "styles": [{"dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Metric", "sanitize": false, "type": "string"}, {"colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "decimals": 0, "pattern": "Current", "thresholds": ["1", "2"], "type": "number", "unit": "short"}], "targets": [{"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"store_disconnected_count\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "Disconnect Stores", "refId": "B", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"store_unhealth_count\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "Unhealth Stores", "refId": "C", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"store_low_space_count\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "LowSpace Stores", "refId": "D", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"store_down_count\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "Down Stores", "refId": "E", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"store_offline_count\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "Offline Stores", "refId": "F", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"store_tombstone_count\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "Tombstone Stores", "refId": "G", "step": 20}], "timeFrom": "1s", "title": "Abnormal stores", "transform": "timeseries_aggregations", "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 9}, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(grpc_server_handling_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=\"$instance\"}[5m])) by (grpc_method, le))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{grpc_method}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "99% completed cmds duration seconds", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 9}, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.98, sum(rate(pd_client_request_handle_requests_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (type, le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}-98%", "refId": "A", "step": 10}, {"expr": "avg(rate(pd_client_request_handle_requests_duration_seconds_sum{cluster_id=~\".*$cluster_id\"}[2m])) by (type) /  avg(rate(pd_client_request_handle_requests_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{type}}-average", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Handle requests duration seconds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 16}, "id": 66, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "pd_regions_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}, {"expr": "sum(pd_regions_status{cluster_id=~\".*$cluster_id\"}) by (instance, type)", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Region health", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 23}, "id": 68, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "pd_hotspot_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\",type=\"hot_write_region_as_leader\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "store-{{store}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Hot write Region's leader distribution", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 0, "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 23}, "id": 69, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "pd_hotspot_status{cluster_id=~\".*$cluster_id\", instance=\"$instance\",type=\"hot_read_region_as_leader\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "store-{{store}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Hot read Region's leader distribution", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 30}, "id": 33, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(delta(pd_scheduler_region_heartbeat{cluster_id=~\".*$cluster_id\", instance=\"$instance\", type=\"report\", status=\"ok\"}[2m])) by (store)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "store-{{store}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Region heartbeat report", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "opm", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 30}, "id": 67, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(pd_scheduler_region_heartbeat_latency_seconds_bucket{cluster_id=~\".*$cluster_id\"}[5m])) by (store, le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "store-{{store}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "99% Region heartbeat latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ms", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "PD", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 82, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 3}, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 1, "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_executor_statement_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Statement OPS", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 3}, "id": 34, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 3, "legendFormat": "99", "refId": "B", "step": 15}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 10}, "id": 35, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tidb_server_query_total{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} {{type}} {{result}}", "refId": "A", "step": 20}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPS By Instance", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 10}, "id": 72, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_server_execute_error_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": " {{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Failed Query OPM", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 17}, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": true, "steppedLine": true, "targets": [{"expr": "tidb_server_connections{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}, {"expr": "sum(tidb_server_connections{cluster_id=~\".*$cluster_id\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "total", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connection Count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 17}, "id": 36, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "process-{{instance}}", "refId": "A", "step": 10}, {"expr": "go_memstats_heap_inuse_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "legendFormat": "HeapInuse-{{instance}}", "format": "time_series", "intervalFactor": 2, "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 24}, "id": 70, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_session_transaction_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 24}, "id": 71, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95", "refId": "B"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "80", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 31}, "id": 37, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Cmd OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 31}, "id": 38, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Cmd Duration 99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 38}, "id": 39, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(pd_client_cmd_handle_cmds_duration_seconds_count{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "cmd", "refId": "A", "step": 10}, {"expr": "sum(rate(pd_client_request_handle_requests_duration_seconds_count{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "request", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD TSO OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 38}, "id": 40, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "999", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99", "refId": "B"}, {"expr": "histogram_quantile(0.90, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "90", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD TSO Wait Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 2, "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 45}, "id": 41, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_region_err_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tidb_server_session_execute_parse_duration_count", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TiClient Region Error OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 45}, "id": 42, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_lock_resolver_actions_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tidb_tikvclient_lock_resolver_actions_total{}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Lock Resolve OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 52}, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_domain_load_schema_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 52}, "id": 43, "legend": {"alignAsTable": true, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_tikvclient_backoff_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Backoff OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "TiDB", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 83, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "id": 20, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_raftstore_region_count{cluster_id=~\".*$cluster_id\", type=\"leader\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "tikv_raftstore_region_count", "refId": "A", "step": 10}, {"expr": "delta(tikv_raftstore_region_count{cluster_id=~\".*$cluster_id\", type=\"leader\"}[2m]) < -10", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "total", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "leader", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "id": 21, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_raftstore_region_count{cluster_id=~\".*$cluster_id\", type=\"region\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "region", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 75, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tikv\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 74, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"tikv\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 5, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "id": 44, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "store size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "id": 73, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "cf size", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 3, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_channel_full_total{cluster_id=~\".*$cluster_id\"}[2m])) by (instance, type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - {{type}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "channel full", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}, "id": 11, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_server_report_failure_msg_total{cluster_id=~\".*$cluster_id\"}[2m])) by (type,instance,store_id)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - {{type}} - to - {{store_id}}", "metric": "tikv_server_raft_store_msg_total", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "server report failures", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "id": 46, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_scheduler_contex_total{cluster_id=~\".*$cluster_id\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "scheduler pending commands", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}, "id": 51, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_coprocessor_executor_count{cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_coprocessor_request_error", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "coprocessor executor count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 5, "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 44}, "id": 47, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-99%", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-95%", "refId": "B", "step": 10}, {"expr": " sum(rate(tikv_coprocessor_request_duration_seconds_sum{cluster_id=~\".*$cluster_id\", req=\"select\"}[2m])) /  sum(rate(tikv_coprocessor_request_duration_seconds_count{cluster_id=~\".*$cluster_id\", req=\"select\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "select-avg", "refId": "C", "step": 10}, {"expr": " sum(rate(tikv_coprocessor_request_duration_seconds_sum{cluster_id=~\".*$cluster_id\", req=\"index\"}[2m])) /  sum(rate(tikv_coprocessor_request_duration_seconds_count{cluster_id=~\".*$cluster_id\", req=\"index\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "index-avg", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "coprocessor  request duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 44}, "id": 48, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", name=~\"raftstore_.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "raft store CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 52}, "id": 49, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", name=~\"cop_.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "tikv_coprocessor_request_duration_seconds_bucket{}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "TiKV", "type": "row"}], "refresh": "30s", "schemaVersion": 18, "style": "dark", "tags": ["tidb-mixin", "overview"], "templating": {"list": [{"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{status=\"active\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_name", "query": "label_values(dbaas_tenant_info{status=\"active\"},name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_name", "options": [{"selected": true, "text": "<PERSON><PERSON>-<PERSON>", "value": "<PERSON><PERSON>-<PERSON>"}], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "tidb-cluster", "definition": "", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "instance", "options": [], "query": "label_values(pd_cluster_status{cluster_id=~\".*$cluster_id\"}, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Overview", "uid": "eDbRZpnWk", "version": 1}