{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": ""}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "tidb-cluster", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": null, "iteration": 1568258597359, "links": [], "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2796, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "总 CPU 使用率", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 1708, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "客户端发来的常见请求的 QPS。如果同一类请求在多个机器上分布显著不平均，那么需要考虑热点的问题", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 1713, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_grpc_msg_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=~\"coprocessor|kv_get|kv_batch_get|kv_prewrite|kv_commit\"}[2m])) by (instance, type)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}} - {{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [3.6], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "", "frequency": "1m", "handler": 1, "message": "TiKV Storage ReadPool thread CPU usage is high", "name": "TiKV Storage ReadPool CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "点查会走 Storage ReadPool", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 1908, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"store_read_norm.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - normal", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"store_read_high.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - high", "metric": "tikv_thread_cpu_seconds_total", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"store_read_low.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - low", "metric": "tikv_thread_cpu_seconds_total", "refId": "C", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Storage ReadPool CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [7.2], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "", "frequency": "1m", "handler": 1, "message": "TiKV Coprocessor thread CPU usage is high", "name": "TiKV Coprocessor CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "非点查的 SQL 走 Coprocessor Pool", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 78, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"cop_normal.*\"}[2m])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - normal", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"cop_high.*\"}[2m])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - high", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"cop_low.*\"}[2m])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - low", "refId": "C", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [3.6], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "", "frequency": "1m", "handler": 1, "message": "TiKV gRPC poll thread CPU usage is high", "name": "TiKV gRPC poll CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "gRPC CPU 使用率，如果打满，需要调 TiKV 的 grpc-concurrency 参数", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 105, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"grpc.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "gRPC poll CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Hot Read", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 2797, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 2}, "id": 2763, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 2}, "id": 2764, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_grpc_msg_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type!=\"kv_gc\"}[2m])) by (instance,type)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}} - {{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 2765, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(node_disk_io_time_seconds_total{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - {{device}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "IO utilization", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [3.6], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "", "frequency": "1m", "handler": 1, "message": "TiKV gRPC poll thread CPU usage is high", "name": "TiKV gRPC poll CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 2783, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"grpc.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "gRPC poll CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [1.7], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "0", "frequency": "1m", "handler": 1, "message": "TiKV raft store thread CPU is high", "name": "TiKV raft store CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "Raftstore 线程 CPU 使用率，这个线程池用来写 Raft log。", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 61, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"raftstore_.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.85}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft store CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [1.8], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "", "frequency": "1m", "handler": 1, "message": "TiKV async apply thread CPU is high", "name": "TiKV async apply CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "Apply 线程 CPU，这个线程池用来将写入应用到 kv engine 中。", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 79, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"apply_[0-9]+\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Async apply CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [3.6], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "", "frequency": "1m", "handler": 1, "message": "TiKV Scheduler Worker thread CPU is high", "name": "TiKV Scheduler Worker CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Scheduler worker 线程池的 CPU 使用率。事务会在 Scheduler 中排队，这个线程池接近打满意味着 MVCC 的旧版本太多，事务处理过慢。", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 26}, "id": 63, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"sched_worker.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Scheduler Worker CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Hot Write", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 2798, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 3}, "id": 34, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_raftstore_region_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"leader\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}, {"expr": "delta(tikv_raftstore_region_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"leader\"}[2m]) < -10", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Leader", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Append log 时间。过长意味着 Raftstore 线程太忙，有可能心跳处理不过来导致上面的 Leader 被切走。", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 3}, "id": 2786, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999999, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": " 99.9999%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95%", "refId": "B"}, {"expr": "sum(rate(tikv_raftstore_append_log_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) / sum(rate(tikv_raftstore_append_log_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Append log duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Propose 如果在 Raftstore 中等得太久，则说明 Raftstore 线程很忙，或者被卡住了。", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 10}, "id": 2787, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999999, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} ", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Propose 99.9999% wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 10}, "id": 2789, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"write_max\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"write_percentile99\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"write_percentile95\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"write_average\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft RocksDB write duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "Raftstore 线程需要处理一些周期性任务。如果处理时间过长，则相当于 Raftstore 线程被卡住了。", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 17}, "id": 2829, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999999, sum(rate(tikv_raftstore_event_duration_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance, type))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{type}}", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 100, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft event 99.9999% handle duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Leader Drop", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 2799, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "TiKV 的 Raftstore 会在处理不过来消息时发生 channel full，此时后续的消息会被丢弃掉，可能会导致 TiDB 重试或者掉 Leader。", "editable": true, "error": false, "fill": 3, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "id": 22, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_channel_full_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance, type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - {{type}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Channel full", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "RocksDB write stall 说明写入请求被 compaction 或者其他流量控制措施卡住了。", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "id": 87, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\", type=\"write_stall_max\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\", type=\"write_stall_percentile99\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\", type=\"write_stall_percentile95\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\", type=\"write_stall_average\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft RocksDB write stall duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 39, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": " 99%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_raftstore_append_log_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) / sum(rate(tikv_raftstore_append_log_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Append log duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 40, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} ", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Append log duration per server", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 67}, "id": 2819, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_commit_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99%", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_commit_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "refId": "B"}, {"expr": "sum(rate(tikv_raftstore_commit_log_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) / sum(rate(tikv_raftstore_commit_log_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Commit log duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 67}, "id": 2821, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_commit_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Commit log duration per server", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Channel Full", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 2800, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 2777, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_scheduler_too_busy_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "scheduler-{{instance}}", "metric": "", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_channel_full_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance, type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "channelfull-{{instance}}-{{type}}", "metric": "", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_coprocessor_request_error{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type='full'}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "coprocessor-{{instance}}", "metric": "", "refId": "C", "step": 4}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_percentile99\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "stall-{{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Server is busy", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "height": "", "id": 193, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "maxPerRow": 2, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_scheduler_contex_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Scheduler pending commands", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 3, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 2779, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_channel_full_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (instance, type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - {{type}}", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Channel full", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 13}, "id": 2785, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_max\"}) by (db)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{db}} max", "metric": "", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_percentile99\"}) by (db)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{db}} 99%", "metric": "", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_percentile95\"}) by (db)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{db}} 95%", "metric": "", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_average\"}) by (db)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{db}} avg", "metric": "", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RocksDB write stall duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Server Is Busy", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 2801, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 38}, "id": 2768, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_grpc_msg_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type!=\"kv_gc\"}[2m])) by (le, type))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "99% gRPC message duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [3.6], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "0m", "frequency": "1m", "handler": 1, "message": "TiKV gRPC poll thread CPU is high", "name": "TiKV gRPC poll CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 38}, "id": 2782, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"grpc.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "gRPC poll CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [7.2], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "", "frequency": "1m", "handler": 1, "message": "TiKV Coprocessor thread CPU is high", "name": "TiKV Coprocessor CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 45}, "id": 2762, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"cop_normal.*\"}[2m])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - normal", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"cop_high.*\"}[2m])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - high", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"cop_low.*\"}[2m])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - low", "refId": "C", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"alert": {"conditions": [{"evaluator": {"params": [3.6], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "1m", "now"]}, "reducer": {"params": [], "type": "max"}, "type": "query"}], "executionErrorState": "alerting", "for": "", "frequency": "1m", "handler": 1, "message": "TiKV Storage ReadPool thread CPU is high", "name": "TiKV Storage ReadPool CPU alert", "noDataState": "ok", "notifications": []}, "aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 45}, "id": 2761, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"store_read_norm.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - normal", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"store_read_high.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - high", "metric": "tikv_thread_cpu_seconds_total", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"store_read_low.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - low", "metric": "tikv_thread_cpu_seconds_total", "refId": "C", "step": 4}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Storage ReadPool CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 52}, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-99.99%", "refId": "E"}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-95%", "refId": "B", "step": 4}, {"expr": " sum(rate(tikv_coprocessor_request_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (req) / sum(rate(tikv_coprocessor_request_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (req)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor request duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 52}, "id": 111, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-99.99%", "refId": "D"}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-95%", "refId": "B", "step": 4}, {"expr": " sum(rate(tikv_coprocessor_request_wait_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (req) / sum(rate(tikv_coprocessor_request_wait_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (req)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 59}, "id": 52, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, avg(rate(tikv_coprocessor_scan_keys_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, req))  ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-99.99%", "refId": "D"}, {"expr": "histogram_quantile(0.99, avg(rate(tikv_coprocessor_scan_keys_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, req))  ", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{req}}-99%", "metric": "tikv_coprocessor_scan_keys_bucket", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.95, avg(rate(tikv_coprocessor_scan_keys_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, req))  ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-95%", "metric": "tikv_coprocessor_scan_keys_bucket", "refId": "B", "step": 10}, {"expr": "histogram_quantile(0.90, avg(rate(tikv_coprocessor_scan_keys_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, req))  ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{req}}-90%", "metric": "tikv_coprocessor_scan_keys_bucket", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor scan keys", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 5, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 59}, "id": 116, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tikv_coprocessor_request_wait_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance,req))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}-{{req}}", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "95% Coprocessor wait duration by store", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 5, "description": "读请求执行前，都需要拿一个 snapshot", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 66}, "id": 2828, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999999, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"snapshot\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99.9999%", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_storage_engine_async_request_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"snapshot\"}[2m])) / sum(rate(tikv_storage_engine_async_request_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"snapshot\"}[2m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "average", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Get snapshot duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 66}, "id": 82, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_get_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"get_max\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_get_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"get_percentile99\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_get_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"get_percentile95\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_get_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"get_average\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RocksDB get duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 73}, "id": 125, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_seek_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"seek_max\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_seek_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"seek_percentile99\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_seek_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"seek_percentile95\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_seek_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"seek_average\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RocksDB seek duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 2, "description": "Disk seconds Read Latency.\n- Critical:\n  - Recommended performance value is < 10ms as avg value of the Avg Disk sec/Read,Write.\n  - Critical value of the Avg Disk sec/Read,Write is > 50ms, should not exceed this value.", "fill": 1, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 80}, "id": 2821, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(rate(node_disk_read_time_seconds_total{cluster_id=~\".*$cluster_id\"}[5m])/ rate(node_disk_reads_completed_total{cluster_id=~\".*$cluster_id\"}[5m])) > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}: [{{ device }}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Read Latency (ms)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 2, "description": "", "fill": 1, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 84}, "id": 2822, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_bytes_total{cluster_id=~\".*$cluster_id\"}[5m]) + irate(node_disk_written_bytes_total{cluster_id=~\".*$cluster_id\"}[5m]) > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}: [{{ device }}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Throughput", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Read Too Slow", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 2802, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 7}, "id": 2781, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_grpc_msg_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type!=\"kv_gc\"}[2m])) by (le, type, instance))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} {{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "99% gRPC message duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 7}, "id": 109, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write\"}[2m])) by (le, instance))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write\"}[2m])) by (le, instance))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "refId": "B", "step": 4}, {"expr": "rate(tikv_storage_engine_async_request_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write\"}[2m]) / rate(tikv_storage_engine_async_request_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Storage async write duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "id": 2753, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_scheduler_latch_wait_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"prewrite\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "metric": "", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_scheduler_latch_wait_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"prewrite\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "metric": "", "refId": "B", "step": 10}, {"expr": "rate(tikv_scheduler_latch_wait_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"prewrite\"}[2m]) / rate(tikv_scheduler_latch_wait_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"prewrite\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "metric": "", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Prewrite latch wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "id": 2774, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_scheduler_latch_wait_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"commit\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "metric": "", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_scheduler_latch_wait_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"commit\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "metric": "", "refId": "B", "step": 10}, {"expr": "rate(tikv_scheduler_latch_wait_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"commit\"}[2m]) / rate(tikv_scheduler_latch_wait_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"commit\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "metric": "", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Commit latch wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 21}, "id": 2788, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "refId": "B", "step": 4}, {"expr": "rate(tikv_raftstore_append_log_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]) / rate(tikv_raftstore_append_log_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Append log duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 21}, "id": 2790, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Append log duration per server", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}, "id": 2830, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_commit_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99%", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_commit_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "refId": "B"}, {"expr": "sum(rate(tikv_raftstore_commit_log_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) / sum(rate(tikv_raftstore_commit_log_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Commit log duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 28}, "id": 2831, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_commit_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Commit log duration per server", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 35}, "id": 31, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_apply_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "refId": "B", "step": 4}, {"expr": "rate(tikv_raftstore_apply_log_duration_seconds_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]) / rate(tikv_raftstore_apply_log_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Apply log duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 35}, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_log_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Apply log duration per server", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 42}, "id": 2794, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_request_wait_time_duration_secs_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "refId": "B", "step": 4}, {"expr": "rate(tikv_raftstore_request_wait_time_duration_secs_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]) / rate(tikv_raftstore_request_wait_time_duration_secs_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Propose wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 42}, "id": 2795, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_wait_time_duration_secs_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "metric": "", "refId": "A", "step": 4}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_apply_wait_time_duration_secs_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "refId": "B", "step": 4}, {"expr": "rate(tikv_raftstore_apply_wait_time_duration_secs_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]) / rate(tikv_raftstore_apply_wait_time_duration_secs_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m]) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "refId": "C", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Apply wait duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 49}, "id": 126, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"write_max\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"write_percentile99\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"write_percentile95\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\",type=\"write_average\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV RocksDB write duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 49}, "id": 2776, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"write_max\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} max", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"write_percentile99\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"write_percentile95\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_write_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"write_average\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft RocksDB write duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 56}, "id": 137, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tikv_engine_wal_file_synced{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} sync", "metric": "", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft RocksDB WAL sync operations", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 56}, "id": 135, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 2, "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tikv_engine_wal_file_sync_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"wal_file_sync_max\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} max", "refId": "A", "step": 10}, {"expr": "tikv_engine_wal_file_sync_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"wal_file_sync_percentile99\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "refId": "B", "step": 10}, {"expr": "tikv_engine_wal_file_sync_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"wal_file_sync_percentile95\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "refId": "C", "step": 10}, {"expr": "tikv_engine_wal_file_sync_micro_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"raft\",type=\"wal_file_sync_average\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft RocksDB WAL sync duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 63}, "id": 2793, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_max\"}) by (db)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{db}} max", "metric": "", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_percentile99\"}) by (db)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{db}} 99%", "metric": "", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_percentile95\"}) by (db)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{db}} 95%", "metric": "", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_average\"}) by (db)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{db}} avg", "metric": "", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RocksDB write stall duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 70}, "id": 2791, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(node_disk_io_time_seconds_total{cluster_id=~\".*$cluster_id\"}[2m]) > 0", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - {{device}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "IO utilization", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 74}, "id": 2818, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(node_disk_reads_completed_total{cluster_id=~\".*$cluster_id\"}[5m]) + rate(node_disk_writes_completed_total{cluster_id=~\".*$cluster_id\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} IOPs", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "IOPs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "DISK seconds Read/ Write Latency.\n- Critical:\n  - Recommended performance value is < 10ms as avg value of the Avg Disk sec/Read,Write.\n  - Critical value of the Avg Disk sec/Read,Write is > 50ms, should not exceed this value.", "fill": 1, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 78}, "id": 2820, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(rate(node_disk_write_time_seconds_total{cluster_id=~\".*$cluster_id\"}[5m])/ rate(node_disk_writes_completed_total{cluster_id=~\".*$cluster_id\"}[5m])) > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}: [{{ device }}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Write Latency (ms)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 2, "description": "", "fill": 1, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 82}, "id": 2819, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(node_disk_read_bytes_total{cluster_id=~\".*$cluster_id\"}[5m]) + irate(node_disk_written_bytes_total{cluster_id=~\".*$cluster_id\"}[5m]) > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}: [{{ device }}]", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Throughput", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Write Too Slow", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 2806, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 8}, "id": 2810, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"db": {"selected": false, "text": "kv", "value": "kv"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_num_files_at_level{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\", level=\"0\"}) by (instance, cf)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} {{cf}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Level0 SST file number", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 8}, "id": 2811, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"db": {"selected": false, "text": "kv", "value": "kv"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_num_immutable_mem_table{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"$db\"}) by (instance, cf)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} {{cf}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Immutable mem-table number", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "id": 2808, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"db": {"selected": false, "text": "kv", "value": "kv"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tikv_engine_pending_compaction_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"$db\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{instance}} {{cf}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pending compaction bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "id": 2812, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"db": {"selected": false, "text": "kv", "value": "kv"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_max\", db=\"$db\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} max", "metric": "", "refId": "A", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_percentile99\", db=\"$db\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 99%", "metric": "", "refId": "B", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_percentile95\", db=\"$db\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} 95%", "metric": "", "refId": "C", "step": 10}, {"expr": "avg(tikv_engine_write_stall{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", type=\"write_stall_average\", db=\"$db\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} avg", "metric": "", "refId": "D", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "RocksDB write stall duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "µs", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": "db", "title": "Write Stall - $db", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 2803, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 102, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 300, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_engine_block_cache_size_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", db=\"kv\"}) by(cf)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{cf}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Block cache size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 2770, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "OOM", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 2804, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 11}, "id": 1481, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_region_size_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99%", "metric": "", "refId": "B", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_raftstore_region_size_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "95%", "metric": "", "refId": "C", "step": 10}, {"expr": "sum(rate(tikv_raftstore_region_size_sum{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) / sum(rate(tikv_raftstore_region_size_count{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "avg", "metric": "", "refId": "D", "step": 10}, {"expr": "histogram_quantile(0.999999, sum(rate(tikv_raftstore_region_size_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$instance\"}[2m])) by (le))", "format": "time_series", "intervalFactor": 2, "legendFormat": "99.9999%", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Approximate Region size", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 11}, "id": 2792, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$instance\", name=~\"split_check\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Split checker CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Huge Region", "type": "row"}], "refresh": "1m", "schemaVersion": 18, "style": "dark", "tags": ["tidb-mixin", "tikv"], "templating": {"list": [{"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{status=\"active\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_name", "query": "label_values(dbaas_tenant_info{status=\"active\"},name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_name", "options": [{"selected": true, "text": "<PERSON><PERSON>-<PERSON>", "value": "<PERSON><PERSON>-<PERSON>"}], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "tidb-cluster", "definition": "", "hide": 0, "includeAll": true, "label": "db", "multi": true, "name": "db", "options": [], "query": "label_values(tikv_engine_block_cache_size_bytes{cluster_id=~\".*$cluster_id\"}, db)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "datasource": "tidb-cluster", "definition": "", "hide": 0, "includeAll": true, "label": "command", "multi": true, "name": "command", "options": [], "query": "label_values(tikv_storage_command_total{cluster_id=~\".*$cluster_id\"}, type)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "tidb-cluster", "definition": "", "hide": 0, "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": "label_values(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}, instance)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "TiKV-Trouble-Shooting", "uid": "Lg4wiEkZz", "version": 4}