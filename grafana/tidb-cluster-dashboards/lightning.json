{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": ""}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "singlestat", "name": "Singlestat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "hideControls": false, "id": null, "links": [], "refresh": false, "rows": [{"collapse": false, "height": "250px", "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 1, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tikv_import_write_chunk_bytes_sum{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "write from lightning", "refId": "B"}, {"expr": "sum(rate(tikv_import_upload_chunk_bytes_sum{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "upload to tikv", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Import speed", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "1/rate(lightning_chunks{cluster_id=~\".*$cluster_id\", state=\"finished\"}[2m]) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Chunk process duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Import Speed", "titleSize": "h6"}, {"collapse": false, "height": 250, "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#d44a3a", "rgba(237, 129, 40, 0.89)", "#299c46"], "datasource": "tidb-cluster", "decimals": null, "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": false}, "id": 4, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 3, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": true, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "lightning_chunks{cluster_id=~\".*$cluster_id\", state=\"finished\"} / ignoring(state) lightning_chunks{cluster_id=~\".*$cluster_id\", state=\"estimated\"}", "format": "time_series", "instant": false, "intervalFactor": 2, "refId": "A"}], "thresholds": "0,0", "title": "Import Progress", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#d44a3a", "rgba(237, 129, 40, 0.89)", "#299c46"], "datasource": "tidb-cluster", "format": "percentunit", "gauge": {"maxValue": 1, "minValue": 0, "show": true, "thresholdLabels": false, "thresholdMarkers": false}, "id": 12, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "span": 3, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "lightning_tables{cluster_id=~\".*$cluster_id\", state=\"completed\"} / ignoring(state) lightning_tables{cluster_id=~\".*$cluster_id\", state=\"pending\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "refId": "A"}], "thresholds": "0,0", "title": "Checksum progress", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"columns": [{"text": "Max", "value": "max"}], "datasource": "tidb-cluster", "fontSize": "100%", "id": 8, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "span": 6, "styles": [{"alias": "Time", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "Step", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "decimals": 0, "pattern": "Metric", "thresholds": ["1", "2"], "type": "number", "unit": "none"}, {"alias": "Tables", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "pattern": "Max", "thresholds": ["0", "0"], "type": "number", "unit": "none"}], "targets": [{"expr": "lightning_tables{cluster_id=~\".*$cluster_id\", result=\"failure\"}", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{state}}", "refId": "A"}], "title": "Failures", "transform": "timeseries_aggregations", "type": "table"}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Import Progress", "titleSize": "h6"}, {"collapse": false, "height": 250, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 7, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"importer\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "importer RSS", "refId": "A"}, {"expr": "go_memstats_heap_inuse_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb-lightning\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "lightning heap", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Memory usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 9, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "go_goroutines{cluster_id=~\".*$cluster_id\", component=\"tidb-lightning\"}", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Number of Lightning Goroutines", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 3, "legend": {"alignAsTable": false, "avg": true, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 4, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tidb-lightning\"}[2m])*100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Lightning", "refId": "A"}, {"expr": "rate(process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"importer\"}[2m])*100", "format": "time_series", "intervalFactor": 2, "legendFormat": "Importer", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "CPU%", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "percent", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Resource usage", "titleSize": "h6"}, {"collapse": false, "height": 250, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 5, "stack": false, "steppedLine": true, "targets": [{"expr": "lightning_idle_workers{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Idle workers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 5, "stack": false, "steppedLine": true, "targets": [{"expr": "lightning_kv_encoder{cluster_id=~\".*$cluster_id\", type=\"open\"} - ignoring(type) lightning_kv_encoder{cluster_id=~\".*$cluster_id\", type=\"closed\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "KV Encoder", "refId": "A"}, {"expr": "lightning_importer_engine{cluster_id=~\".*$cluster_id\", type=\"open\"} - ignoring(type) lightning_importer_engine{cluster_id=~\".*$cluster_id\", type=\"closed\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Importer Engines (via Lightning)", "refId": "B"}, {"expr": "tikv_import_rpc_duration_count{cluster_id=~\".*$cluster_id\", request=\"open_engine\",result=\"ok\"} - ignoring(request) tikv_import_rpc_duration_count{cluster_id=~\".*$cluster_id\", request=\"close_engine\",result=\"ok\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "Importer Engines (via Importer)", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "External resources", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"columns": [{"text": "Current", "value": "current"}], "datasource": "tidb-cluster", "fontSize": "100%", "id": 21, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "span": 2, "styles": [{"alias": "TiKV", "pattern": "Metric"}, {"alias": "", "colorMode": "cell", "colors": ["#E0B400", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "link": false, "mappingType": 2, "pattern": "Current", "rangeMaps": [{"from": "0", "text": "Import", "to": "0"}, {"from": "1", "text": "Normal", "to": "Infinity"}], "thresholds": ["1", "1"], "type": "string", "unit": "short"}], "targets": [{"expr": "min(tikv_config_rocksdb{cluster_id=~\".*$cluster_id\", name=\"hard_pending_compaction_bytes_limit\"}) by (instance)", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "title": "Import/Normal mode", "transform": "timeseries_aggregations", "type": "table"}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Resource usage", "titleSize": "h6"}, {"collapse": false, "height": 223, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 13, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(lightning_chunk_parser_read_block_seconds_sum{cluster_id=~\".*$cluster_id\"}[2m]) / rate(lightning_chunk_parser_read_block_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "read block", "refId": "A"}, {"expr": "rate(lightning_apply_worker_seconds_sum{cluster_id=~\".*$cluster_id\", name = \"io\"}[2m]) /rate(lightning_apply_worker_seconds_count{cluster_id=~\".*$cluster_id\", name = \"io\"}[2m]) ", "format": "time_series", "intervalFactor": 2, "legendFormat": "apply worker", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Chunk parser read block duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 15, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(lightning_row_encode_seconds_sum{cluster_id=~\".*$cluster_id\"}[2m]) / rate(lightning_row_encode_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "row encode", "refId": "A"}, {"expr": "rate(lightning_block_deliver_seconds_sum{cluster_id=~\".*$cluster_id\"}[2m]) / rate(lightning_block_deliver_seconds_count{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "block deliver", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "SQL process duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Dashboard Row", "titleSize": "h6"}, {"collapse": false, "height": 235, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(lightning_block_deliver_bytes_sum{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{kind}} deliver rate", "refId": "B"}, {"expr": "sum(rate(lightning_block_deliver_bytes_sum{cluster_id=~\".*$cluster_id\"}[2m]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "total deliver rate", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "SQL process rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 17, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "lightning_row_read_bytes_sum{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "parser read size", "refId": "A"}, {"expr": "lightning_block_deliver_bytes_sum{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{kind}} deliver size", "refId": "B"}, {"expr": "pd_cluster_status{cluster_id=~\".*$cluster_id\", type=\"storage_size\"} / ignoring(type) pd_config_status{cluster_id=~\".*$cluster_id\", type=\"max_replicas\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "storage_size / replicas", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Total bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Dashboard Row", "titleSize": "h6"}, {"collapse": false, "height": 243, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tikv_import_range_delivery_duration_sum{cluster_id=~\".*$cluster_id\"}[2m]) / rate(tikv_import_range_delivery_duration_count{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "range deliver", "refId": "A"}, {"expr": "rate(tikv_import_sst_delivery_duration_sum{cluster_id=~\".*$cluster_id\"}[2m]) / rate(tikv_import_sst_delivery_duration_count{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "SST file deliver", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Deliver duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "id": 19, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "SST size", "yaxis": 2}], "spaceLength": 10, "span": 6, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tikv_import_split_sst_duration_sum{cluster_id=~\".*$cluster_id\"}[2m]) / rate(tikv_import_split_sst_duration_count{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "Split SST", "refId": "C"}, {"expr": "rate(tikv_import_sst_upload_duration_sum{cluster_id=~\".*$cluster_id\"}[2m]) / rate(tikv_import_sst_upload_duration_count{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "SST upload", "refId": "D"}, {"expr": "rate(tikv_import_sst_ingest_duration_sum{cluster_id=~\".*$cluster_id\"}[2m]) / rate(tikv_import_sst_ingest_duration_count{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "SST ingest", "refId": "E"}, {"expr": "rate(tikv_import_sst_chunk_bytes_sum{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "SST size", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "SST process duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}]}], "repeat": null, "repeatIteration": null, "repeatRowId": null, "showTitle": false, "title": "Dashboard Row", "titleSize": "h6"}], "schemaVersion": 14, "style": "dark", "tags": ["tidb-mixin", "lightning"], "templating": {"list": [{"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{status=\"active\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_name", "query": "label_values(dbaas_tenant_info{status=\"active\"},name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_name", "options": [{"selected": true, "text": "<PERSON><PERSON>-<PERSON>", "value": "<PERSON><PERSON>-<PERSON>"}], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Lightning", "uid": "t5PjsKUGz", "version": 4}