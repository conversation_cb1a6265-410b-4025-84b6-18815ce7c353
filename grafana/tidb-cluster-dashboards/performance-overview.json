{"annotations": {"list": [{"builtIn": 1, "datasource": "tidb-cluster", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "iteration": 1647935011134, "links": [], "panels": [{"collapsed": false, "datasource": "tidb-cluster", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 138, "panels": [], "title": "Performance Overview", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Service Time Per Second, show service time distribution among different SQL types:\n1. Database time, the total time that the TiDB cluster is processing application requests.\n2. The service time of different SQL types.\n", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 1}, "hiddenSeries": false, "id": 189, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:601", "alias": "database time", "bars": false, "color": "#FADE2A", "lines": true, "linewidth": 2, "stack": false}, {"$$hashKey": "object:637", "alias": "Select", "color": "#5794F2"}, {"$$hashKey": "object:645", "alias": "Commit", "color": "#96D98D"}, {"$$hashKey": "object:655", "alias": "Insert", "color": "#73BF69"}, {"$$hashKey": "object:700", "alias": "general", "color": "#F2495C"}, {"$$hashKey": "object:2605", "alias": "Update", "color": "#37872D"}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_server_handle_query_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m]))", "hide": false, "interval": "", "legendFormat": "database time", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(tidb_server_handle_query_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m])) by (sql_type)", "hide": false, "interval": "", "legendFormat": "{{sql_type}}", "refId": "G"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Database Time by SQL Type", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3528", "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:3529", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Service Time Per Second, show service time distribution among different SQL phases.\n1. Database time, the total time that the TiDB cluster is processing application requests.\n2. The service time of different SQL phases.\n", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 1}, "hiddenSeries": false, "id": 187, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:205", "alias": "parse", "color": "#FF7383"}, {"$$hashKey": "object:216", "alias": "compile", "color": "#FF9830"}, {"$$hashKey": "object:227", "alias": "execute", "color": "#73BF69"}, {"$$hashKey": "object:364", "alias": "database time", "bars": false, "color": "#FADE2A", "lines": true, "linewidth": 2, "stack": false}, {"$$hashKey": "object:2194", "alias": "get token", "color": "#C4162A"}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_server_handle_query_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m]))", "hide": false, "interval": "", "legendFormat": "database time", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(tidb_session_parse_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m]))", "hide": false, "interval": "", "legendFormat": "parse", "refId": "D"}, {"exemplar": true, "expr": "sum(rate(tidb_session_compile_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m]))", "hide": false, "interval": "", "legendFormat": "compile", "refId": "E"}, {"exemplar": true, "expr": "sum(rate(tidb_session_execute_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m]))", "hide": false, "interval": "", "legendFormat": "execute", "refId": "F"}, {"exemplar": true, "expr": "sum(rate(tidb_server_get_token_duration_seconds_sum{cluster_id=~\".*$cluster_id\"}[2m]))/1000000", "hide": false, "instant": false, "interval": "", "legendFormat": "get token", "refId": "G"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Database Time by SQL Phase", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3528", "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:3529", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Service Time Per Second, show service time distribution among different KV/PD request:\n1. Execute time, the execute time in SQL Phase\n2. Service time  of different KV/PD request ", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 1}, "hiddenSeries": false, "id": 190, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:1113", "alias": "tso_wait", "color": "#701313"}, {"$$hashKey": "object:1127", "alias": "Commit", "color": "#37872D"}, {"$$hashKey": "object:1139", "alias": "Prewrite", "color": "#73BF69"}, {"$$hashKey": "object:1144", "alias": "PessimisticLock", "color": "#b40606"}, {"$$hashKey": "object:2301", "alias": "Get", "color": "#5794F2"}, {"$$hashKey": "object:2313", "alias": "BatchGet", "color": "#1F60C4"}, {"$$hashKey": "object:2321", "alias": "<PERSON><PERSON>", "color": "#C0D8FF"}, {"$$hashKey": "object:2673", "alias": "<PERSON><PERSON>", "color": "#8778ee"}, {"$$hashKey": "object:2828", "alias": "execute time", "bars": false, "color": "#FADE2A", "lines": true, "linewidth": 2, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_tikvclient_request_seconds_sum{ cluster_id=~\".*$cluster_id\", store!=\"0\"}[2m])) by (type)", "hide": false, "interval": "", "legendFormat": "{{type}}", "refId": "B"}, {"exemplar": true, "expr": "sum(rate(pd_client_cmd_handle_cmds_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", type=\"wait\"}[2m]))", "hide": false, "interval": "", "legendFormat": "tso_wait", "refId": "C"}, {"exemplar": true, "expr": "sum(rate(tidb_session_execute_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m]))", "hide": false, "interval": "", "legendFormat": "execute time", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "SQL Execute Time Overview", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3528", "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:3529", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB statement statistics.\nBold red line on right Y axis for Failed Queries per second", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 8}, "hiddenSeries": false, "id": 179, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:1069", "alias": "Failed", "color": "#C4162A", "linewidth": 2, "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_executor_statement_total{ cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}, {"exemplar": true, "expr": "sum(rate(tidb_executor_statement_total{ cluster_id=~\".*$cluster_id\"}[2m]))", "hide": false, "interval": "", "legendFormat": "Total", "refId": "B"}, {"exemplar": true, "expr": "sum(rate(tidb_server_execute_error_total{cluster_id=~\".*$cluster_id\"}[2m])) ", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Failed", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:822", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:823", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB command total statistics including both successful and failed ones", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 8}, "hiddenSeries": false, "id": 178, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_server_query_total{ cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPS By Type", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB plan cache hit total.", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 8}, "hiddenSeries": false, "id": 91, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_server_plan_cache_total{ cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "avg", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Queries Using Plan Cache OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3416", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:3417", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "kv/tso request total by command type", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "hiddenSeries": false, "id": 180, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_tikvclient_request_seconds_count{ cluster_id=~\".*$cluster_id\"}[2m])) by (type)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 30}, {"exemplar": true, "expr": "sum(rate(tidb_tikvclient_request_seconds_count{ cluster_id=~\".*$cluster_id\"}[2m]))", "hide": false, "interval": "", "legendFormat": "kv request total", "refId": "B"}, {"exemplar": true, "expr": "sum(rate(pd_client_cmd_handle_cmds_duration_seconds_count{ cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m]))", "hide": false, "interval": "", "legendFormat": "tso - cmd", "refId": "C"}, {"exemplar": true, "expr": "sum(rate(pd_client_request_handle_requests_duration_seconds_count{ cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m]))", "hide": false, "interval": "", "legendFormat": "tso - request", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV/TSO Request OPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB current connection counts", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "hiddenSeries": false, "id": 188, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "tidb_server_connections{ cluster_id=~\".*$cluster_id\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 30}, {"exemplar": true, "expr": "sum(tidb_server_connections{ cluster_id=~\".*$cluster_id\"})", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "total", "refId": "B"}, {"exemplar": true, "expr": "sum(rate(tidb_server_handle_query_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m]))", "hide": false, "interval": "", "legendFormat": "active connections", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connection Count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3472", "decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:3473", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "avg: average cpu usage for all instance.\ndelta: max(cpu utilization) - min(cpu utilization)\nmax: max(cpu utilization)", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 22}, "hiddenSeries": false, "id": 181, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "avg(rate(process_cpu_seconds_total{ cluster_id=~\".*$cluster_id\",component=\"tidb\"}[2m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "avg", "refId": "A", "step": 30}, {"exemplar": true, "expr": "(max(rate(process_cpu_seconds_total{ cluster_id=~\".*$cluster_id\", component=\"tidb\"}[2m])) - min(rate(process_cpu_seconds_total{ cluster_id=~\".*$cluster_id\", component=\"tidb\"}[2m])))", "hide": false, "interval": "", "legendFormat": "delta", "refId": "B"}, {"exemplar": true, "expr": "max(rate(process_cpu_seconds_total{ cluster_id=~\".*$cluster_id\",component=\"tidb\"}[2m]))", "hide": false, "interval": "", "legendFormat": "max", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TiDB CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "1. CPU: CPU utilization for all TiKV instances.\n- CPU-Avg: avg(cpu utilization)\n- CPU-Delta: max(cpu utilization) - min(cpu utilization)\n- CPU-Max: max(cpu utilization)\n2. IO MBps: The total bytes of read and write in all TiKV instances", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 22}, "hiddenSeries": false, "id": 182, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:371", "alias": "IO-Avg", "yaxis": 2}, {"$$hashKey": "object:563", "alias": "IO-Max", "yaxis": 2}, {"$$hashKey": "object:576", "alias": "IO-Delta", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "avg(rate(process_cpu_seconds_total{ cluster_id=~\".*$cluster_id\", job=~\".*tikv\"}[2m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "CPU-Avg", "refId": "A", "step": 30}, {"exemplar": true, "expr": "(max(rate(process_cpu_seconds_total{ cluster_id=~\".*$cluster_id\", job=~\".*tikv\"}[2m])) - min(rate(process_cpu_seconds_total{ cluster_id=~\".*$cluster_id\", job=~\".*tikv\"}[2m])))", "hide": false, "interval": "", "legendFormat": "CPU-Delta", "refId": "B"}, {"exemplar": true, "expr": "max(rate(process_cpu_seconds_total{ cluster_id=~\".*$cluster_id\", job=~\".*tikv\"}[2m]))", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "CPU-Max", "refId": "C"}, {"exemplar": true, "expr": "avg(sum(rate(tikv_engine_flow_bytes{cluster_id=~\".*$cluster_id\", db=\"kv\", type=~\"wal_file_bytes|bytes_read|iter_bytes_read\"}[2m])) by (instance))", "hide": false, "instant": false, "interval": "", "legendFormat": "IO-Avg", "refId": "D"}, {"exemplar": true, "expr": "max(sum(rate(tikv_engine_flow_bytes{cluster_id=~\".*$cluster_id\", db=\"kv\", type=~\"wal_file_bytes|bytes_read|iter_bytes_read\"}[2m])) by (instance))", "hide": false, "interval": "", "legendFormat": "IO-Max", "refId": "E"}, {"exemplar": true, "expr": "max(sum(rate(tikv_engine_flow_bytes{cluster_id=~\".*$cluster_id\", db=\"kv\", type=~\"wal_file_bytes|bytes_read|iter_bytes_read\"}[2m])) by (instance)) - min(sum(rate(tikv_engine_flow_bytes{cluster_id=~\".*$cluster_id\", db=\"kv\", type=~\"wal_file_bytes|bytes_read|iter_bytes_read\"}[2m])) by (instance))", "hide": false, "interval": "", "legendFormat": "IO-Delta", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TiKV CPU/IO MBps", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:208", "decimals": null, "format": "percentunit", "label": "CPU (%)", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:209", "decimals": null, "format": "Bps", "label": "MBps ", "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB query durations", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 29}, "hiddenSeries": false, "id": 80, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_server_handle_query_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m])) / sum(rate(tidb_server_handle_query_duration_seconds_count{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "avg", "refId": "D"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m])) by (le))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "99", "refId": "B"}, {"exemplar": true, "expr": "sum(rate(tidb_server_handle_query_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m])) by (sql_type) / sum(rate(tidb_server_handle_query_duration_seconds_count{ cluster_id=~\".*$cluster_id\", sql_type!=\"internal\"}[2m])) by (sql_type)", "hide": false, "interval": "", "legendFormat": "avg-{{sql_type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3308", "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:3309", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB connection idle durations", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 29}, "hiddenSeries": false, "id": 171, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(sum(rate(tidb_server_conn_idle_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", in_txn='1'}[2m])) / sum(rate(tidb_server_conn_idle_duration_seconds_count{ cluster_id=~\".*$cluster_id\", in_txn='1'}[2m])))", "hide": false, "interval": "", "legendFormat": "avg-in-txn", "refId": "C"}, {"exemplar": true, "expr": "(sum(rate(tidb_server_conn_idle_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", in_txn='0'}[2m])) / sum(rate(tidb_server_conn_idle_duration_seconds_count{ cluster_id=~\".*$cluster_id\", in_txn='0'}[2m])))", "format": "time_series", "instant": false, "interval": "", "intervalFactor": 2, "legendFormat": "avg-not-in-txn", "refId": "A", "step": 30}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tidb_server_conn_idle_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", in_txn='1'}[2m])) by (le,in_txn))", "hide": false, "interval": "", "legendFormat": "99-in-txn", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tidb_server_conn_idle_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", in_txn='0'}[2m])) by (le,in_txn))", "hide": false, "interval": "", "legendFormat": "99-not-in-txn", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connection Idle Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3364", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:3365", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time cost of parsing SQL to AST", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 36}, "hiddenSeries": false, "id": 156, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_session_parse_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])) by (le))", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "95", "refId": "B"}, {"exemplar": true, "expr": "(sum(rate(tidb_session_parse_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])) / sum(rate(tidb_session_parse_duration_seconds_count{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "C"}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_session_parse_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])) by (le))", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "99", "refId": "D", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Parse Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1955", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:1956", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The time to build plan", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 36}, "hiddenSeries": false, "id": 170, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(sum(rate(tidb_session_compile_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])) / sum(rate(tidb_session_compile_duration_seconds_count{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "D"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tidb_session_compile_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])) by (le))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "99", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Compile Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:283", "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:284", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time cost of executing the SQL which does not include the time to get the results of the query .", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 36}, "hiddenSeries": false, "id": 169, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(tidb_session_execute_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])) by (le))", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "95", "refId": "B"}, {"exemplar": true, "expr": "(sum(rate(tidb_session_execute_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])) / sum(rate(tidb_session_execute_duration_seconds_count{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "C"}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_session_execute_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", sql_type=\"general\"}[2m])) by (le))", "format": "time_series", "instant": false, "intervalFactor": 2, "legendFormat": "99", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Execute Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2109", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2110", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "tidb avg kv request duration", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 44}, "hiddenSeries": false, "id": 172, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_tikvclient_request_seconds_sum{ cluster_id=~\".*$cluster_id\", store!=\"0\"}[2m])) by (type)/ sum(rate(tidb_tikvclient_request_seconds_count{ cluster_id=~\".*$cluster_id\", store!=\"0\"}[2m])) by (type)", "hide": false, "interval": "", "legendFormat": "{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Avg TiDB KV Request Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2161", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2162", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "tikv grpc avg duration", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 44}, "hiddenSeries": false, "id": 173, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tikv_grpc_msg_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", store!=\"0\"}[2m])) by (type)/ sum(rate(tikv_grpc_msg_duration_seconds_count{ cluster_id=~\".*$cluster_id\", store!=\"0\"}[2m])) by (type)", "hide": false, "interval": "", "legendFormat": "{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Avg TiKV GRPC Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2263", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2264", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "- TSO Wait Duration: The duration of a client starting to wait for the TS until received the TS result.\n- TSO RPC Duration: The duration of a client sending TSO request until received the response.", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 44}, "hiddenSeries": false, "id": 77, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(sum(rate(pd_client_cmd_handle_cmds_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", type=\"wait\"}[2m])) / sum(rate(pd_client_cmd_handle_cmds_duration_seconds_count{ cluster_id=~\".*$cluster_id\", type=\"wait\"}[2m])))", "hide": false, "interval": "", "legendFormat": "wait - avg", "refId": "D"}, {"exemplar": true, "expr": "(sum(rate(pd_client_request_handle_requests_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])) / sum(rate(pd_client_request_handle_requests_duration_seconds_count{ cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])))", "hide": false, "interval": "", "legendFormat": "rpc - avg", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(pd_client_cmd_handle_cmds_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", type=\"wait\"}[2m])) by (le))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "wait - 99", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(pd_client_request_handle_requests_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", type=\"tso\"}[2m])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "rpc - 99", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD TSO Wait/RPC Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2315", "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2316", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time consumed by processing asynchronous write requests.\nStorage async write duration = store duration + apply duration", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 52}, "hiddenSeries": false, "id": 185, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tikv_storage_engine_async_request_duration_seconds_sum{ cluster_id=~\".*$cluster_id\", type=\"write\"}[2m])) / sum(rate(tikv_storage_engine_async_request_duration_seconds_count{ cluster_id=~\".*$cluster_id\", type=\"write\"}[2m]))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tikv_storage_engine_async_request_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\", type=\"write\"}[2m])) by (le))", "hide": false, "interval": "", "legendFormat": "99", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Storage Async Write Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2371", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2372", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The store time duration of each request", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 52}, "hiddenSeries": false, "id": 183, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tikv_raftstore_store_duration_secs_sum{ cluster_id=~\".*$cluster_id\"}[2m])) / sum(rate(tikv_raftstore_store_duration_secs_count{ cluster_id=~\".*$cluster_id\"}[2m]))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_store_duration_secs_bucket{ cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "hide": false, "interval": "", "legendFormat": "99", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Store Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2520", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2521", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The apply time duration of each request", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 52}, "hiddenSeries": false, "id": 174, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(sum(rate(tikv_raftstore_apply_duration_secs_sum{ cluster_id=~\".*$cluster_id\"}[2m])) / sum(rate(tikv_raftstore_apply_duration_secs_count{ cluster_id=~\".*$cluster_id\"}[2m])))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_duration_secs_bucket{ cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "hide": false, "interval": "", "legendFormat": "99", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Apply Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2572", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2573", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time consumed when <PERSON><PERSON> appends log", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 60}, "hiddenSeries": false, "id": 176, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(sum(rate(tikv_raftstore_append_log_duration_seconds_sum{ cluster_id=~\".*$cluster_id\"}[2m])) / sum(rate(tikv_raftstore_append_log_duration_seconds_count{ cluster_id=~\".*$cluster_id\"}[2m])))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_append_log_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "hide": false, "interval": "", "legendFormat": "99", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Append Log Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2672", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2673", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time consumed when <PERSON><PERSON> commits log", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 60}, "hiddenSeries": false, "id": 177, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(sum(rate(tikv_raftstore_commit_log_duration_seconds_sum{ cluster_id=~\".*$cluster_id\"}[2m])) / sum(rate(tikv_raftstore_commit_log_duration_seconds_count{ cluster_id=~\".*$cluster_id\"}[2m])))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_commit_log_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "hide": false, "interval": "", "legendFormat": "99", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Commit Log Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2724", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2725", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "The time consumed for Raft to apply logs", "editable": true, "error": false, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 60}, "hiddenSeries": false, "id": 186, "interval": "", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(sum(rate(tikv_raftstore_apply_log_duration_seconds_sum{ cluster_id=~\".*$cluster_id\"}[2m])) / sum(rate(tikv_raftstore_apply_log_duration_seconds_count{ cluster_id=~\".*$cluster_id\"}[2m])))", "hide": false, "interval": "", "legendFormat": "avg", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_apply_log_duration_seconds_bucket{ cluster_id=~\".*$cluster_id\"}[2m])) by (le))", "hide": false, "interval": "", "legendFormat": "99", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Apply Log Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2776", "decimals": null, "format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:2777", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "30s", "schemaVersion": 30, "style": "dark", "tags": ["tidb-mixin", "performance-overview"], "templating": {"list": [{"allValue": null, "current": {}, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{status=\"active\"},name)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_name", "options": [], "query": {"query": "label_values(dbaas_tenant_info{status=\"active\"},name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": null, "current": {}, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "options": [], "query": {"query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": null, "current": {}, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_name", "options": [], "query": {"query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": null, "current": {}, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "", "multi": false, "name": "cluster_id", "options": [], "query": {"query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "2m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Performance-Overview", "uid": "eDbRZpnWa2", "version": 2}