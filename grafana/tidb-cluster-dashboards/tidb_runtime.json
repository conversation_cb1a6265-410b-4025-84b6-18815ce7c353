{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "6.1.6"}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "tidb-cluster", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": null, "links": [], "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [{"aliasColors": {}, "bars": true, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB process rss memory usage. TiDB heap memory size in use ", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "id": 4, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sideWidth": null, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "seriesOverrides": [{"alias": "alloc-from-os", "fill": 3, "lines": true, "stack": false}, {"alias": "gc-threshold", "bars": false, "color": "#C4162A", "lines": true, "linewidth": 2, "stack": false}, {"alias": "gc", "bars": false, "color": "#C4162A", "hideTooltip": true, "legend": false, "pointradius": 3, "points": true, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "alloc-from-os", "refId": "A"}, {"expr": "go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} / (1 + tidb_server_gogc{cluster_id=~\".*$cluster_id\", component=\"tidb\"} / 100)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "estimate-inuse", "refId": "H"}, {"expr": "go_memstats_heap_alloc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} - go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} / (1 + tidb_server_gogc{cluster_id=~\".*$cluster_id\", component=\"tidb\"} / 100)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "estimate-garbage", "refId": "C"}, {"expr": "go_memstats_heap_idle_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} - go_memstats_heap_released_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} + go_memstats_heap_inuse_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} - go_memstats_heap_alloc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "reserved-by-go", "refId": "B"}, {"expr": "go_memstats_stack_sys_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} + go_memstats_mspan_sys_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} + go_memstats_mcache_sys_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} + go_memstats_buck_hash_sys_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} + go_memstats_gc_sys_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"} + go_memstats_other_sys_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "used-by-go", "refId": "D"}, {"expr": "go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "gc-threshold", "refId": "E"}, {"expr": "(clamp_max(idelta(go_memstats_last_gc_time_seconds{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[1m]), 1) * go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}) > 0", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "gc", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB cpu usage calculated with process cpu running seconds", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 8}, "id": 6, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}, {"alias": "/limit/", "color": "#C4162A", "fill": 0, "nullPointMode": "null"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "cpu-usage", "refId": "A", "step": 40}, {"expr": "(idelta((go_memstats_gc_cpu_fraction{cluster_id=~\".*$cluster_id\", component=\"tidb\"} * (go_memstats_last_gc_time_seconds{cluster_id=~\".*$cluster_id\", component=\"tidb\"} - process_start_time_seconds{cluster_id=~\".*$cluster_id\", component=\"tidb\"}) * tidb_server_maxprocs{cluster_id=~\".*$cluster_id\", component=\"tidb\"})[30s:]) > 0) / 15", "format": "time_series", "intervalFactor": 1, "legendFormat": "gc-cpu", "refId": "C"}, {"expr": "tidb_server_maxprocs{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "limit", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Count of live objects.", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 21, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sideWidth": null, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_memstats_heap_objects{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "objects", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Estimated Live Objects", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB process Go garbage collection STW pause duration", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 8}, "id": 8, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_gc_duration_seconds{cluster_id=~\".*$cluster_id\", component=\"tidb\", quantile=\"0\"}", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "min", "refId": "A", "step": 40}, {"expr": "go_gc_duration_seconds{cluster_id=~\".*$cluster_id\", component=\"tidb\", quantile!~\"0|1\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{quantile}}", "refId": "B"}, {"expr": "go_gc_duration_seconds{cluster_id=~\".*$cluster_id\", component=\"tidb\", quantile=\"1\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "max", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC STW Duration (last 256 GC cycles)", "tooltip": {"msResolution": false, "shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The throughput of <PERSON>'s memory allocator.", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 22}, "id": 16, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "sweep", "transform": "negative-Y"}, {"alias": "alloc-ops", "yaxis": 2}, {"alias": "swepp-ops", "transform": "negative-Y", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(go_memstats_alloc_bytes_total{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])", "format": "time_series", "intervalFactor": 1, "legendFormat": "alloc", "refId": "A"}, {"expr": "irate((go_memstats_alloc_bytes_total{cluster_id=~\".*$cluster_id\", component=\"tidb\"} - go_memstats_heap_alloc_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"})[30s:])", "format": "time_series", "intervalFactor": 1, "legendFormat": "sweep", "refId": "B"}, {"expr": "irate(go_memstats_mallocs_total{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])", "format": "time_series", "intervalFactor": 1, "legendFormat": "alloc-ops", "refId": "C"}, {"expr": "irate(go_memstats_frees_total{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])", "format": "time_series", "intervalFactor": 1, "legendFormat": "swepp-ops", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Allocator Throughput", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": true, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB process current goroutines count", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "id": 12, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "threads", "fill": 0, "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": " go_goroutines{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "goroutines", "refId": "A"}, {"expr": "go_threads{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "threads", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Goroutine Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB side and TiKV side RPC duration.", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 29}, "id": 19, "legend": {"avg": false, "current": false, "hideEmpty": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_request_seconds_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\", store!=\"0\",instance=~\"$instance\"}[30s])) by (le, store))", "format": "time_series", "intervalFactor": 1, "legendFormat": "tidb-to-store{{store}}", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_grpc_msg_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\", type!=\"kv_gc\"}[30s])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "tikv-{{instance}}-side", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV RPC Latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB side and PD side TSO RPC duration.", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 29}, "id": 18, "legend": {"avg": false, "current": false, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(pd_client_request_handle_requests_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\", type=\"tso\"}[30s])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "tidb-side", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(pd_server_handle_tso_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le))", "format": "time_series", "intervalFactor": 1, "legendFormat": "pd-side", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TSO RPC Latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Number of requests in each batch", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 29}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_batch_requests_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "99", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Requests Batch Size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Number of requests in queue", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 29}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_batch_pending_requests_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le))", "format": "heatmap", "interval": "", "intervalFactor": 1, "legendFormat": "99", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pending Requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The time spend on enqueue request", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 43}, "id": 26, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_tikvclient_batch_wait_duration_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "999", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_batch_wait_duration_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "B", "step": 10}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_tikvclient_batch_wait_duration_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "95", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request Enqueue Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ns", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Time spend on enqueue batch into gRPC", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 43}, "id": 28, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tidb_tikvclient_batch_send_latency_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "9999", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.999, sum(rate(tidb_tikvclient_batch_send_latency_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "999", "refId": "B", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_batch_send_latency_bucket{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[30s])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "99", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Batch Enqueue Duration", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ns", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": "instance", "title": "$instance", "type": "row"}], "refresh": "30s", "schemaVersion": 18, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{},cluster_id)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": "label_values(dbaas_tidb_cluster_info{},cluster_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Test-Cluster-TiDB-Runtime", "uid": "000000013", "version": 1}