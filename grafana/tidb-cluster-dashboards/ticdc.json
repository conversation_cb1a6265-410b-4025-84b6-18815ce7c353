{"__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": ""}, {"type": "panel", "id": "graph", "name": "Graph", "version": ""}, {"type": "panel", "id": "heatmap", "name": "Heatmap", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": false, "expr": "", "hide": true, "iconColor": "#F2495C", "limit": 100, "name": "", "showIn": 0, "tagKeys": "", "textFormat": "", "titleFormat": "", "type": "dashboard", "useValueForTime": false}, {"datasource": "tidb-cluster", "enable": true, "expr": "max(ticdc_processor_checkpoint_ts_lag) by (changefeed, capture) > BOOL $spike_threshold", "hide": true, "iconColor": "#F2495C", "limit": 100, "name": "Latency spike", "showIn": 0, "tagKeys": "changefeed", "tags": [], "titleFormat": "Latency spike", "type": "tags", "useValueForTime": false}, {"datasource": "tidb-cluster", "enable": false, "expr": "delta(up{cluster_id=~\".*$cluster_id\", component=~\"tikv|ticdc\"}[2m]) < BOOL 0", "hide": false, "iconColor": "#FF9830", "limit": 100, "name": "Server down", "showIn": 0, "step": "15s", "tagKeys": "instance,job", "tags": [], "textFormat": "", "titleFormat": "Down", "type": "tags"}, {"datasource": "tidb-cluster", "enable": false, "expr": "sum(ALERTS{cluster_id=~\".*$cluster_id\", alertstate=\"firing\", alertname=~\"ticdc.*\"}) by (alertname) > BOOL 0", "hide": false, "iconColor": "#B877D9", "limit": 100, "name": "All TiCDC alerts", "showIn": 0, "tagKeys": "alertname", "tags": [], "titleFormat": "Alert <PERSON>", "type": "tags"}, {"datasource": "tidb-cluster", "enable": false, "expr": "delta(tikv_cdc_region_resolve_status{status=\"resolved\"}[2m]) < BOOL -800", "hide": false, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "Resolved region drop", "showIn": 0, "step": "15s", "tagKeys": "instance", "tags": [], "titleFormat": "Resolved region drop", "type": "tags"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "id": null, "iteration": 1628762880809, "links": [], "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 21, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Uptime of TiCDC and TiKV", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 1}, "hiddenSeries": false, "id": 19, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(time() - process_start_time_seconds{cluster_id=~\".*$cluster_id\", component=\"ticdc\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "TiCDC - {{instance}}", "refId": "A"}, {"expr": "(time() - process_start_time_seconds{cluster_id=~\".*$cluster_id\", component=\"tikv\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "TiKV - {{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Uptime", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "dtdurations", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Goroutine count of TiCDC", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 8, "y": 1}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": " go_goroutines{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}, {"expr": "go_threads{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "threads-{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Goroutine count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The count of open FD count of TiCDC", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 14, "y": 1}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_open_fds{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Open FD count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"columns": [{"text": "Current", "value": "current"}], "datasource": "tidb-cluster", "description": "TiCDC cluster ownership status", "fontSize": "100%", "gridPos": {"h": 7, "w": 4, "x": 20, "y": 1}, "hideTimeOverride": true, "id": 113, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 2, "desc": false}, "styles": [{"alias": "Instance", "colorMode": null, "colors": ["#8AB8FF", "#73BF69", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "mappingType": 2, "pattern": "Current", "preserveFormat": false, "rangeMaps": [{"from": "0.1", "text": "Owner", "to": "2"}, {"from": "0", "text": "Worker", "to": "0.1"}], "sanitize": false, "thresholds": ["0.1", "2"], "type": "string", "unit": "short", "valueMaps": [{"text": "Owner", "value": "1"}]}], "targets": [{"expr": "rate(ticdc_owner_ownership_counter{cluster_id=~\".*$cluster_id\"}[2m])", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": "1s", "title": "Ownership", "transform": "timeseries_aggregations", "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "CPU usage of TiCDC", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 8}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}[2m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Memory usage of TiCDC", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 8}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "process-{{instance}}", "refId": "A"}, {"expr": "go_memstats_heap_alloc_bytes{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "heap-{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The history of TiCDC cluster ownership, owner node has a value that is great than 0", "fill": 1, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 8}, "id": 110, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"expr": "sum(rate(ticdc_owner_ownership_counter{cluster_id=~\".*$cluster_id\"}[2m])) by (instance) > BOOL 0.5", "format": "time_series", "interval": "30s", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Ownership history", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 8}, "hiddenSeries": false, "id": 114, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(ticdc_server_etcd_health_check_duration_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le,instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "p999-{{instance}}", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(ticdc_server_etcd_health_check_duration_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le,instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "p99-{{instance}}", "refId": "B"}, {"expr": "histogram_quantile(0.95, sum(rate(ticdc_server_etcd_health_check_duration_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le,instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "p95-{{instance}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Etcd health check duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Server", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 11, "panels": [{"columns": [{"text": "Current", "value": "current"}], "datasource": "tidb-cluster", "description": "The number of captured table of TiCDC nodes ", "fontSize": "100%", "gridPos": {"h": 5, "w": 7, "x": 0, "y": 2}, "id": 4, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "Time", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "sum(ticdc_processor_num_of_tables{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Changefeed table count", "transform": "timeseries_aggregations", "type": "table"}, {"columns": [{"text": "Current", "value": "current"}], "datasource": "tidb-cluster", "description": "Internal resolved ts of TiCDC nodes", "fontSize": "100%", "gridPos": {"h": 10, "w": 7, "x": 7, "y": 2}, "id": 90, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "table", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Metric", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "resolved ts", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "MM-DD HH:mm:ss.SSS", "decimals": 2, "pattern": "Current", "thresholds": [], "type": "date", "unit": "short"}], "targets": [{"expr": "max(ticdc_processor_resolved_ts{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture,changefeed)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}--{{changefeed}}", "refId": "A"}, {"expr": "max(ticdc_processor_checkpoint_ts{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture,changefeed) > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "checkpoint-{{capture}}--{{changefeed}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Processor resolved ts", "transform": "timeseries_aggregations", "type": "table"}, {"columns": [{"text": "Current", "value": "current"}], "datasource": "tidb-cluster", "description": "Internal resolved ts of captured tables", "fontSize": "100%", "gridPos": {"h": 10, "w": 10, "x": 14, "y": 2}, "id": 30, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "table", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Metric", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "resolved ts", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "MM-DD HH:mm:ss.SSS", "decimals": 2, "pattern": "Current", "thresholds": [], "type": "date", "unit": "short"}], "targets": [{"expr": "max(ticdc_processor_table_resolved_ts{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture,changefeed,table)", "format": "time_series", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}-{{changefeed}}-{{table}}", "refId": "A"}, {"expr": "max(ticdc_processor_checkpoint_ts{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture,changefeed,table) > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "checkpoint-{{changefeed}}", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Table resolved ts", "transform": "timeseries_aggregations", "type": "table"}, {"columns": [{"text": "Current", "value": "current"}], "datasource": "tidb-cluster", "description": "The number of replicated tables maintained in owner", "fontSize": "100%", "gridPos": {"h": 5, "w": 7, "x": 0, "y": 7}, "id": 138, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": null, "desc": false}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "sum(ticdc_owner_maintain_table_num{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\",type=\"total\"}) by (capture)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}-total", "refId": "A"}, {"expr": "sum(ticdc_owner_maintain_table_num{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\",type=\"wip\"}) by (capture)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}-wip", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Table count maintained by owner", "transform": "timeseries_aggregations", "type": "table"}, {"aliasColors": {}, "bars": true, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The checkpoint ts of changefeeds.", "fill": 0, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 12}, "id": 86, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "sideWidth": null, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/approximate current time.*/", "bars": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(ticdc_owner_checkpoint_ts{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}) by (changefeed) > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{changefeed}}", "refId": "A"}, {"expr": "max(pd_cluster_tso{cluster_id=~\".*$cluster_id\"}) * 1000", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "approximate current time (s)", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changefeed checkpoint", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "max": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "dateTimeAsIso", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Request count of etcd operation per second", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 9, "y": 12}, "hiddenSeries": false, "id": 102, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_etcd_request_count{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (capture, type)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}-{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PD etcd requests/s", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of errors that interrupt changefeed per minute ", "fill": 1, "gridPos": {"h": 7, "w": 7, "x": 17, "y": 12}, "id": 82, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(delta(ticdc_processor_exit_with_error_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Exit error count/m", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The lag between changefeed checkpoint ts and the latest ts of upstream TiDB.", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 19}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(ticdc_owner_checkpoint_ts_lag{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}) by (changefeed)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{changefeed}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Changefeed checkpoint lag", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The lag between internal resolved ts and the latest ts of upstream TiDB.", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 19}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(ticdc_processor_resolved_ts_lag{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture,changefeed)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}--{{changefeed}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Processor resolved ts lag", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The status of each changefeed.\n\n0: Normal\n\n1: Error\n\n2: Failed\n\n3: Stopped\n\n4: Finished\n\n-1: Unknown", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 26}, "id": 163, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 1, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "ticdc_owner_status{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{changefeed}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "The status of changefeeds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Percentiles of sink write duration of changefeeds", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 26}, "hiddenSeries": false, "id": 35, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(ticdc_sink_txn_exec_duration_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (le,instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-p95", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(ticdc_sink_txn_exec_duration_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (le,instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-p99", "refId": "B"}, {"expr": "histogram_quantile(0.999, sum(rate(ticdc_sink_txn_exec_duration_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (le,instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-p999", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sink write duration percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "max": null, "min": 1, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Sink write duration of changefeeds", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 33}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 94, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "repeat": null, "repeatDirection": "h", "reverseYBuckets": false, "targets": [{"expr": "max(rate(ticdc_sink_txn_exec_duration_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "Sink write duration", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Percentiles of sink batch size", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.90, sum(rate(ticdc_sink_txn_batch_size_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,capture))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-p90", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(ticdc_sink_txn_batch_size_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,capture))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-p99", "refId": "B"}, {"expr": "histogram_quantile(0.999, sum(rate(ticdc_sink_txn_batch_size_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,capture))", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{capture}}-p999", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sink write batch size percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of changed rows that are written to  downstream per second", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 40}, "hiddenSeries": false, "id": 34, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum (rate(ticdc_sink_txn_batch_size_sum{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}", "refId": "A"}, {"expr": "sum (rate(ticdc_sink_txn_batch_size_sum{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (changefeed)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "total", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sink write rows count/s", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Percentiles of asynchronous flush sink duration of changefeeds", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 40}, "hiddenSeries": false, "id": 98, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95, sum(rate(ticdc_processor_flush_event_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (le,instance,type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-{{type}}-p95", "refId": "A"}, {"expr": "histogram_quantile(0.99, sum(rate(ticdc_sink_flush_event_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (le,instance,type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-{{type}}-p99", "refId": "B"}, {"expr": "histogram_quantile(0.999, sum(rate(ticdc_sink_flush_event_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (le,instance,type))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-{{type}}-p999", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Flush sink duration percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "max": null, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Asynchronous flush sink duration of changefeeds", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 47}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 93, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "max(rate(ticdc_sink_flush_event_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "Flush sink duration", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Distribution of MySQL worker loads", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 47}, "hiddenSeries": false, "id": 95, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_sink_bucket_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (capture,bucket)", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}-{{bucket}}", "refId": "A"}, {"expr": "count(rate(ticdc_sink_bucket_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m]) >= 0)", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "total worker", "refId": "B"}, {"expr": "count(rate(ticdc_sink_bucket_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m]) <= 2)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "0-2 row/s worker", "refId": "C"}, {"expr": "count(rate(ticdc_sink_bucket_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m]) > 2 and rate(ticdc_sink_bucket_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m]) <= 10)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "2-10 row/s worker", "refId": "D"}, {"expr": "count(rate(ticdc_sink_bucket_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m]) > 10 and rate(ticdc_sink_bucket_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m]) <= 100)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "10-100 row/s worker", "refId": "E"}, {"expr": "count(rate(ticdc_sink_bucket_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m]) > 100)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": ">100 row/s worker", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "MySQL sink worker load", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "max": null, "min": 1, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "The duration of detecting and waiting conflict of MySQL sink", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 54}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 103, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "repeatDirection": "h", "reverseYBuckets": false, "targets": [{"expr": "max(rate(ticdc_sink_conflict_detect_duration_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "MySQL sink conflict detect duration", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Percentiles of detecting and waiting conflict duration of MySQL sink", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 54}, "hiddenSeries": false, "id": 83, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.95,sum(rate(ticdc_sink_conflict_detect_duration_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,instance))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}-p95", "refId": "A"}, {"expr": "histogram_quantile(0.99,sum(rate(ticdc_sink_conflict_detect_duration_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,instance))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}-p99", "refId": "B"}, {"expr": "histogram_quantile(0.999,sum(rate(ticdc_sink_conflict_detect_duration_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,instance))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}-p999", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "MySQL sink conflict detect duration percentile", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 61}, "id": 149, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_processor_table_memory_consumption_sum{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m]) / rate(ticdc_processor_table_memory_consumption_count{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{ capture }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Processor Memory Consumption Per Capture", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"columns": [], "datasource": "tidb-cluster", "fontSize": "100%", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 61}, "id": 151, "links": [], "pageSize": null, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"alias": "", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "changefeed", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "table", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "instance", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "memory consumption", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "bytes"}, {"alias": "", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "/.*/", "thresholds": [], "type": "hidden", "unit": "short"}], "targets": [{"expr": "sum(rate(ticdc_processor_table_memory_consumption_sum{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m]) / rate(ticdc_processor_table_memory_consumption_count{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (capture, changefeed)", "format": "table", "intervalFactor": 1, "legendFormat": "{{ capture }} - {{ changefeed }}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Processor Memory Consumption Per Table", "transform": "table", "type": "table"}], "title": "Changefeed", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 13, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of established Eventfeed RPC between TiCDC and TiKV", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 3}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*-rpc/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(ticdc_kvclient_event_feed_count{cluster_id=~\".*$cluster_id\"}) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}, {"expr": "sum(grpc_client_started_total{cluster_id=~\".*$cluster_id\", grpc_method=\"EventFeed\"}) by (instance) - sum(grpc_client_handled_total{cluster_id=~\".*$cluster_id\", grpc_method=\"EventFeed\"}) by (instance)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-rpc", "refId": "B"}, {"expr": "sum(grpc_client_started_total{cluster_id=~\".*$cluster_id\", grpc_method=\"EventFeed\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}}-rpc-started", "refId": "C"}, {"expr": "sum(grpc_client_handled_total{cluster_id=~\".*$cluster_id\", grpc_method=\"EventFeed\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}}-rpc-handled", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Eventfeed count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "Percentiles of Eventfeed message size", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 3}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(ticdc_kvclient_event_size_bytes_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-p999", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(ticdc_kvclient_event_size_bytes_bucket{cluster_id=~\".*$cluster_id\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-p95", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Event size percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of errors that interrupt Eventfeed RPC", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 3}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(ticdc_kvclient_event_feed_error_count{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (type)", "format": "time_series", "hide": false, "interval": "1m", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}, {"expr": "-sum(increase(pd_schedule_operators_count{cluster_id=~\".*$cluster_id\", event=\"create\", type=~\".*leader\"}[2m]))", "format": "time_series", "hide": false, "interval": "1m", "intervalFactor": 1, "legendFormat": "transfer-leader", "refId": "B"}, {"expr": "-sum(increase(pd_schedule_operators_count{cluster_id=~\".*$cluster_id\", event=\"create\", type=~\".*(peer|region)\"}[2m]))", "format": "time_series", "interval": "1m", "intervalFactor": 1, "legendFormat": "move-region", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Eventfeed error/m", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of KV client received events from TiKV per seconds", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 10}, "hiddenSeries": false, "id": 29, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_kvclient_pull_event_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (instance, type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV client receive events/s", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of events that puller outputs to sorter \n per second", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 10}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum (rate(ticdc_puller_txn_collect_event_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (instance, type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} - {{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Puller output events/s", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of rows that sink flushes to downstream per second", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 10}, "hiddenSeries": false, "id": 108, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_sink_total_flushed_rows_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sink flush rows/s", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of events that are buffered in <PERSON><PERSON><PERSON>'s memory buffer and output channel", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 17}, "hiddenSeries": false, "id": 43, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*chan.*/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_puller_output_chan_size_sum{tidb_cluster=\\\"$tidb_cluster\\\", changefeed=~\\\"$changefeed\\\",capture=~\\\"$capture\\\"}[2m]) / rate(ticdc_puller_output_chan_size_count{tidb_cluster=\\\"$tidb_cluster\\\", changefeed=~\\\"$changefeed\\\",capture=~\\\"$capture\\\"}[2m])) by (capture)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{capture}} - output chan", "refId": "B"}, {"expr": "sum(rate(ticdc_puller_event_chan_size_sum{tidb_cluster=\\\"$tidb_cluster\\\", changefeed=~\\\"$changefeed\\\",capture=~\\\"$capture\\\"}[2m]) / rate(ticdc_puller_event_chan_size_count{tidb_cluster=\\\"$tidb_cluster\\\", changefeed=~\\\"$changefeed\\\",capture=~\\\"$capture\\\"}[2m])) by (capture)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{capture}} - event chan", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Puller buffer size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of events that are buffered in Sorter's unsorted events buffer and output channel", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 17}, "hiddenSeries": false, "id": 51, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*resolvedts/", "yaxis": 2}, {"alias": "/.*chan/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(ticdc_puller_entry_sorter_unsorted_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-unsorted events", "refId": "A"}, {"expr": "-sum(ticdc_puller_entry_sorter_resolved_chan_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "{{capture}}-resolvedts", "refId": "B"}, {"expr": "-sum(ticdc_puller_entry_sorter_output_chan_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{capture}}-ou<PERSON> chan", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Entry sorter buffer size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of events that are buffered in Processor's output channel and Mounter input channel", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 17}, "hiddenSeries": false, "id": 107, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*processor.*/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(ticdc_mounter_input_chan_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-mounter input chan", "refId": "A"}, {"expr": "-sum(ticdc_sink_buffer_chan_size{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-sink buffer chan", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sink/Mounter buffer size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of rows(events) that are buffered in Sink's pending flush rows buffer", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 17}, "hiddenSeries": false, "id": 96, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(ticdc_sink_total_rows_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture) - sum(ticdc_sink_total_flushed_rows_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}) by (capture)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{capture}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sink rows buffer size", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of sorting unsorted events", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 24}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 99, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(rate(ticdc_puller_entry_sorter_sort_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "Entry sorter sort duration", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Percentiles of sorting events duration", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 24}, "hiddenSeries": false, "id": 53, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(ticdc_puller_entry_sorter_sort_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,capture))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-p999", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(ticdc_puller_entry_sorter_sort_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,capture))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{capture}}-p95", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Entry sorter sort duration percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of merging sorted events", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 31}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 105, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(rate(ticdc_puller_entry_sorter_merge_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "Entry sorter merge duration", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Percentiles of merging sorted events duration", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 31}, "hiddenSeries": false, "id": 106, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(ticdc_puller_entry_sorter_merge_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,capture))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-p999", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(ticdc_puller_entry_sorter_merge_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le,capture))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{capture}}-p95", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Entry sorter merge duration percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "Duration of unmarshal events from kv to SQL row", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 38}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 101, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "max(rate(ticdc_mounter_unmarshal_and_mount_bucket{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "Mounter unmarshal duration", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Percentiles of unmarshal events from kv to SQL row duration", "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 38}, "hiddenSeries": false, "id": 55, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(ticdc_mounter_unmarshal_and_mount_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le, capture))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{capture}}-p99", "refId": "A"}, {"expr": "histogram_quantile(0.999, sum(rate(ticdc_mounter_unmarshal_and_mount_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (le, capture))", "format": "time_series", "hide": true, "instant": false, "intervalFactor": 1, "legendFormat": "{{capture}}-p999", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mounter unmarshal duration percentile", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The number of KV client dispatched event per second", "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 45}, "hiddenSeries": false, "id": 31, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "paceLength": 10, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*batch-resolved/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_kvclient_send_event_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\"}[2m])) by (capture, changefeed, type)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-{{changefeed}}-{{type}}", "refId": "A"}, {"expr": "sum(rate(ticdc_kvclient_batch_resolved_event_size_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\",capture=~\"$capture\"}[2m])) by (capture, changefeed, table)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-{{changefeed}}-batch-resolved", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV client dispatch events/s", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "The size of batch resolved ts message from TiKV", "gridPos": {"h": 7, "w": 12, "x": 12, "y": 45}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 97, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(rate(ticdc_kvclient_batch_resolved_event_size_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "KV client batch resolved size", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "none", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}], "title": "Events", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 130, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "id": 131, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_sorter_consume_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\", capture=~\"$capture\"}[2m])) by (capture,changefeed)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-{{changefeed}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unified Sorter intake rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}, "id": 132, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(ticdc_sorter_event_count{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\", capture=~\"$capture\"}[2m])) by (capture,changefeed)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}-{{changefeed}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unified Sorter event output rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 133, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(ticdc_sorter_on_disk_data_size_gauge{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unified Sorter on disk data size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}, "id": 134, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(ticdc_sorter_in_memory_data_size_gauge{cluster_id=~\".*$cluster_id\", capture=~\"$capture\"}) by (capture)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{capture}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unified Sorter in-memory data size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "max": null, "min": null, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 135, "legend": {"show": false}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(rate(ticdc_sorter_flush_count_histogram_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\", capture=~\"$capture\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 1, "legendFormat": "{{le}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Unified Sorter flush sizes", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": null, "format": "none", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}, {"cards": {"cardPadding": null, "cardRound": null}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateBlues", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 136, "legend": {"show": false}, "links": [], "reverseYBuckets": false, "targets": [{"expr": "sum(rate(ticdc_sorter_merge_count_histogram_bucket{cluster_id=~\".*$cluster_id\", changefeed=~\"$changefeed\", capture=~\"$capture\"}[2m])) by (le)", "format": "heatmap", "intervalFactor": 1, "legendFormat": "{{le}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Unified Sorter merge size", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": null, "format": "none", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "auto", "yBucketNumber": null, "yBucketSize": null}], "title": "Unified Sorter", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 58, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 5}, "id": 60, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cdc_.*|cdc\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "A", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CDC endpoint CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 5}, "id": 62, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*tso/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cdcwkr.*\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - worker", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"tso\"}[2m])) by (instance)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{instance}} - tso", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CDC worker CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The count of different kinds of gRPC message", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 12}, "id": 147, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_grpc_msg_duration_seconds_count{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", type!=\"kv_gc\"}[2m])) by (type)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{type}}", "metric": "tikv_grpc_msg_duration_seconds_bucket", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "gRPC message count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The memory usage per TiKV instance", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 12}, "id": 74, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*-cap-.*/", "yaxis": 2}, {"alias": "/.*tikv.*/", "pointradius": 1, "points": true, "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", component=~\"tikv.*\"}) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "tikv-{{instance}}", "refId": "A", "step": 10}, {"expr": "avg(process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", component=~\"cdc.*\"}) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "cdc-{{instance}}", "refId": "B", "step": 10}, {"expr": "(avg(process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", component=~\"tikv.*\"}) by (instance)) - (avg(tikv_engine_block_cache_size_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", db=\"kv\"}) by(instance))", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "tikv-{{instance}}", "refId": "C", "step": 10}, {"expr": "sum(tikv_cdc_sink_memory_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "sink-{{instance}}", "refId": "D", "step": 10}, {"expr": "sum(tikv_cdc_old_value_cache_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "old-value-{{instance}}", "refId": "E", "step": 10}, {"expr": "sum(tikv_cdc_sink_memory_capacity{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "sink-cap-{{instance}}", "refId": "F", "step": 10}, {"expr": "sum(tikv_cdc_old_value_cache_memory_quota{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "old-value-cap-{{instance}}", "refId": "G", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CDC memory", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The min resolved ts of each TiKV", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 7, "x": 0, "y": 19}, "id": 152, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "sideWidth": null, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*-ts/", "lines": true, "linewidth": 3, "points": false, "yaxis": 2}, {"alias": "/.*-lag/", "bars": true, "fill": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "scalar(max(pd_cluster_tso{cluster_id=~\".*$cluster_id\"})) - avg(tikv_cdc_min_resolved_ts{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}/1000) by (instance) > 0", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 2, "legendFormat": "{{instance}}-min-resolved-lag", "refId": "A", "step": 10}, {"expr": "max(pd_cluster_tso{cluster_id=~\".*$cluster_id\"}) * 1000", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "current-ts", "refId": "B", "step": 10}, {"expr": "avg(tikv_cdc_min_resolved_ts{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}-min-resolved-ts", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Min resolved ts", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The ID of the min resolved region of each TiKV", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 5, "x": 7, "y": 19}, "id": 153, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": null, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 1, "points": true, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_cdc_min_resolved_ts_region{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}-min-resolved-region", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Min resolved Region", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 19}, "id": 70, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99999, sum(rate(tikv_cdc_resolved_ts_gap_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-p9999", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Resolved ts lag duration percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 10, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "The time consumed to CDC incremental scan", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 26}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 68, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "maxPerRow": 3, "repeat": null, "repeatDirection": "h", "reverseYBuckets": false, "targets": [{"expr": "sum(rate(tikv_cdc_scan_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "Initial scan duration", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "", "fill": 1, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 26}, "id": 72, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.9999, sum(rate(tikv_cdc_scan_duration_seconds_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le, instance))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}}-p9999", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Initial scan duration percentile", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The number of incremental scan task in different status.", "fill": 1, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 26}, "id": 140, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*ongoing/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_cdc_scan_tasks{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", type=\"ongoing\"}) by (type, instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}} - {{type}}", "refId": "A"}, {"expr": "sum(tikv_cdc_scan_tasks{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", type=\"total\"}) by (instance) - sum(tikv_cdc_scan_tasks{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", type=~\"abort|finish\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}} - pending", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Initial scan tasks status", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The memory usage per TiKV instance", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 33}, "id": 78, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": null, "sortDesc": null, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(tikv_cdc_captured_region_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "tikv-{{instance}}-total", "refId": "A", "step": 10}, {"expr": "sum(tikv_cdc_region_resolve_status{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance, status)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "tikv-{{instance}}-{{status}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Captured region count", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The speed of TiKV CDC incremental scan", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 33}, "id": 76, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_cdc_scan_bytes_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", component=\"tikv\"}[2m])) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "tikv-{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CDC scan speed", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The total bytes of TiKV CDC incremental scan", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 33}, "id": 139, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_cdc_scan_bytes_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", component=\"tikv\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "tikv-{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CDC total scan bytes", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "", "fill": 4, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 40}, "id": 143, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": false, "min": true, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 1, "points": true, "renderer": "flot", "seriesOverrides": [{"alias": "/(access|miss).*/", "fill": 0, "points": false, "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(sum(rate(tikv_cdc_old_value_cache_access{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (instance) - sum(rate(tikv_cdc_old_value_cache_miss{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (instance)) / sum(rate(tikv_cdc_old_value_cache_access{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "hit-rate-{{instance}}", "refId": "A"}, {"expr": "-sum(rate(tikv_cdc_old_value_cache_access{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (instance)", "format": "time_series", "hide": true, "intervalFactor": 1, "legendFormat": "access-{{instance}}", "refId": "B"}, {"expr": "-sum(rate(tikv_cdc_old_value_cache_miss{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "miss-{{instance}}", "refId": "C"}, {"expr": "-sum(rate(tikv_cdc_old_value_cache_miss_none{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "miss-none-{{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Old value cache hit", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The total number of cache entries in the old value cache.", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 40}, "id": 145, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*len/", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_cdc_old_value_cache_length{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}} - len", "refId": "A"}, {"expr": "sum(tikv_cdc_old_value_cache_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance) / sum(tikv_cdc_old_value_cache_length{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}} - avg entry bytes", "refId": "B"}, {"expr": "sum(tikv_cdc_old_value_cache_memory_quota{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}) by (instance)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{instance}} - quota", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Old value cache size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": null, "format": "none", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 47}, "id": 141, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_cdc_old_value_scan_details{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (instance, cf, tag)", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "{{instance}}-  {{cf}} - {{tag}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Old value seek operation", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cards": {"cardPadding": 0, "cardRound": 0}, "color": {"cardColor": "#FF9830", "colorScale": "linear", "colorScheme": "interpolateSpectral", "exponent": 0.5, "min": 0, "mode": "spectrum"}, "dataFormat": "tsbuckets", "datasource": "tidb-cluster", "description": "The time consumed to get an old value (both from cache and from disk)", "gridPos": {"h": 7, "w": 6, "x": 12, "y": 47}, "heatmap": {}, "hideZeroBuckets": true, "highlightCards": true, "id": 146, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "links": [], "maxPerRow": 3, "repeatDirection": "h", "reverseYBuckets": false, "targets": [{"expr": "sum(rate(tikv_cdc_old_value_duration_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le)", "format": "heatmap", "instant": false, "intervalFactor": 2, "legendFormat": "{{le}}", "refId": "A"}], "title": "Old value seek duration", "tooltip": {"show": true, "showHistogram": true}, "tooltipDecimals": 1, "type": "heatmap", "xAxis": {"show": true}, "xBucketNumber": null, "xBucketSize": null, "yAxis": {"decimals": 1, "format": "s", "logBase": 1, "max": null, "min": null, "show": true, "splitFactor": null}, "yBucketBound": "upper", "yBucketNumber": null, "yBucketSize": null}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "", "fill": 1, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 47}, "id": 142, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": false, "hideZero": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "paceLength": 10, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tikv_cdc_old_value_duration_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le, instance, tag))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} - 99% - {{tag}}", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(tikv_cdc_old_value_duration_bucket{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le, instance, tag))", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} - 95% - {{tag}}", "refId": "B"}, {"expr": "sum(rate(tikv_cdc_old_value_duration_sum{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le, instance, tag) / sum(rate(tikv_cdc_old_value_duration_count{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\"}[2m])) by (le, instance, tag)", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{instance}} - avg - {{tag}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Old value seek duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "title": "TiKV", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 155, "panels": [{"aliasColors": {}, "bars": true, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiCDC process rss memory usage. TiCDC heap memory size in use ", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 6}, "id": 157, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sideWidth": null, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "scopedVars": {"instance": {"selected": false, "text": "***********:47912", "value": "***********:47912"}, "runtime_instance": {"selected": false, "text": "***********:47912", "value": "***********:47912"}}, "seriesOverrides": [{"alias": "alloc-from-os", "fill": 3, "lines": true, "stack": false}, {"alias": "gc-threshold", "bars": false, "color": "#C4162A", "lines": true, "linewidth": 2, "stack": false}, {"alias": "gc", "bars": false, "color": "#C4162A", "hideTooltip": true, "legend": false, "pointradius": 3, "points": true, "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "alloc-from-os", "refId": "A"}, {"expr": "go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} / (1 + tidb_server_gogc{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} / 100)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "estimate-inuse", "refId": "H"}, {"expr": "go_memstats_heap_alloc_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} - go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} / (1 + tidb_server_gogc{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} / 100)", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "estimate-garbage", "refId": "C"}, {"expr": "go_memstats_heap_idle_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} - go_memstats_heap_released_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} + go_memstats_heap_inuse_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} - go_memstats_heap_alloc_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "reserved-by-go", "refId": "B"}, {"expr": "go_memstats_stack_sys_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} + go_memstats_mspan_sys_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} + go_memstats_mcache_sys_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} + go_memstats_buck_hash_sys_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} + go_memstats_gc_sys_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} + go_memstats_other_sys_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "used-by-go", "refId": "D"}, {"expr": "go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "gc-threshold", "refId": "E"}, {"expr": "(clamp_max(idelta(go_memstats_last_gc_time_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}[2m]), 1) * go_memstats_next_gc_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}) > 0", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "gc", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "Count of live objects.", "editable": true, "error": false, "fill": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 6}, "id": 158, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": false, "show": true, "sideWidth": null, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "repeatDirection": "h", "scopedVars": {"instance": {"selected": false, "text": "***********:47912", "value": "***********:47912"}, "runtime_instance": {"selected": false, "text": "***********:47912", "value": "***********:47912"}}, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_memstats_heap_objects{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "objects", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Estimated Live Objects", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiCDC process Go garbage collection STW pause duration", "editable": true, "error": false, "fill": 1, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 13}, "id": 160, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "percentage": false, "pluginVersion": "6.1.6", "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"instance": {"selected": false, "text": "***********:47912", "value": "***********:47912"}, "runtime_instance": {"selected": false, "text": "***********:47912", "value": "***********:47912"}}, "seriesOverrides": [{}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_gc_duration_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\", quantile=\"0\"}", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "min", "refId": "A", "step": 40}, {"expr": "go_gc_duration_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\", quantile!~\"0|1\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{quantile}}", "refId": "B"}, {"expr": "go_gc_duration_seconds{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\", quantile=\"1\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "max", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC STW Duration (last 256 GC cycles)", "tooltip": {"msResolution": false, "shared": true, "sort": 1, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The throughput of <PERSON>'s memory allocator.", "fill": 1, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 13}, "id": 161, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "scopedVars": {"instance": {"selected": false, "text": "***********:47912", "value": "***********:47912"}, "runtime_instance": {"selected": false, "text": "***********:47912", "value": "***********:47912"}}, "seriesOverrides": [{"alias": "sweep", "transform": "negative-Y"}, {"alias": "alloc-ops", "yaxis": 2}, {"alias": "swepp-ops", "transform": "negative-Y", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(go_memstats_alloc_bytes_total{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}[2m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "alloc", "refId": "A"}, {"expr": "irate((go_memstats_alloc_bytes_total{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"} - go_memstats_heap_alloc_bytes{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"})[30s:])", "format": "time_series", "intervalFactor": 1, "legendFormat": "sweep", "refId": "B"}, {"expr": "irate(go_memstats_mallocs_total{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}[2m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "alloc-ops", "refId": "C"}, {"expr": "irate(go_memstats_frees_total{cluster_id=~\".*$cluster_id\", instance=~\"$runtime_instance\"}[2m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "swepp-ops", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Allocator Throughput", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": true, "alignLevel": null}}], "repeat": "runtime_instance", "title": "Runtime $runtime_instance", "type": "row"}], "refresh": "1m", "schemaVersion": 18, "style": "dark", "tags": ["tidb-mixin", "ticdc"], "templating": {"list": [{"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{status=\"active\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_name", "query": "label_values(dbaas_tenant_info{status=\"active\"},name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_name", "options": [{"selected": true, "text": "<PERSON><PERSON>-<PERSON>", "value": "<PERSON><PERSON>-<PERSON>"}], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "tidb-cluster", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "tidb-cluster", "definition": "label_values(ticdc_processor_resolved_ts{cluster_id=~\".*$cluster_id\"}, changefeed)", "hide": 0, "includeAll": true, "label": "Changefeed", "multi": true, "name": "changefeed", "options": [], "query": "label_values(ticdc_processor_resolved_ts{cluster_id=~\".*$cluster_id\"}, changefeed)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "tidb-cluster", "definition": "label_values(process_start_time_seconds{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}, instance)", "hide": 0, "includeAll": true, "label": "TiCDC", "multi": true, "name": "capture", "options": [], "query": "label_values(process_start_time_seconds{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}, instance)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {}, "datasource": "tidb-cluster", "definition": "label_values(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}, instance)", "hide": 0, "includeAll": true, "label": "TiKV", "multi": false, "name": "tikv_instance", "options": [], "query": "label_values(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}, instance)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "9999999999", "current": {"selected": true, "text": "All", "value": "$__all"}, "hide": 0, "includeAll": true, "label": "Latency spike (s) >", "multi": false, "name": "spike_threshold", "options": [{"selected": true, "text": "All", "value": "$__all"}, {"selected": false, "text": "1", "value": "1"}, {"selected": false, "text": "3", "value": "3"}, {"selected": false, "text": "5", "value": "5"}, {"selected": false, "text": "10", "value": "10"}, {"selected": false, "text": "60", "value": "60"}, {"selected": false, "text": "300", "value": "300"}], "query": "1, 3, 5, 10, 60, 300", "skipUrlSync": false, "type": "custom"}, {"allValue": "", "current": {}, "datasource": "tidb-cluster", "definition": "label_values(process_start_time_seconds{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}, instance)", "hide": 0, "includeAll": true, "label": "Runtime metrics", "multi": false, "name": "runtime_instance", "options": [], "query": "label_values(process_start_time_seconds{cluster_id=~\".*$cluster_id\", component=\"ticdc\"}, instance)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "TiCDC", "uid": "YiGL8hBZ1", "version": 19}