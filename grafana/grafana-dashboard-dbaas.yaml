apiVersion: v1
kind: ConfigMapList
items:
- apiVersion: v1
  data:
    central.json: |-
      {
        "annotations": {
          "list": [
            {
              "builtIn": 1,
              "datasource": "-- Grafana --",
              "enable": true,
              "hide": true,
              "iconColor": "rgba(0, 211, 255, 1)",
              "name": "Annotations & Alerts",
              "type": "dashboard"
            }
          ]
        },
        "editable": true,
        "gnetId": null,
        "graphTooltip": 0,
        "iteration": 1595237841171,
        "links": [],
        "panels": [
          {
            "datasource": "prometheus",
            "gridPos": {
              "h": 8,
              "w": 12,
              "x": 0,
              "y": 0
            },
            "id": 8,
            "options": {
              "fieldOptions": {
                "calcs": [
                  "mean"
                ],
                "defaults": {
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 80
                      }
                    ]
                  }
                },
                "overrides": [],
                "values": false
              },
              "orientation": "auto",
              "showThresholdLabels": false,
              "showThresholdMarkers": true
            },
            "pluginVersion": "6.6.0",
            "targets": [
              {
                "expr": "sum(dbaas_tidb_cluster_status)",
                "refId": "A"
              }
            ],
            "timeFrom": null,
            "timeShift": null,
            "title": "Total",
            "type": "gauge"
          },
          {
            "datasource": "prometheus",
            "gridPos": {
              "h": 8,
              "w": 12,
              "x": 12,
              "y": 0
            },
            "id": 9,
            "options": {
              "fieldOptions": {
                "calcs": [
                  "mean"
                ],
                "defaults": {
                  "mappings": [],
                  "thresholds": {
                    "mode": "absolute",
                    "steps": [
                      {
                        "color": "green",
                        "value": null
                      },
                      {
                        "color": "red",
                        "value": 0
                      }
                    ]
                  }
                },
                "overrides": [],
                "values": false
              },
              "orientation": "auto",
              "showThresholdLabels": false,
              "showThresholdMarkers": true
            },
            "pluginVersion": "6.6.0",
            "targets": [
              {
                "expr": "sum(dbaas_tidb_cluster_status{status=\"unready\"})",
                "refId": "A"
              }
            ],
            "timeFrom": null,
            "timeShift": null,
            "title": "Unready",
            "type": "gauge"
          },
          {
            "columns": [],
            "datasource": "prometheus",
            "description": "",
            "fontSize": "100%",
            "gridPos": {
              "h": 8,
              "w": 24,
              "x": 0,
              "y": 8
            },
            "id": 5,
            "options": {},
            "pageSize": null,
            "showHeader": true,
            "sort": {
              "col": 2,
              "desc": true
            },
            "styles": [
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "Time",
                "thresholds": [],
                "type": "hidden",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "Value",
                "thresholds": [],
                "type": "hidden",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 2,
                "pattern": "status",
                "preserveFormat": false,
                "rangeMaps": [],
                "sanitize": false,
                "thresholds": [
                  "1"
                ],
                "type": "string",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "",
                "thresholds": [],
                "type": "number",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "",
                "thresholds": [],
                "type": "number",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "decimals": 2,
                "mappingType": 1,
                "pattern": "cluster_id",
                "preserveFormat": false,
                "sanitize": false,
                "thresholds": [],
                "type": "string",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "Time",
                "thresholds": [],
                "type": "hidden",
                "unit": "short"
              },
              {
                "alias": "status",
                "align": "auto",
                "colorMode": "row",
                "colors": [
                  "#5794F2",
                  "#73BF69",
                  "rgba(245, 54, 54, 0.9)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "Value",
                "thresholds": [
                  "1",
                  "2"
                ],
                "type": "number",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "",
                "thresholds": [],
                "type": "number",
                "unit": "short"
              }
            ],
            "targets": [
              {
                "expr": "sum(dbaas_tidb_cluster_status{namespace=\"$namespace\"})by(cluster_id,status,tenant)",
                "format": "table",
                "instant": true,
                "intervalFactor": 1,
                "refId": "A"
              }
            ],
            "timeFrom": null,
            "timeShift": null,
            "title": "dbaas_tidb_cluster_status",
            "transform": "table",
            "type": "table"
          },
          {
            "columns": [],
            "datasource": "prometheus",
            "description": "",
            "fontSize": "100%",
            "gridPos": {
              "h": 8,
              "w": 24,
              "x": 0,
              "y": 16
            },
            "id": 10,
            "options": {},
            "pageSize": null,
            "showHeader": true,
            "sort": {
              "col": 2,
              "desc": true
            },
            "styles": [
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "Time",
                "thresholds": [],
                "type": "hidden",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "decimals": 2,
                "mappingType": 1,
                "pattern": "cluster_id",
                "preserveFormat": false,
                "sanitize": false,
                "thresholds": [],
                "type": "string",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "auto",
                "colorMode": null,
                "colors": [
                  "rgba(245, 54, 54, 0.9)",
                  "rgba(237, 129, 40, 0.89)",
                  "rgba(50, 172, 45, 0.97)"
                ],
                "dateFormat": "YYYY-MM-DD HH:mm:ss",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "Time",
                "thresholds": [],
                "type": "hidden",
                "unit": "short"
              },
              {
                "alias": "",
                "align": "auto",
                "colorMode": "row",
                "colors": [
                  "rgba(50, 172, 45, 0.97)",
                  "rgba(237, 129, 40, 0.89)",
                  "#C4162A"
                ],
                "dateFormat": "YYYY-MM-DD",
                "decimals": 2,
                "mappingType": 1,
                "pattern": "Value",
                "thresholds": [
                  "120",
                  "600"
                ],
                "type": "number",
                "unit": "s"
              }
            ],
            "targets": [
              {
                "expr": "sum(dbaas_tidb_cluster_wait_ready_time{namespace=\"$namespace\"})by(cluster_id,status,tenant)",
                "format": "table",
                "instant": true,
                "intervalFactor": 1,
                "refId": "A"
              }
            ],
            "timeFrom": null,
            "timeShift": null,
            "title": "dbaas_tidb_cluster_wait_ready_time",
            "transform": "table",
            "type": "table"
          },
          {
            "aliasColors": {},
            "bars": false,
            "dashLength": 10,
            "dashes": false,
            "datasource": "prometheus",
            "fill": 1,
            "fillGradient": 0,
            "gridPos": {
              "h": 8,
              "w": 24,
              "x": 0,
              "y": 24
            },
            "hiddenSeries": false,
            "id": 6,
            "legend": {
              "alignAsTable": false,
              "avg": false,
              "current": false,
              "max": false,
              "min": false,
              "rightSide": false,
              "show": true,
              "total": false,
              "values": false
            },
            "lines": true,
            "linewidth": 1,
            "nullPointMode": "null",
            "options": {
              "dataLinks": []
            },
            "percentage": false,
            "pointradius": 2,
            "points": false,
            "renderer": "flot",
            "seriesOverrides": [],
            "spaceLength": 10,
            "stack": false,
            "steppedLine": false,
            "targets": [
              {
                "expr": "rate(dbaas_statistics_connect_db_failed{namespace=\"$namespace\"}[5m])",
                "legendFormat": "{{ pod }}",
                "refId": "A"
              }
            ],
            "thresholds": [],
            "timeFrom": null,
            "timeRegions": [],
            "timeShift": null,
            "title": "dbaas_statistics_connect_db_failed",
            "tooltip": {
              "shared": true,
              "sort": 0,
              "value_type": "individual"
            },
            "type": "graph",
            "xaxis": {
              "buckets": null,
              "mode": "time",
              "name": null,
              "show": true,
              "values": []
            },
            "yaxes": [
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              },
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              }
            ],
            "yaxis": {
              "align": false,
              "alignLevel": null
            }
          },
          {
            "aliasColors": {},
            "bars": false,
            "dashLength": 10,
            "dashes": false,
            "datasource": "prometheus",
            "fill": 1,
            "fillGradient": 0,
            "gridPos": {
              "h": 8,
              "w": 24,
              "x": 0,
              "y": 32
            },
            "hiddenSeries": false,
            "id": 3,
            "legend": {
              "alignAsTable": false,
              "avg": false,
              "current": false,
              "max": false,
              "min": false,
              "rightSide": false,
              "show": true,
              "total": false,
              "values": false
            },
            "lines": true,
            "linewidth": 1,
            "nullPointMode": "null",
            "options": {
              "dataLinks": []
            },
            "percentage": false,
            "pointradius": 2,
            "points": false,
            "renderer": "flot",
            "seriesOverrides": [],
            "spaceLength": 10,
            "stack": false,
            "steppedLine": false,
            "targets": [
              {
                "expr": "sum(rate(dbaas_checkworker_failed{namespace=\"$namespace\"}[5m]))by(pod)",
                "legendFormat": "{{ pod }}",
                "refId": "A"
              }
            ],
            "thresholds": [],
            "timeFrom": null,
            "timeRegions": [],
            "timeShift": null,
            "title": "dbaas_checkworker_failed",
            "tooltip": {
              "shared": true,
              "sort": 0,
              "value_type": "individual"
            },
            "type": "graph",
            "xaxis": {
              "buckets": null,
              "mode": "time",
              "name": null,
              "show": true,
              "values": []
            },
            "yaxes": [
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              },
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              }
            ],
            "yaxis": {
              "align": false,
              "alignLevel": null
            }
          }
        ],
        "schemaVersion": 22,
        "style": "dark",
        "tags": [],
        "templating": {
          "list": [
            {
              "allValue": null,
              "current": {
                "text": "prod",
                "value": "prod"
              },
              "datasource": "prometheus",
              "definition": "label_values(dbaas_statistics_connect_db_failed,namespace)",
              "hide": 0,
              "includeAll": false,
              "label": "namespace",
              "multi": false,
              "name": "namespace",
              "options": [],
              "query": "label_values(dbaas_statistics_connect_db_failed,namespace)",
              "refresh": 1,
              "regex": "",
              "skipUrlSync": false,
              "sort": 0,
              "tagValuesQuery": "",
              "tags": [],
              "tagsQuery": "",
              "type": "query",
              "useTags": false
            }
          ]
        },
        "time": {
          "from": "now-12h",
          "to": "now"
        },
        "timepicker": {
          "refresh_intervals": [
            "5s",
            "10s",
            "30s",
            "1m",
            "5m",
            "15m",
            "30m",
            "1h",
            "2h",
            "1d"
          ]
        },
        "timezone": "",
        "title": "central",
        "uid": "R2U52nZGk",
        "version": 1
      }
  kind: ConfigMap
  metadata:
    name: grafana-dashboard-central-v2
    namespace: monitoring
#- apiVersion: v1
#  data:
#    billing.json: |-
#      {
#        "annotations": {
#          "list": [
#            {
#              "builtIn": 1,
#              "datasource": "-- Grafana --",
#              "enable": true,
#              "hide": true,
#              "iconColor": "rgba(0, 211, 255, 1)",
#              "name": "Annotations & Alerts",
#              "type": "dashboard"
#            }
#          ]
#        },
#        "editable": true,
#        "gnetId": null,
#        "graphTooltip": 0,
#        "iteration": 1595237613126,
#        "links": [],
#        "panels": [
#          {
#            "aliasColors": {},
#            "bars": false,
#            "dashLength": 10,
#            "dashes": false,
#            "datasource": "prometheus",
#            "fill": 1,
#            "fillGradient": 0,
#            "gridPos": {
#              "h": 8,
#              "w": 24,
#              "x": 0,
#              "y": 0
#            },
#            "hiddenSeries": false,
#            "id": 4,
#            "legend": {
#              "avg": false,
#              "current": false,
#              "max": false,
#              "min": false,
#              "show": true,
#              "total": false,
#              "values": false
#            },
#            "lines": true,
#            "linewidth": 1,
#            "nullPointMode": "null",
#            "options": {
#              "dataLinks": []
#            },
#            "percentage": false,
#            "pointradius": 2,
#            "points": false,
#            "renderer": "flot",
#            "seriesOverrides": [],
#            "spaceLength": 10,
#            "stack": false,
#            "steppedLine": false,
#            "targets": [
#              {
#                "expr": "rate(billing_consumer_sqs_message{namespace=\"$namespace\"}[5m])",
#                "legendFormat": "{{ pod }}",
#                "refId": "A"
#              }
#            ],
#            "thresholds": [],
#            "timeFrom": null,
#            "timeRegions": [],
#            "timeShift": null,
#            "title": "billing_consumer_sqs_message",
#            "tooltip": {
#              "shared": true,
#              "sort": 0,
#              "value_type": "individual"
#            },
#            "type": "graph",
#            "xaxis": {
#              "buckets": null,
#              "mode": "time",
#              "name": null,
#              "show": true,
#              "values": []
#            },
#            "yaxes": [
#              {
#                "format": "short",
#                "label": null,
#                "logBase": 1,
#                "max": null,
#                "min": null,
#                "show": true
#              },
#              {
#                "format": "short",
#                "label": null,
#                "logBase": 1,
#                "max": null,
#                "min": null,
#                "show": true
#              }
#            ],
#            "yaxis": {
#              "align": false,
#              "alignLevel": null
#            }
#          },
#          {
#            "aliasColors": {},
#            "bars": false,
#            "dashLength": 10,
#            "dashes": false,
#            "datasource": "prometheus",
#            "fill": 1,
#            "fillGradient": 0,
#            "gridPos": {
#              "h": 8,
#              "w": 24,
#              "x": 0,
#              "y": 8
#            },
#            "hiddenSeries": false,
#            "id": 2,
#            "legend": {
#              "avg": false,
#              "current": false,
#              "max": false,
#              "min": false,
#              "show": true,
#              "total": false,
#              "values": false
#            },
#            "lines": true,
#            "linewidth": 1,
#            "nullPointMode": "null",
#            "options": {
#              "dataLinks": []
#            },
#            "percentage": false,
#            "pointradius": 2,
#            "points": false,
#            "renderer": "flot",
#            "seriesOverrides": [],
#            "spaceLength": 10,
#            "stack": false,
#            "steppedLine": false,
#            "targets": [
#              {
#                "expr": "rate(billing_consumer_failed{namespace=\"$namespace\"}[5m])",
#                "refId": "B"
#              }
#            ],
#            "thresholds": [],
#            "timeFrom": null,
#            "timeRegions": [],
#            "timeShift": null,
#            "title": "billing_consumer_failed",
#            "tooltip": {
#              "shared": true,
#              "sort": 0,
#              "value_type": "individual"
#            },
#            "type": "graph",
#            "xaxis": {
#              "buckets": null,
#              "mode": "time",
#              "name": null,
#              "show": true,
#              "values": []
#            },
#            "yaxes": [
#              {
#                "format": "short",
#                "label": null,
#                "logBase": 1,
#                "max": null,
#                "min": null,
#                "show": true
#              },
#              {
#                "format": "short",
#                "label": null,
#                "logBase": 1,
#                "max": null,
#                "min": null,
#                "show": true
#              }
#            ],
#            "yaxis": {
#              "align": false,
#              "alignLevel": null
#            }
#          }
#        ],
#        "schemaVersion": 22,
#        "style": "dark",
#        "tags": [],
#        "templating": {
#          "list": [
#            {
#              "allValue": null,
#              "current": {
#                "text": "prod",
#                "value": "prod"
#              },
#              "datasource": "prometheus",
#              "definition": "label_values(billing_consumer_failed,namespace)",
#              "hide": 0,
#              "includeAll": false,
#              "label": "",
#              "multi": false,
#              "name": "namespace",
#              "options": [],
#              "query": "label_values(billing_consumer_failed,namespace)",
#              "refresh": 1,
#              "regex": "",
#              "skipUrlSync": false,
#              "sort": 0,
#              "tagValuesQuery": "",
#              "tags": [],
#              "tagsQuery": "",
#              "type": "query",
#              "useTags": false
#            }
#          ]
#        },
#        "time": {
#          "from": "now-6h",
#          "to": "now"
#        },
#        "timepicker": {
#          "refresh_intervals": [
#            "5s",
#            "10s",
#            "30s",
#            "1m",
#            "5m",
#            "15m",
#            "30m",
#            "1h",
#            "2h",
#            "1d"
#          ]
#        },
#        "timezone": "",
#        "title": "billing",
#        "uid": "oqevsWWMz",
#        "version": 1
#      }
#  kind: ConfigMap
#  metadata:
#    name: grafana-dashboard-billing-v2
#    namespace: monitoring
- apiVersion: v1
  data:
    central-grpc-server.json: |-
      {
        "annotations": {
          "list": [
            {
              "builtIn": 1,
              "datasource": "-- Grafana --",
              "enable": true,
              "hide": true,
              "iconColor": "rgba(0, 211, 255, 1)",
              "name": "Annotations & Alerts",
              "type": "dashboard"
            }
          ]
        },
        "editable": true,
        "gnetId": null,
        "graphTooltip": 0,
        "iteration": 1595237156319,
        "links": [],
        "panels": [
          {
            "aliasColors": {},
            "bars": false,
            "dashLength": 10,
            "dashes": false,
            "datasource": "prometheus",
            "fill": 1,
            "fillGradient": 0,
            "gridPos": {
              "h": 7,
              "w": 24,
              "x": 0,
              "y": 0
            },
            "hiddenSeries": false,
            "id": 2,
            "legend": {
              "alignAsTable": true,
              "avg": false,
              "current": true,
              "max": false,
              "min": false,
              "rightSide": true,
              "show": true,
              "sort": "total",
              "sortDesc": true,
              "total": true,
              "values": true
            },
            "lines": true,
            "linewidth": 1,
            "nullPointMode": "null",
            "options": {
              "dataLinks": []
            },
            "percentage": false,
            "pointradius": 2,
            "points": false,
            "renderer": "flot",
            "seriesOverrides": [],
            "spaceLength": 10,
            "stack": false,
            "steppedLine": false,
            "targets": [
              {
                "expr": "sum(rate(grpc_server_started_total{job=\"central-metrics\",grpc_service=~\"$grpc_service\",grpc_method=~\"$grpc_method\"}[1m])) by (grpc_service)",
                "legendFormat": "{{ grpc_service }}-{{ $grpc_method }}",
                "refId": "B"
              },
              {
                "expr": "sum(rate(grpc_server_started_total{job=\"central-metrics\"}[1m]))",
                "legendFormat": "total",
                "refId": "A"
              }
            ],
            "thresholds": [],
            "timeFrom": null,
            "timeRegions": [],
            "timeShift": null,
            "title": "request inbound rate",
            "tooltip": {
              "shared": true,
              "sort": 0,
              "value_type": "individual"
            },
            "type": "graph",
            "xaxis": {
              "buckets": null,
              "mode": "time",
              "name": null,
              "show": true,
              "values": []
            },
            "yaxes": [
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              },
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              }
            ],
            "yaxis": {
              "align": false,
              "alignLevel": null
            }
          },
          {
            "aliasColors": {},
            "bars": false,
            "dashLength": 10,
            "dashes": false,
            "datasource": "prometheus",
            "fill": 1,
            "fillGradient": 0,
            "gridPos": {
              "h": 7,
              "w": 24,
              "x": 0,
              "y": 7
            },
            "hiddenSeries": false,
            "id": 5,
            "legend": {
              "alignAsTable": true,
              "avg": false,
              "current": true,
              "max": false,
              "min": false,
              "rightSide": true,
              "show": true,
              "sort": "current",
              "sortDesc": true,
              "total": false,
              "values": true
            },
            "lines": true,
            "linewidth": 1,
            "nullPointMode": "null",
            "options": {
              "dataLinks": []
            },
            "percentage": false,
            "pointradius": 2,
            "points": false,
            "renderer": "flot",
            "seriesOverrides": [],
            "spaceLength": 10,
            "stack": false,
            "steppedLine": false,
            "targets": [
              {
                "expr": "histogram_quantile(0.99, \n  sum(rate(grpc_server_handling_seconds_bucket{job=\"central-metrics\",grpc_type=\"unary\",grpc_service=~\"$grpc_service\",grpc_method=~\"$grpc_method\"}[5m])) by (grpc_service,le)\n)",
                "legendFormat": "{{ grpc_service }}-{{ $grpc_method }}",
                "refId": "A"
              }
            ],
            "thresholds": [],
            "timeFrom": null,
            "timeRegions": [],
            "timeShift": null,
            "title": "99%-tile latency of unary requests (seconds)",
            "tooltip": {
              "shared": true,
              "sort": 0,
              "value_type": "individual"
            },
            "type": "graph",
            "xaxis": {
              "buckets": null,
              "mode": "time",
              "name": null,
              "show": true,
              "values": []
            },
            "yaxes": [
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              },
              {
                "format": "s",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              }
            ],
            "yaxis": {
              "align": false,
              "alignLevel": null
            }
          },
          {
            "aliasColors": {},
            "bars": false,
            "dashLength": 10,
            "dashes": false,
            "datasource": "prometheus",
            "fill": 1,
            "fillGradient": 0,
            "gridPos": {
              "h": 7,
              "w": 24,
              "x": 0,
              "y": 14
            },
            "hiddenSeries": false,
            "id": 6,
            "legend": {
              "alignAsTable": true,
              "avg": false,
              "current": true,
              "max": false,
              "min": false,
              "rightSide": true,
              "show": true,
              "sort": "total",
              "sortDesc": true,
              "total": false,
              "values": true
            },
            "lines": true,
            "linewidth": 1,
            "nullPointMode": "null",
            "options": {
              "dataLinks": []
            },
            "percentage": false,
            "pointradius": 2,
            "points": false,
            "renderer": "flot",
            "seriesOverrides": [],
            "spaceLength": 10,
            "stack": false,
            "steppedLine": false,
            "targets": [
              {
                "expr": "100.0 - (\nsum(rate(grpc_server_handling_seconds_bucket{job=\"central-metrics\",grpc_type=\"unary\",le=\"0.25\",grpc_service=~\"$grpc_service\",grpc_method=~\"$grpc_method\"}[5m])) by (grpc_service)\n / \nsum(rate(grpc_server_handling_seconds_count{job=\"central-metrics\",grpc_type=\"unary\",grpc_service=~\"$grpc_service\",grpc_method=~\"$grpc_method\"}[5m])) by (grpc_service)\n) * 100.0",
                "legendFormat": "{{ grpc_service }}-{{ $grpc_method }}",
                "refId": "A"
              }
            ],
            "thresholds": [],
            "timeFrom": null,
            "timeRegions": [],
            "timeShift": null,
            "title": "percentage of slow unary queries (>250ms)",
            "tooltip": {
              "shared": true,
              "sort": 0,
              "value_type": "individual"
            },
            "type": "graph",
            "xaxis": {
              "buckets": null,
              "mode": "time",
              "name": null,
              "show": true,
              "values": []
            },
            "yaxes": [
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              },
              {
                "format": "s",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              }
            ],
            "yaxis": {
              "align": false,
              "alignLevel": null
            }
          },
          {
            "aliasColors": {},
            "bars": false,
            "dashLength": 10,
            "dashes": false,
            "datasource": "prometheus",
            "fill": 1,
            "fillGradient": 0,
            "gridPos": {
              "h": 7,
              "w": 24,
              "x": 0,
              "y": 21
            },
            "hiddenSeries": false,
            "id": 3,
            "legend": {
              "alignAsTable": true,
              "avg": false,
              "current": true,
              "max": false,
              "min": false,
              "rightSide": true,
              "show": true,
              "sort": "total",
              "sortDesc": true,
              "total": true,
              "values": true
            },
            "lines": true,
            "linewidth": 1,
            "nullPointMode": "null",
            "options": {
              "dataLinks": []
            },
            "percentage": false,
            "pointradius": 2,
            "points": false,
            "renderer": "flot",
            "seriesOverrides": [],
            "spaceLength": 10,
            "stack": false,
            "steppedLine": false,
            "targets": [
              {
                "expr": "sum(rate(grpc_server_handled_total{job=\"central-metrics\",grpc_type=\"unary\",grpc_code!=\"OK\",grpc_service=~\"$grpc_service\",grpc_method=~\"$grpc_method\"}[1m])) by (grpc_service)",
                "legendFormat": "{{ grpc_service }}-{{ $grpc_method }}",
                "refId": "A"
              }
            ],
            "thresholds": [],
            "timeFrom": null,
            "timeRegions": [],
            "timeShift": null,
            "title": "unary request error rate",
            "tooltip": {
              "shared": true,
              "sort": 0,
              "value_type": "individual"
            },
            "type": "graph",
            "xaxis": {
              "buckets": null,
              "mode": "time",
              "name": null,
              "show": true,
              "values": []
            },
            "yaxes": [
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              },
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              }
            ],
            "yaxis": {
              "align": false,
              "alignLevel": null
            }
          },
          {
            "aliasColors": {},
            "bars": false,
            "dashLength": 10,
            "dashes": false,
            "datasource": "prometheus",
            "fill": 1,
            "fillGradient": 0,
            "gridPos": {
              "h": 7,
              "w": 24,
              "x": 0,
              "y": 28
            },
            "hiddenSeries": false,
            "id": 4,
            "legend": {
              "alignAsTable": true,
              "avg": false,
              "current": true,
              "max": false,
              "min": false,
              "rightSide": true,
              "show": true,
              "sort": "total",
              "sortDesc": true,
              "total": false,
              "values": true
            },
            "lines": true,
            "linewidth": 1,
            "nullPointMode": "null",
            "options": {
              "dataLinks": []
            },
            "percentage": false,
            "pointradius": 2,
            "points": false,
            "renderer": "flot",
            "seriesOverrides": [],
            "spaceLength": 10,
            "stack": false,
            "steppedLine": false,
            "targets": [
              {
                "expr": "sum(rate(grpc_server_handled_total{job=\"central-metrics\",grpc_type=\"unary\",grpc_code!=\"OK\",grpc_service=~\"$grpc_service\",grpc_method=~\"$grpc_method\"}[1m])) by (grpc_service)\n / \nsum(rate(grpc_server_started_total{job=\"central-metrics\",grpc_type=\"unary\",grpc_service=~\"$grpc_service\",grpc_method=~\"$grpc_method\"}[1m])) by (grpc_service)\n * 100.0",
                "legendFormat": "{{ grpc_service }}-{{ $grpc_method }}",
                "refId": "A"
              }
            ],
            "thresholds": [],
            "timeFrom": null,
            "timeRegions": [],
            "timeShift": null,
            "title": "unary request error percentage",
            "tooltip": {
              "shared": true,
              "sort": 0,
              "value_type": "individual"
            },
            "type": "graph",
            "xaxis": {
              "buckets": null,
              "mode": "time",
              "name": null,
              "show": true,
              "values": []
            },
            "yaxes": [
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              },
              {
                "format": "short",
                "label": null,
                "logBase": 1,
                "max": null,
                "min": null,
                "show": true
              }
            ],
            "yaxis": {
              "align": false,
              "alignLevel": null
            }
          }
        ],
        "schemaVersion": 22,
        "style": "dark",
        "tags": [],
        "templating": {
          "list": [
            {
              "allValue": "",
              "current": {
                "selected": true,
                "text": "All",
                "value": "$__all"
              },
              "datasource": "prometheus",
              "definition": "label_values(grpc_server_started_total{job=\"central-metrics\",grpc_type=\"unary\"},grpc_service)",
              "hide": 0,
              "includeAll": true,
              "label": "grpc_service",
              "multi": false,
              "name": "grpc_service",
              "options": [],
              "query": "label_values(grpc_server_started_total{job=\"central-metrics\",grpc_type=\"unary\"},grpc_service)",
              "refresh": 1,
              "regex": "",
              "skipUrlSync": false,
              "sort": 0,
              "tagValuesQuery": "",
              "tags": [],
              "tagsQuery": "",
              "type": "query",
              "useTags": false
            },
            {
              "allValue": ".*",
              "current": {
                "selected": false,
                "text": "All",
                "value": "$__all"
              },
              "datasource": "prometheus",
              "definition": "label_values(grpc_server_started_total{job=\"central-metrics\",grpc_type=\"unary\",grpc_service=~\"$grpc_service\"},grpc_method)",
              "hide": 0,
              "includeAll": true,
              "label": "grpc_method",
              "multi": false,
              "name": "grpc_method",
              "options": [],
              "query": "label_values(grpc_server_started_total{job=\"central-metrics\",grpc_type=\"unary\",grpc_service=~\"$grpc_service\"},grpc_method)",
              "refresh": 1,
              "regex": "",
              "skipUrlSync": false,
              "sort": 0,
              "tagValuesQuery": "",
              "tags": [],
              "tagsQuery": "",
              "type": "query",
              "useTags": false
            }
          ]
        },
        "time": {
          "from": "now-3h",
          "to": "now"
        },
        "timepicker": {
          "refresh_intervals": [
            "5s",
            "10s",
            "30s",
            "1m",
            "5m",
            "15m",
            "30m",
            "1h",
            "2h",
            "1d"
          ]
        },
        "timezone": "",
        "title": "central-grpc-server",
        "uid": "Y_CXrDGGztes",
        "version": 2
      }  
  kind: ConfigMap
  metadata:
    name: grafana-dashboard-central-grpc-server-v2
    namespace: monitoring
