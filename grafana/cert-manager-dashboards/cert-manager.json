{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "", "editable": true, "gnetId": null, "graphTooltip": 1, "iteration": 1610447848979, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 20, "panels": [], "title": "All Certificates", "type": "row"}, {"datasource": "$datasource", "description": "The number of certificates in the ready state.", "fieldConfig": {"defaults": {"custom": {}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "True"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "7.0.0", "targets": [{"expr": "sum by (condition) (certmanager_certificate_ready_status)", "interval": "", "legendFormat": "{{condition}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Certificates Ready", "type": "stat"}, {"datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}, "decimals": 1, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1296000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "7.0.0", "targets": [{"expr": "min(certmanager_certificate_expiration_timestamp_seconds) - time()", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"expr": "vector(1250000)", "hide": true, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Soonest Cert Expiry", "type": "stat"}, {"datasource": "$datasource", "description": "Status of the certificates. Values are True, False or Unknown.", "fieldConfig": {"defaults": {"custom": {"align": null, "displayMode": "color-background"}, "decimals": 1, "mappings": [{"from": "", "id": 0, "operator": "", "text": "Yes", "to": "", "type": 1, "value": ""}], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 1296000}]}, "unit": "dtdurations"}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 9}, "id": 9, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "<PERSON>id <PERSON>"}]}, "pluginVersion": "7.0.0", "targets": [{"expr": "avg by (name, condition) (certmanager_certificate_ready_status == 1)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"expr": "sort_desc(avg by (name, namespace, exported_namespace, cluster) (certmanager_certificate_expiration_timestamp_seconds) -time())", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Certificates", "transformations": [{"id": "seriesToColumns", "options": {"byField": "name"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": true, "exported_namespace": false, "namespace": true}, "indexByName": {"Time": 7, "Value #A": 6, "Value #B": 5, "cluster": 0, "condition": 4, "exported_namespace": 2, "name": 3, "namespace": 1}, "renameByName": {"Value #B": "TTL", "cluster": "Cluster", "condition": "Ready Status", "exported_namespace": "Certificate Namespace", "name": "Certificate", "namespace": "Namespace"}}}], "type": "table"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 22, "panels": [], "title": "Applications", "type": "row"}, {"aliasColors": {"max": "dark-yellow"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "CPU Usage and limits, as percent of a vCPU core. ", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 21}, "hiddenSeries": false, "id": 12, "interval": "1m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 250, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pluginVersion": "7.1.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "CPU", "fill": 1, "fillGradient": 5}, {"alias": "/Request.*/", "color": "#FF9830", "dashes": true}, {"alias": "/Limit.*/", "color": "#F2495C", "dashes": true}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg by (pod) (rate(container_cpu_usage_seconds_total{cluster=\"$cluster\",container=\"cert-manager\"}[$__interval]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "CPU {{pod}}", "refId": "A"}, {"expr": "avg by (pod) (kube_pod_container_resource_limits{cluster=\"$cluster\",container=\"cert-manager\",resource=\"cpu\"})", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "Limit {{pod}}", "refId": "B"}, {"expr": "avg by (pod) (kube_pod_container_resource_requests{cluster=\"$cluster\",container=\"cert-manager\",resource=\"cpu\"})", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "Request {{pod}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"max": "dark-yellow"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Percent of the time that the CPU is being throttled. Higher is badderer. ", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 21}, "hiddenSeries": false, "id": 14, "interval": "1m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 250, "nullPointMode": "connected", "options": {"dataLinks": []}, "percentage": false, "pluginVersion": "7.1.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/external-dns.*/", "fill": 1, "fillGradient": 5}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg by (pod) (\n  rate(container_cpu_cfs_throttled_periods_total{cluster=\"$cluster\",container=\"cert-manager\"}[$__interval])\n  /\n  rate(container_cpu_cfs_periods_total{container=\"cert-manager\"}[$__interval])\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Throttling", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"max": "dark-yellow"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Memory utilisation and limits.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 21}, "hiddenSeries": false, "id": 16, "interval": "1m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 250, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pluginVersion": "7.1.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Memory", "fill": 1, "fillGradient": 5}, {"alias": "Request", "color": "#FF9830", "dashes": true}, {"alias": "Limit", "color": "#F2495C", "dashes": true}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg by (pod) (container_memory_usage_bytes{cluster=\"$cluster\",container=\"cert-manager\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Memory {{pod}}", "refId": "A"}, {"expr": "avg by (pod) (kube_pod_container_resource_limits{cluster=\"$cluster\",container=\"cert-manager\",resource=\"memory\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Limit {{pod}}", "refId": "B"}, {"expr": "avg by (pod) (kube_pod_container_resource_requests_memory_bytes{cluster=\"$cluster\",container=\"cert-manager\",resource=\"memory\"})", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Request {{pod}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"max": "dark-yellow"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Network ingress/egress.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 5, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 21}, "hiddenSeries": false, "id": 18, "interval": "1m", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pluginVersion": "7.1.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "transmit", "transform": "negative-Y"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(\n  sum without (interface) (\n    rate(container_network_receive_bytes_total{cluster=\"$cluster\",namespace=\"cert-manager\"}[$__interval])\n  )\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "receive", "refId": "A"}, {"expr": "avg(\n  sum without (interface) (\n    rate(container_network_transmit_bytes_total{cluster=\"$cluster\",namespace=\"cert-manager\"}[$__interval])\n  )\n)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "transmit", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Rate of requests to ACME provider.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 29}, "hiddenSeries": false, "id": 6, "interval": "20s", "legend": {"avg": false, "current": false, "hideEmpty": true, "hideZero": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "maxDataPoints": 250, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pluginVersion": "7.1.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (method, path, status) (\n  rate(certmanager_http_acme_client_request_count{cluster=\"$cluster\"}[$__interval])\n)", "interval": "", "legendFormat": "{{method}} {{path}} {{status}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ACME HTTP Requests/sec", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "reqps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Average duration of requests to ACME provider. ", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 29}, "hiddenSeries": false, "id": 10, "interval": "30s", "legend": {"avg": false, "current": false, "hideEmpty": true, "hideZero": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "maxDataPoints": 250, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pluginVersion": "7.1.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (method, path, status) (rate(certmanager_http_acme_client_request_duration_seconds_sum{cluster=\"$cluster\"}[$__interval]))\n/\nsum by (method, path, status) (rate(certmanager_http_acme_client_request_duration_seconds_count{cluster=\"$cluster\"}[$__interval]))", "interval": "", "legendFormat": "{{method}} {{path}} {{status}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "ACME HTTP Request avg duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The rate of controller sync requests.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 37}, "hiddenSeries": false, "id": 7, "interval": "20s", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "maxDataPoints": 250, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pluginVersion": "7.1.3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (controller) (\n  rate(certmanager_controller_sync_call_count{cluster=\"$cluster\"}[$__interval])\n)", "interval": "", "legendFormat": "{{controller}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Controller Sync Requests/sec", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "reqps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 25, "style": "dark", "tags": ["cert-manager", "infra"], "templating": {"list": [{"current": {"selected": false, "text": "thanos", "value": "thanos"}, "hide": 0, "includeAll": false, "label": "Data Source", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "base/eks/us-west-2", "value": "base/eks/us-west-2"}, "datasource": "$datasource", "definition": "", "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster", "options": [], "query": "label_values(kube_pod_info{namespace=\"cert-manager\"}, cluster)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Cert Manager Dashboard", "uid": "TvuRo2iMk", "version": 15}