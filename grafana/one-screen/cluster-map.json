{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": null, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.width", "value": 184}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_id"}, "properties": [{"id": "unit", "value": "string"}, {"id": "links", "value": [{"targetBlank": true, "title": "Drill down to K8s namespace", "url": "/d/one-screen-tidb/tidb-in-one-screen?${__url_time_range}&var-tenant_name=${__data.fields.tenant_name}&var-cluster_name=${__data.fields.name}&var-cluster_id=${__data.fields.cluster_id}"}]}, {"id": "custom.width", "value": 191}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "shoot"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "Drill down to K8s shoot", "url": "/d/rnogfAznks?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--${__data.fields.namespace}--${__data.fields.shoot}&var-namespace=tidb${__data.fields.cluster_id}"}]}, {"id": "custom.width", "value": 121}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "project_id"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.width", "value": 184}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "status"}, "properties": [{"id": "custom.width", "value": 91}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "version"}, "properties": [{"id": "custom.width", "value": 85}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "provider"}, "properties": [{"id": "custom.width", "value": 99}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "created_at"}, "properties": [{"id": "custom.width", "value": 176}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "created_at"}]}, "pluginVersion": "8.2.3", "targets": [{"exemplar": true, "expr": "sum(dbaas_tidb_cluster_info) by (cluster_id, name, version, created_at, status, tenant, tenant_name, region, shoot, provider, dev_tier, project_id,namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Cluster Map", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "cluster": true, "endpoint": true, "instance": true, "job": true, "namespace": false, "pod": true, "prometheus": true, "receive": true, "replica": true, "service": true, "stage": true, "tenant_id": true}, "indexByName": {"Time": 5, "Value": 13, "cluster_id": 0, "created_at": 3, "dev_tier": 12, "name": 1, "project_id": 8, "provider": 11, "region": 9, "shoot": 10, "status": 4, "tenant": 6, "tenant_name": 7, "version": 2}, "renameByName": {"Value": "cluster count", "cluster": ""}}}], "type": "table"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "unit", "value": "string"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "project"}, "properties": [{"id": "unit", "value": "string"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "", "url": "/d/rnogfAznks?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--${__data.fields.namespace}--${__data.fields.name}"}]}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 9}, "id": 4, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "status"}]}, "pluginVersion": "8.2.3", "targets": [{"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "infra_gardener_resources_shoot_info", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Shoot Map", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "cluster": true, "endpoint": true, "exported_namespace": true, "instance": true, "job": true, "namespace": true, "pod": true, "prometheus": true, "receive": true, "replica": true, "service": true, "tenant_id": true}, "indexByName": {"Time": 0, "Value": 18, "__name__": 1, "cluster": 2, "endpoint": 3, "exported_namespace": 20, "hibernated": 15, "instance": 4, "job": 5, "name": 6, "namespace": 19, "pod": 8, "project": 9, "prometheus": 10, "provider": 11, "receive": 12, "region": 13, "service": 14, "status": 16, "tenant": 7, "tenant_id": 17}, "renameByName": {}}}], "type": "table"}], "schemaVersion": 31, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Cluster Map", "uid": "3px9i92nz", "version": 19}