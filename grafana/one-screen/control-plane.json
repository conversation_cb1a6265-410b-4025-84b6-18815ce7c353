{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 86, "iteration": 1636944140887, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 96, "panels": [], "title": "Overview", "type": "row"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 1}, "hiddenSeries": false, "id": 88, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "probe_success{cluster=\"base/eks/us-west-2\",job=\"blackbox_http_probe\"}", "interval": "", "legendFormat": "domain: {{ instance }} ", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TiDBCloud  Uptime", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:80", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 89, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "avg_over_time(up{job=\"central-metrics\"}[5m])", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Central Uptime", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:80", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "hiddenSeries": false, "id": 90, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "avg_over_time(up{job=\"billing\"}[5m])", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Billing Uptime", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:80", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "h"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 15}, "id": 18, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.2", "targets": [{"exemplar": false, "expr": "dbaas_system_uptime / (60 * 60)", "format": "time_series", "instant": true, "interval": "", "legendFormat": "{{cluster}}  {{namespace}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Central Last Start Time", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "cluster": false, "endpoint": true, "instance": false, "job": true, "namespace": false, "prometheus": true, "provider": true, "receive": true, "region": true, "replica": true, "service": true}, "indexByName": {}, "renameByName": {"Value": "Uptime"}}}], "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 4, "x": 12, "y": 15}, "id": 38, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "count(dbaas_tenant_info{namespace=\"$namespace\",status=\"active\"})", "interval": "", "legendFormat": "Init", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Active Tenant", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 4, "x": 16, "y": 15}, "id": 73, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "sum(dbaas_tidb_cluster_info{status!=\"deletecomplete\",job=\"worker-metrics\"})", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Total TiDB Cluster Count", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 4, "x": 20, "y": 15}, "id": 83, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "sum(dbaas_shoot_status{job=\"worker-metrics\"})", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Shoot K8S Cluster Count", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 0, "y": 24}, "id": 12, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "right", "values": ["percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "tooltip": {"mode": "single"}}, "pluginVersion": "7.5.4", "targets": [{"exemplar": true, "expr": "count(dbaas_tenant_info{status!=\"cleared\"}) by (status)", "interval": "", "legendFormat": "{{ status }}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Tenant Count By Status", "type": "piechart"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 8, "y": 24}, "id": 81, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "right", "values": ["percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "tooltip": {"mode": "single"}}, "pluginVersion": "7.5.4", "targets": [{"exemplar": true, "expr": "count(dbaas_tidb_cluster_info{job=\"worker-metrics\"})by(status)", "instant": true, "interval": "", "legendFormat": "{{ status }}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "TiDB Cluster Count By Status", "type": "piechart"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 14, "w": 8, "x": 16, "y": 24}, "id": 82, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "right", "values": ["percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "tooltip": {"mode": "single"}}, "pluginVersion": "7.5.4", "targets": [{"exemplar": true, "expr": "count(dbaas_shoot_status{job=\"worker-metrics\"})by(status)", "instant": true, "interval": "", "legendFormat": "{{status}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "K8S Cluster Count By Status", "type": "piechart"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "links": [{"title": "Drill down to K8s shoot", "url": "/d/efa86fd1d0c121a26444b636a3f509a8/kubernetes-compute-resources-cluster?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--prod--${__data.fields.shoot}"}], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "shoot"}, "properties": [{"id": "custom.width", "value": 98}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "custom.width", "value": 203}, {"id": "unit", "value": "string"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_name"}, "properties": [{"id": "custom.width", "value": 266}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "region"}, "properties": [{"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "status"}, "properties": [{"id": "custom.width", "value": 102}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "provider"}, "properties": [{"id": "custom.width", "value": 517}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "project_id"}, "properties": [{"id": "unit", "value": "string"}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 38}, "id": 54, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "sum(dbaas_tidb_cluster_info{namespace=\"$namespace\", job=\"worker-metrics\"}) by (region,shoot,tenant,project_id,tenant_name,provider)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "TiDB Cluster Count By Tenant", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {"Time": 0, "Value": 7, "project_id": 3, "provider": 6, "region": 4, "shoot": 5, "tenant": 1, "tenant_name": 2}, "renameByName": {"Value": "cluster count", "cluster": "k8s cluster", "project_id": ""}}}], "type": "table"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "shoot"}, "properties": [{"id": "custom.width", "value": 92}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "custom.width", "value": 167}, {"id": "unit", "value": "string"}, {"id": "links", "value": [{"title": "Drill down to K8s shoot", "url": "/d/efa86fd1d0c121a26444b636a3f509a8/kubernetes-compute-resources-cluster?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--prod--${__data.fields.shoot}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_name"}, "properties": [{"id": "custom.width", "value": 131}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "region"}, "properties": [{"id": "custom.width", "value": 167}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "status"}, "properties": [{"id": "custom.width", "value": 102}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "created_at"}, "properties": [{"id": "custom.width", "value": 234}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_id"}, "properties": [{"id": "unit", "value": "string"}, {"id": "links", "value": [{"title": "Drill down to K8s namespace", "url": "/d/85a562078cdf77779eaa1add43ccec1e/kubernetes-compute-resources-namespace-pods?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--prod--${__data.fields.shoot}&var-namespace=tidb${__data.fields.cluster_id}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "shoot"}, "properties": [{"id": "links", "value": [{"title": "Drill down to K8s shoot", "url": "/d/efa86fd1d0c121a26444b636a3f509a8/kubernetes-compute-resources-cluster?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--prod--${__data.fields.shoot}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "project_id"}, "properties": [{"id": "unit", "value": "string"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "version"}, "properties": [{"id": "custom.width", "value": 96}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "provider"}, "properties": [{"id": "custom.width", "value": 101}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 47}, "id": 84, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "created_at"}]}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "sum(dbaas_tidb_cluster_info) by (cluster_id, name, version, created_at, status, tenant, tenant_name, region, shoot, provider, dev_tier, project_id)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "TiDB Cluster List", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "cluster": true, "endpoint": true, "instance": true, "job": true, "namespace": true, "pod": true, "prometheus": true, "receive": true, "replica": true, "service": true, "stage": true, "tenant_id": true}, "indexByName": {"Time": 5, "Value": 13, "cluster_id": 0, "created_at": 3, "dev_tier": 12, "name": 1, "project_id": 8, "provider": 11, "region": 9, "shoot": 10, "status": 4, "tenant": 6, "tenant_name": 7, "version": 2}, "renameByName": {"Value": "cluster count", "cluster": ""}}}], "type": "table"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 56}, "id": 111, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "right", "values": ["percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "tooltip": {"mode": "single"}}, "pluginVersion": "7.5.4", "targets": [{"exemplar": true, "expr": "count(sum without (replica, pod, instance) (dbaas_tidb_cluster_info{job=\"worker-metrics\"}))by(version)", "hide": false, "interval": "", "legendFormat": "{{ version }}", "refId": "A"}], "title": "TiDB Cluster Version", "type": "piechart"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 56}, "id": 112, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "right", "values": ["percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "tooltip": {"mode": "single"}}, "pluginVersion": "7.5.4", "targets": [{"exemplar": true, "expr": "count(sum without (replica, pod, instance) (dbaas_tidb_cluster_info{job=\"worker-metrics\"}))by(region)", "interval": "", "legendFormat": "{{ region }}", "refId": "A"}], "title": "TiDB Cluster Region", "type": "piechart"}, {"datasource": null, "description": "每秒报警次数最多的五个集群", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "k8s cluster"}, "properties": [{"id": "custom.width", "value": 368}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 64}, "id": 34, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "alerts per second"}]}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "topk(5, sum(increase(prometheus_notifications_sent_total[1h]))by(tenant,cluster))", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"exemplar": true, "expr": "", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Top5 unstable cluster", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {"Time": 0, "Value": 3, "cluster": 2, "tenant": 1}, "renameByName": {"Value": "alerts increase 1hours", "cluster": "k8s cluster", "tenant": "tenant id"}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 72}, "hiddenSeries": false, "id": 122, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate) by (cluster)", "hide": false, "instant": false, "interval": "", "legendFormat": "{{cluster}}", "refId": "A"}, {"exemplar": true, "expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\"}) by (cluster) / sum(kube_node_status_allocatable{resource=\"cpu\"}) by (cluster)", "hide": true, "instant": true, "interval": "", "legendFormat": "{{cluster}} requests", "refId": "B"}, {"exemplar": true, "expr": "sum(kube_pod_container_resource_limits{resource=\"cpu\"}) by (cluster) / sum(kube_node_status_allocatable{resource=\"cpu\"}) by (cluster)", "hide": true, "instant": true, "interval": "", "legendFormat": "{{cluster}} limits", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "K8s Cluster CPU Utilisation", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:189", "format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:190", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 82}, "hiddenSeries": false, "id": 124, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(container_memory_working_set_bytes{container!=\"\"}) by (cluster)  / sum(kube_node_status_allocatable{resource=\"memory\"}) by (cluster)", "interval": "", "legendFormat": "{{cluster}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "K8s Cluster Memory Utilisation", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:373", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:374", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 90}, "hiddenSeries": false, "id": 46, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(kube_node_info) by (cluster)", "interval": "", "legendFormat": "{{cluster}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Kubernetes node size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1207", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:1208", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 98}, "id": 126, "panels": [], "title": "SLI", "type": "row"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 99}, "id": 128, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, rate(dbaas_backup_execution_speed_bytes_bucket[30d]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "DBaaS Backup Execution speed P99 in month", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "noValue": "120", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 99}, "id": 136, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, rate(dbaas_backup_execution_speed_bytes_bucket[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "DBaaS Backup Execution speed in minute", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 107}, "id": 132, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(dbaas_backup_status_count{tenant_name=\"$tenant_name\", status=\"success\"})/sum(dbaas_backup_status_count{tenant_name=\"$tenant_name\"}) ", "interval": "", "legendFormat": "", "refId": "A"}], "title": "DBaaS Backup Success rate", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 107}, "id": 130, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.999, rate(dbaas_backup_execution_speed_bytes_bucket[30d]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "DBaaS Backup Execution speed P99.9 in month", "type": "timeseries"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 115}, "id": 8, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 9}, "hiddenSeries": false, "id": 100, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": " avg_over_time(shoot:availability{shoot_name=\"$shoot\"}[5m])", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Shoot Apiserver Availability", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:80", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "shoot"}, "properties": [{"id": "custom.width", "value": 98}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "custom.width", "value": 167}, {"id": "unit", "value": "string"}, {"id": "links", "value": [{"title": "Drill down to K8s shoot", "url": "/d/efa86fd1d0c121a26444b636a3f509a8/kubernetes-compute-resources-cluster?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--prod--${__data.fields.shoot}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_name"}, "properties": [{"id": "custom.width", "value": 131}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "region"}, "properties": [{"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "status"}, "properties": [{"id": "custom.width", "value": 102}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "created_at"}, "properties": [{"id": "custom.width", "value": null}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_id"}, "properties": [{"id": "unit", "value": "string"}, {"id": "links", "value": [{"title": "Drill down to K8s namespace", "url": "/d/85a562078cdf77779eaa1add43ccec1e/kubernetes-compute-resources-namespace-pods?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--prod--${__data.fields.shoot}&var-namespace=tidb${__data.fields.cluster_id}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "shoot"}, "properties": [{"id": "links", "value": [{"title": "Drill down to K8s shoot", "url": "/d/efa86fd1d0c121a26444b636a3f509a8/kubernetes-compute-resources-cluster?${__url_time_range}&var-cluster=shoot/${__data.fields.provider}/${__data.fields.region}/shoot--prod--${__data.fields.shoot}"}]}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 19}, "id": 94, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "created_at"}]}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "sum without (replica, pod, instance) (dbaas_tidb_cluster_info{tenant=\"$tenant_id\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "TiDB Cluster In Tenant", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "__name__": true, "cluster": true, "endpoint": true, "instance": true, "job": true, "namespace": true, "pod": true, "prometheus": true, "receive": true, "replica": true, "service": true, "stage": true, "tenant_id": true}, "indexByName": {"Time": 6, "Value": 12, "__name__": 13, "cluster": 5, "cluster_id": 0, "created_at": 3, "endpoint": 14, "instance": 15, "job": 16, "name": 1, "namespace": 17, "pod": 18, "prometheus": 19, "provider": 11, "receive": 20, "region": 9, "replica": 21, "service": 22, "shoot": 10, "stage": 23, "status": 4, "tenant": 7, "tenant_id": 24, "tenant_name": 8, "version": 2}, "renameByName": {"Value": "cluster count", "cluster": ""}}}], "type": "table"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 26}, "hiddenSeries": false, "id": 99, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": " avg_over_time(kube_pod_status_phase{phase=\"Running\",namespace=\"shoot--$namespace--$shoot\"}[5m])", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"exemplar": true, "expr": " avg_over_time(kube_pod_status_phase{phase=\"Running\",tenant=\"$tenant_id\",namespace=\"tidb-admin\"}[5m])", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " Control Plane Uptime", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:80", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 36}, "hiddenSeries": false, "id": 101, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{namespace=\"shoot--$namespace--$shoot\"}) by (pod)/\nsum(kube_pod_container_resource_requests{namespace=\"shoot--$namespace--$shoot\",resource=\"cpu\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"exemplar": false, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{namespace=\"tidb-admin\",tenant=\"$tenant_id\"}) by (pod)/\nsum(kube_pod_container_resource_requests{namespace=\"tidb-admin\",tenant=\"$tenant_id\",resource=\"cpu\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [{"$$hashKey": "object:2436", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "K8s Control Plane CPU Usage (By Request)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:79", "format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"$$hashKey": "object:80", "format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 46}, "hiddenSeries": false, "id": 108, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(increase(container_cpu_cfs_throttled_periods_total{namespace=\"shoot--$namespace--$shoot\"}[5m])) by (pod, namespace)/\nsum(increase(container_cpu_cfs_periods_total{namespace=\"shoot--$namespace--$shoot\"}[5m])) by (pod, namespace)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"exemplar": true, "expr": "sum(increase(container_cpu_cfs_throttled_periods_total{namespace=\"tidb-admin\",tenant=\"$tenant_id\"}[5m])) by (pod, namespace)/\nsum(increase(container_cpu_cfs_periods_total{namespace=\"tidb-admin\",tenant=\"$tenant_id\"}[5m])) by ( pod, namespace)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [{"$$hashKey": "object:2622", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.5, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Throttled", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:948", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:949", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "超过 100% 说明没有设置 limit", "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 56}, "hiddenSeries": false, "id": 103, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(container_memory_working_set_bytes{namespace=\"shoot--$namespace--$shoot\"})by(pod)/\nsum(kube_pod_container_resource_limits{namespace=\"shoot--$namespace--$shoot\",resource=\"memory\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"exemplar": true, "expr": "sum(container_memory_working_set_bytes{namespace=\"tidb-admin\",tenant=\"$tenant_id\"})by(pod)/\nsum(kube_pod_container_resource_limits{namespace=\"tidb-admin\",tenant=\"$tenant_id\",resource=\"memory\"}) by (pod)\n", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [{"$$hashKey": "object:2527", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "K8s Control Memory Usage (By Limit) ", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:948", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:949", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 66}, "hiddenSeries": false, "id": 114, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(\n  sum(kubelet_volume_stats_capacity_bytes{namespace=\"shoot--$namespace--$shoot\"})by(cluster,namespace,persistentvolumeclaim)\n  -\n  sum(kubelet_volume_stats_available_bytes{namespace=\"shoot--$namespace--$shoot\"})by(cluster,namespace,persistentvolumeclaim)\n)\n/\nsum(kubelet_volume_stats_capacity_bytes{namespace=\"shoot--$namespace--$shoot\"})by(cluster,namespace,persistentvolumeclaim)\n", "hide": false, "interval": "", "legendFormat": "{{namespace}}/{{persistentvolumeclaim}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:4444", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Storage Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:748", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:749", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "description": "Status of the expiration of the certificates.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "decimals": 1, "mappings": [{"options": {"": {"text": "Yes"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 604800}]}, "unit": "dtdurations"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Certificate"}, "properties": [{"id": "custom.width", "value": 198}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Ready Status"}, "properties": [{"id": "custom.width", "value": 145}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Certificate Namespace"}, "properties": [{"id": "custom.width", "value": 194}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Cluster"}, "properties": [{"id": "custom.width", "value": 354}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 74}, "id": 30, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "avg by (name, condition) (certmanager_certificate_ready_status{tenant=\"$tenant_id\"} == 1)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"exemplar": true, "expr": "avg by (name, namespace, exported_namespace, cluster) (certmanager_certificate_expiration_timestamp_seconds{tenant=\"$tenant_id\"}) -time()", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Certificates Expired In Shoot", "transformations": [{"id": "seriesToColumns", "options": {"byField": "name"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Value #A": true, "Value #B": false, "exported_namespace": false, "namespace": true}, "indexByName": {"Time": 7, "Value #A": 6, "Value #B": 5, "cluster": 0, "condition": 4, "exported_namespace": 2, "name": 3, "namespace": 1}, "renameByName": {"Time": "", "Value #A": "", "Value #B": "TTL", "cluster": "Cluster", "condition": "Ready Status", "exported_namespace": "Namespace", "name": "Certificate", "namespace": ""}}}], "type": "table"}], "title": "Tenants", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 116}, "id": 4, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 3}, "hiddenSeries": false, "id": 42, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{namespace=\"$namespace\", container!=\"POD\"}) by (pod)\n/\nsum(kube_pod_container_resource_requests{namespace=\"$namespace\", container!=\"POD\",resource=\"cpu\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:3200", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Control Plane CPU Usage In Base", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:430", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:431", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 12}, "hiddenSeries": false, "id": 44, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(container_memory_working_set_bytes{namespace=\"$namespace\"}) by (pod)/sum(kube_pod_container_resource_limits{namespace=\"$namespace\",resource=\"memory\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}, {"exemplar": true, "expr": "", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [{"$$hashKey": "object:3384", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Control Plane Memory Usage In Base", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:393", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:394", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 21}, "hiddenSeries": false, "id": 109, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(increase(container_cpu_cfs_throttled_periods_total{namespace=\"$namespace\"}[5m])) by (pod, namespace)/\nsum(increase(container_cpu_cfs_periods_total{namespace=\"$namespace\"}[5m])) by (pod, namespace)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:2622", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.5, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Throttled In Base", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:948", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:949", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 31}, "hiddenSeries": false, "id": 32, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(dbaas_http_duration_ms_count{namespace=\"$namespace\"}[2m])) by (method,url)", "hide": false, "interval": "", "legendFormat": "{{method}}({{ url }})", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Central API QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:409", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:410", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 38}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99,sum(rate(dbaas_http_duration_ms_bucket{namespace=\"$namespace\"}[2m])) by (url,le)) ", "hide": false, "interval": "", "legendFormat": "{{url}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Central API 99 Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:58", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:59", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 46}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(dbaas_http_duration_ms_count{status_code!=\"0\" , status_code!=\"200\"}[2m])) by (status_code, method, url)", "interval": "", "legendFormat": "{{method}}( {{url}}):  {{status_code}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Central API Error", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:486", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:487", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 54}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_executor_statement_total{service=\"db-tidb\", kubernetes_namespace=\"$namespace\"}[1m]))", "interval": "", "legendFormat": "Statement QPS", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Meta DB QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 54}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.999, sum(rate(tidb_server_handle_query_duration_seconds_bucket{kubernetes_namespace=\"$namespace\"}[1m])) by (le))", "interval": "", "legendFormat": "query-999", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket{kubernetes_namespace=\"$namespace\"}[1m])) by (le))", "hide": false, "interval": "", "legendFormat": "query-99", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(tidb_server_handle_query_duration_seconds_bucket{kubernetes_namespace=\"$namespace\"}[1m])) by (le))", "hide": false, "interval": "", "legendFormat": "query-95", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile(0.999, sum(rate(tidb_session_transaction_duration_seconds_bucket{kubernetes_namespace=\"$namespace\"}[1m])) by (le))", "hide": false, "interval": "", "legendFormat": "txn-999", "refId": "D"}, {"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(tidb_session_transaction_duration_seconds_bucket{kubernetes_namespace=\"$namespace\"}[1m])) by (le))", "hide": false, "interval": "", "legendFormat": "txn-999", "refId": "E"}, {"exemplar": true, "expr": "histogram_quantile(0.95, sum(rate(tidb_session_transaction_duration_seconds_bucket{kubernetes_namespace=\"$namespace\"}[1m])) by (le))", "hide": false, "interval": "", "legendFormat": "txn-95", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Meta DB Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:349", "format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:350", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 62}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(tidb_server_connections{service=\"db-tidb\", kubernetes_namespace=\"$namespace\"}) by (instance)", "instant": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Meta DB Connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 62}, "hiddenSeries": false, "id": 52, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum without (node,replica) (kubelet_volume_stats_capacity_bytes{namespace=\"$namespace\"}) -  sum without(node,replica)(kubelet_volume_stats_available_bytes{namespace=\"$namespace\"})", "interval": "", "legendFormat": "{{persistentvolumeclaim}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Meta DB Storage Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:681", "format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:682", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "description": "Status of the expiration of the certificates.", "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "decimals": 1, "mappings": [{"options": {"": {"text": "Yes"}}, "type": "value"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 604800}]}, "unit": "dtdurations"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Certificate"}, "properties": [{"id": "custom.width", "value": 198}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Ready Status"}, "properties": [{"id": "custom.width", "value": 96}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Certificate Namespace"}, "properties": [{"id": "custom.width", "value": 194}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Cluster"}, "properties": [{"id": "custom.width", "value": 354}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 70}, "id": 107, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.5.4", "targets": [{"exemplar": true, "expr": "avg by (name, condition) (certmanager_certificate_ready_status{cluster=~\"base.*\"} == 1)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"exemplar": true, "expr": "avg by (name, namespace, exported_namespace, cluster) (certmanager_certificate_expiration_timestamp_seconds{cluster=~\"base.*\"}) -time()", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Certificates Expired", "transformations": [{"id": "seriesToColumns", "options": {"byField": "name"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Value #A": true, "Value #B": false, "exported_namespace": false, "namespace": true}, "indexByName": {"Time": 7, "Value #A": 6, "Value #B": 5, "cluster": 0, "condition": 4, "exported_namespace": 2, "name": 3, "namespace": 1}, "renameByName": {"Time": "", "Value #A": "", "Value #B": "TTL", "cluster": "Cluster", "condition": "Ready Status", "exported_namespace": "Namespace", "name": "Certificate", "namespace": ""}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 77}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(dbaas_worker_duration_ms_bucket{namespace=\"$namespace\"}[2m])) by (tenant, name, le))", "hide": false, "interval": "", "legendFormat": "{{tenant}}-{{name}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Worker 99 Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:216", "format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:217", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Global Control Plane", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 117}, "id": 86, "panels": [{"datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 3, "w": 11, "x": 0, "y": 4}, "id": 75, "links": [], "options": {"content": "# [DBaaS OPS trail](https://github.com/tidbcloud/ops-trail/commits/main)\n         ", "mode": "markdown"}, "pluginVersion": "7.5.4", "timeFrom": null, "timeShift": null, "title": "OPS Trail", "transparent": true, "type": "text"}, {"datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 3, "w": 13, "x": 11, "y": 4}, "id": 76, "links": [], "options": {"content": "# [DBaaS User ticket](https://pingcap.zendesk.com/groups/360009307934?desc=1&order=created&page=1&select=tickets)\n         ", "mode": "markdown"}, "pluginVersion": "7.5.4", "timeFrom": null, "timeShift": null, "title": "Ticket", "transparent": true, "type": "text"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 7}, "hiddenSeries": false, "id": 80, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [{"$$hashKey": "object:540"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(ALERTS{severity=\"critical\",alertstate=\"firing\"})by(alertname,cluster)", "format": "time_series", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [{"$$hashKey": "object:512", "colorMode": "background6", "fill": false, "fillColor": "rgba(234, 112, 112, 0.12)", "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time"}], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:200", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:201", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "fixed"}, "custom": {"align": null, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 15}, "id": 78, "options": {"showHeader": true, "sortBy": []}, "pluginVersion": "7.5.4", "targets": [{"exemplar": true, "expr": "sum(ALERTS_FOR_STATE{severity=\"critical\"})by(alertname,cluster,tenant,namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Current Alert", "transformations": [{"id": "organize", "options": {"excludeByName": {"Value": true}, "indexByName": {}, "renameByName": {"tenant": ""}}}], "type": "table"}], "title": "<PERSON><PERSON>", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 118}, "id": 48, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 174}, "hiddenSeries": false, "id": 116, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "topk(10,instance:node_cpu_utilisation:rate1m{cluster=~\"base.*|seed.*\"})", "interval": "", "legendFormat": "{{cluster}}/{{instance}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:473", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Utilisation (top10)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:445", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:446", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 184}, "hiddenSeries": false, "id": 118, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "topk(10,instance:node_memory_utilisation:ratio{cluster=~\"base.*|seed.*\"})", "interval": "", "legendFormat": "{{cluster}}/{{instance}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:473", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Utilisation (top10)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:445", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:446", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 194}, "hiddenSeries": false, "id": 117, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "topk(10,instance:node_load1_per_cpu:ratio{cluster=~\"base.*|seed.*\"})", "interval": "", "legendFormat": "{{cluster}}/{{instance}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:473", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 1, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Load1 Per Core (top10)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:445", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:446", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 204}, "hiddenSeries": false, "id": 119, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "topk(10,instance_device:node_disk_io_time_seconds:rate1m{cluster=~\"base.*|seed.*\"})", "interval": "", "legendFormat": "{{cluster}}/{{instance}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:473", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk IO Utilisation (top10)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:445", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:446", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 214}, "hiddenSeries": false, "id": 120, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "1 - node_filesystem_avail_bytes{cluster=~\"base.*|seed.*\"}\n/\nnode_filesystem_size_bytes{cluster=~\"base.*|seed.*\"}", "interval": "", "legendFormat": "{{cluster}}/{{instance}}/{{device}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:473", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Space Utilisation (top10)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:445", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:446", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 224}, "id": 50, "options": {"showHeader": true}, "pluginVersion": "7.5.4", "targets": [{"exemplar": true, "expr": "kube_node_info", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Kubernetes version", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "Value #A": true, "__name__": true, "ignoreAlerts": true, "instance": true, "job": true, "kubeproxy_version": false, "name": true, "pod_cidr": true, "project": true, "prometheus": true, "provider": true, "provider_id": true, "receive": true, "region": true, "replica": true, "seed_api": true, "seed_provider": true, "seed_region": true, "shoot_infra": true, "shoot_name": true, "tenant": true, "tenant_id": true, "type": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}], "title": "Hardware usage", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 119}, "id": 58, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 6}, "hiddenSeries": false, "id": 56, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{namespace=~\"monitoring|logging\",pod!~\"node-ex.*|.*fluent-bit.*\"}) by (cluster,pod)\n/\nsum(kube_pod_container_resource_requests{namespace=~\"monitoring|logging\",pod!~\"node-ex.*|.*fluent-bit.*\",resource=\"cpu\"}) by (cluster,pod)", "interval": "", "legendFormat": "{{cluster}}/{{pod}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:535", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage (By Request)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:490", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:491", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 15}, "hiddenSeries": false, "id": 113, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(increase(container_cpu_cfs_throttled_periods_total{namespace=~\"monitoring|logging\",pod!~\"node-ex.*|.*fluent-bit.*\"}[5m])) by ( pod, namespace)/\nsum(increase(container_cpu_cfs_periods_total{namespace=~\"monitoring|logging\",pod!~\"node-ex.*|.*fluent-bit.*\"}[5m])) by ( pod, namespace)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:2622", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.5, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Throttled", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:948", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:949", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 25}, "hiddenSeries": false, "id": 60, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(container_memory_working_set_bytes{namespace=~\"monitoring|logging\",pod!~\"node-ex.*|.*fluent-bit.*\"}) by (cluster,pod)\n/\nsum(kube_pod_container_resource_limits{namespace=~\"monitoring|logging\",pod!~\"node-ex.*|.*fluent-bit.*\",resource=\"memory\"}) by (cluster,pod)", "interval": "", "legendFormat": "{{cluster}}/{{pod}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:622", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage (By Limit)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:618", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:619", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 33}, "hiddenSeries": false, "id": 62, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "(\n  sum(kubelet_volume_stats_capacity_bytes{namespace=~\"monitoring|logging\"})by(cluster,persistentvolumeclaim)\n  -\n  sum(kubelet_volume_stats_available_bytes{namespace=~\"monitoring|logging\"})by(cluster,persistentvolumeclaim)\n)\n/\nsum(kubelet_volume_stats_capacity_bytes{namespace=~\"monitoring|logging\"})by(cluster,persistentvolumeclaim)\n", "hide": false, "interval": "", "legendFormat": "{{cluster}}/{{persistentvolumeclaim}}", "refId": "A"}], "thresholds": [{"$$hashKey": "object:4444", "colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.9, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Storage Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:748", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:749", "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Monitoring & Logging", "type": "row"}], "refresh": false, "schemaVersion": 30, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": null, "definition": "label_values(dbaas_tenant_info{name=~'.*'},tenant)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "valid_tenant_id", "options": [], "query": {"query": "label_values(dbaas_tenant_info{name=~'.*'},tenant)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "prod", "value": "prod"}, "datasource": "prometheus", "definition": "label_values(kube_pod_container_info{container='central'}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(kube_pod_container_info{container='central'}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "pingcap management portal", "value": "pingcap management portal"}, "datasource": null, "definition": "label_values(dbaas_tenant_info{tenant=~'.*$valid_tenant_id'},name)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_name", "options": [], "query": {"query": "label_values(dbaas_tenant_info{tenant=~'.*$valid_tenant_id'},name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "1", "value": "1"}, "datasource": null, "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "options": [], "query": {"query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "c0744937", "value": "c0744937"}, "datasource": null, "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},shoot)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "shoot", "options": [], "query": {"query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},shoot)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "DBaaS Control Plane", "uid": "dbaas-control-plane", "version": 45}