{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 240, "iteration": 1639382368473, "links": [], "panels": [{"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 10, "panels": [{"datasource": "loki", "gridPos": {"h": 10, "w": 24, "x": 0, "y": 1}, "id": 7, "options": {"dedupStrategy": "none", "enableLogDetails": true, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{container=\"worker\",namespace=\"$namespace\"}", "refId": "A"}], "title": "central-worker", "type": "logs"}, {"datasource": "loki", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 11}, "id": 5, "options": {"dedupStrategy": "none", "enableLogDetails": true, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{container=\"central\"}", "refId": "A"}], "title": "Central", "type": "logs"}, {"datasource": "loki", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 19}, "id": 4, "options": {"dedupStrategy": "none", "enableLogDetails": true, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{namespace=\"$namespace\", container=\"billing\"}", "refId": "A"}], "title": "Billing", "type": "logs"}, {"datasource": "loki", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 27}, "id": 8, "options": {"dedupStrategy": "none", "enableLogDetails": true, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{namespace=\"$namespace\", container=\"management-portal-server\"}", "refId": "A"}], "title": "Management Portal", "type": "logs"}], "title": "App Layer", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 12, "panels": [{"datasource": "loki", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 2}, "id": 14, "options": {"dedupStrategy": "none", "enableLogDetails": true, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{namespace=\"infra\", app=\"aws-provider\"}", "refId": "A"}], "title": "AWS Provider", "type": "logs"}, {"datasource": "loki", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 2}, "id": 15, "options": {"dedupStrategy": "none", "enableLogDetails": true, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{namespace=\"infra\", app=\"gcp-provider\"}", "refId": "A"}], "title": "GCP Provider", "type": "logs"}, {"datasource": "loki", "gridPos": {"h": 11, "w": 24, "x": 0, "y": 10}, "id": 2, "options": {"dedupStrategy": "none", "enableLogDetails": true, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{namespace=\"infra\", app=\"infra-provider\"}", "refId": "A"}], "title": "Infra Provider", "type": "logs"}], "title": "Infra Layer", "type": "row"}], "schemaVersion": 30, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "staging", "value": "staging"}, "datasource": null, "definition": "label_values(kube_pod_container_info{container=\"central\"}, namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "namespace", "options": [], "query": {"query": "label_values(kube_pod_container_info{container=\"central\"}, namespace)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-10m", "to": "now"}, "timepicker": {"hidden": true, "nowDelay": ""}, "timezone": "", "title": "Recent (10m) Control Plane Logs", "uid": "gaxin927k", "version": 2}