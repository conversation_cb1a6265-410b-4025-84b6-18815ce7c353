{"__inputs": [{"name": "DS_THANOS", "label": "thanos", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__elements": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "8.5.4"}, {"type": "panel", "id": "heatmap", "name": "Heatmap", "version": ""}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Profiling performance-related metrics based on controller-runtime.", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 15920, "graphTooltip": 0, "id": null, "iteration": 1662538568264, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 6, "panels": [], "title": "Overview", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 1}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"exemplar": true, "expr": "controller_runtime_active_workers{namespace=\"$Namespace\", service=\"$Service\", pod=\"$Pod\"}", "interval": "", "legendFormat": "{{controller}}", "refId": "A"}], "title": "Active Workers", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 1}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "exemplar": true, "expr": "workqueue_depth{namespace=\"$Namespace\", service=\"$Service\", pod=\"$Pod\"}", "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "Workqueue Depth", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 10}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"exemplar": true, "expr": "sum by (result) (rate(controller_runtime_reconcile_total{namespace=\"$Namespace\", service=\"$Service\", pod=\"$Pod\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{result}}", "refId": "A"}], "title": "Reoncile Rate", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 8, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 2}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"exemplar": true, "expr": "rate(controller_runtime_reconcile_total{namespace=\"$Namespace\", service=\"$Service\", pod=\"$Pod\", controller=\"$Controller\"}[$__rate_interval])", "interval": "", "legendFormat": "{{result}}", "refId": "A"}], "title": "Reconcile Rate", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 11}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "exemplar": true, "expr": "workqueue_depth{namespace=\"$Namespace\", service=\"$Service\", pod=\"$Pod\", name=\"$Controller\"}", "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "workqueue_depth", "type": "timeseries"}, {"cards": {}, "color": {"cardColor": "#b4ff00", "colorScale": "sqrt", "colorScheme": "interpolateOranges", "exponent": 0.5, "mode": "spectrum"}, "dataFormat": "tsbuckets", "gridPos": {"h": 10, "w": 24, "x": 0, "y": 20}, "heatmap": {}, "hideZeroBuckets": false, "highlightCards": true, "id": 12, "legend": {"show": false}, "reverseYBuckets": false, "targets": [{"exemplar": true, "expr": "rate(controller_runtime_reconcile_time_seconds_bucket{namespace='$Namespace', service='$Service', pod='$Pod', controller='$Controller'}[$__rate_interval])", "format": "heatmap", "interval": "", "legendFormat": "{{le}}", "refId": "A"}], "title": "Reconcile Time Buckets", "tooltip": {"show": true, "showHistogram": false}, "type": "heatmap", "xAxis": {"show": true}, "yAxis": {"format": "short", "logBase": 1, "show": true}, "yBucketBound": "auto"}], "repeat": "Controller", "title": "Controller \"$Controller\" Status", "type": "row"}], "refresh": "1m", "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"current": {}, "definition": "label_values(controller_runtime_active_workers, namespace)", "hide": 0, "includeAll": false, "multi": false, "name": "Namespace", "options": [], "query": {"query": "label_values(controller_runtime_active_workers, namespace)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "definition": "label_values(controller_runtime_active_workers{namespace=\"$Namespace\"}, service)", "hide": 0, "includeAll": false, "multi": false, "name": "Service", "options": [], "query": {"query": "label_values(controller_runtime_active_workers{namespace=\"$Namespace\"}, service)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "definition": "label_values(controller_runtime_active_workers{namespace=\"$Namespace\", service=\"$Service\"},  pod)", "hide": 0, "includeAll": false, "multi": false, "name": "Pod", "options": [], "query": {"query": "label_values(controller_runtime_active_workers{namespace=\"$Namespace\", service=\"$Service\"},  pod)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "definition": "label_values(controller_runtime_active_workers{namespace=\"$Namespace\", service=\"$Service\", pod=\"$Pod\"},  controller)", "hide": 0, "includeAll": false, "multi": true, "name": "Controller", "options": [], "query": {"query": "label_values(controller_runtime_active_workers{namespace=\"$Namespace\", service=\"$Service\", pod=\"$Pod\"},  controller)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "controller-runtime", "uid": "5J4pyKEnk", "version": 9, "weekStart": ""}