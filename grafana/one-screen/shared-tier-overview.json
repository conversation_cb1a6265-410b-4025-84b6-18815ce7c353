{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 200, "iteration": 1652774375680, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 8, "panels": [], "title": "TiDB Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "description": "TiDB clusters created count by kubernetes over selected range, including clusters being deleted.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 0, "y": 1}, "id": 43, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "bottom", "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "count by (kubernetes)\n(\navg by (tidb,kubernetes) (max_over_time(dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name|\"}[$__range]))\n* on(tidb) group_left()\ncount by (tidb)(time() - min_over_time(dispatcher_cluster_creation_time[$__range]) < $__range_s)\n)", "format": "time_series", "instant": true, "interval": "", "legendFormat": "{{kubernetes}}", "refId": "A"}], "title": "Clusters Created Per EKS", "transformations": [], "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "description": "TiDB clusters created count over selected range, including clusters being deleted.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 5, "y": 1}, "id": 44, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.5.2", "targets": [{"exemplar": true, "expr": "count\n(\navg by (tidb) (max_over_time(dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name|\"}[$__range]))\n* on(tidb) \ncount by (tidb)(time() - min_over_time(dispatcher_cluster_creation_time[$__range]) < $__range_s)\n)", "format": "time_series", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Clusters Created", "transformations": [], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 10, "y": 1}, "id": 35, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.5.2", "targets": [{"exemplar": true, "expr": "count(avg(dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name|\"}) by (kubernetes,tidb))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "TiDB Clusters Count", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "description": "#Tidb clusters deployed under current EKS", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 1}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(avg(dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name|\"}) by (kubernetes,tidb))", "instant": false, "interval": "", "legendFormat": "Count", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "TiDB Clusters Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:55", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:56", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 5, "y": 9}, "id": 25, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "bottom", "values": ["percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"exemplar": true, "expr": "count by (phase) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\",phase!=\"\"})", "instant": true, "interval": "", "legendFormat": "{{phase}}", "refId": "A"}, {"exemplar": true, "expr": "count (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\",phase=\"\"})", "hide": false, "instant": true, "interval": "", "legendFormat": "Unknown", "refId": "B"}, {"exemplar": true, "expr": "count (dispatcher_cluster_status_phase{kubernetes=~\"\"})", "hide": false, "instant": true, "interval": "", "legendFormat": "Unsched", "refId": "C"}], "title": "Clusters Count By Phase", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 10, "y": 9}, "id": 20, "options": {"displayLabels": ["value"], "legend": {"displayMode": "list", "placement": "bottom", "values": ["percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"exemplar": true, "expr": "count ((label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[$__range])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"}))== 0)", "instant": true, "interval": "", "legendFormat": "Inactive", "refId": "A"}, {"exemplar": true, "expr": "count\n(label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[$__range])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} > 0)", "hide": false, "instant": true, "interval": "", "legendFormat": "Active", "refId": "B"}, {"exemplar": true, "expr": "count (count by (tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name|\"}) unless count by (tidb)(label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[$__range])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\")))", "hide": false, "instant": true, "interval": "", "legendFormat": "Unknown", "refId": "C"}], "title": "Clusters Count By Active Status", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 9}, "id": 37, "options": {"barRadius": 0, "barWidth": 0.7, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "orientation": "horizontal", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "targets": [{"exemplar": true, "expr": "count ((label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[1d])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} and on(tidb) dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} offset 1d))== 0)", "format": "time_series", "instant": true, "interval": "", "legendFormat": "At Least 1d", "refId": "A"}, {"exemplar": true, "expr": "count ((label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[2d])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} and on(tidb) dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} offset 2d))== 0)", "format": "time_series", "hide": false, "instant": true, "interval": "", "legendFormat": "At Least 2d", "refId": "B"}, {"exemplar": true, "expr": "count ((label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[3d])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} and on(tidb) dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} offset 3d))== 0)", "hide": false, "instant": true, "interval": "", "legendFormat": "At Least 3d", "refId": "C"}, {"exemplar": true, "expr": "count ((label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[4d])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} and on(tidb) dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} offset 4d))== 0)", "hide": false, "instant": true, "interval": "", "legendFormat": "At Least 4d", "refId": "F"}, {"exemplar": true, "expr": "count ((label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[5d])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} and on(tidb) dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} offset 5d))== 0)", "hide": false, "instant": true, "interval": "", "legendFormat": "At Least 5d", "refId": "D"}, {"exemplar": true, "expr": "count ((label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[6d])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} and on(tidb) dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} offset 6d))== 0)", "hide": false, "instant": true, "interval": "", "legendFormat": "At Least 6d", "refId": "E"}, {"exemplar": true, "expr": "count ((label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[7d])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} and on(tidb) dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"} offset 7d))== 0)", "format": "time_series", "hide": false, "instant": true, "interval": "", "legendFormat": "At Least 7d", "refId": "G"}], "title": "Inactive Clusters Count", "transformations": [{"id": "reduce", "options": {"includeTimeField": false, "mode": "seriesToRows", "reducers": ["last"]}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Field": "", "Last": "Count"}}}], "type": "barchart"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "TiDB Cluster"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "Jump to one screen Tidb", "url": "./d/p1FdsDc7z/troubleshoot-dev-tier-cluster-creation?orgId=1&var-provider-type=${provider_type}&var-cluster_k8s=${__data.fields[\"EKS Cluster\"]}&var-cluster_id=${__value.text}"}]}, {"id": "custom.filterable", "value": true}, {"id": "custom.width", "value": 173}, {"id": "unit", "value": "string"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "EKS Cluster"}, "properties": [{"id": "noValue", "value": "⚠️Unsheduled"}, {"id": "custom.width"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Phase"}, "properties": [{"id": "mappings", "value": [{"options": {"Creating": {"color": "yellow", "index": 2}, "Pending": {"color": "yellow", "index": 1}, "Unavailable": {"color": "red", "index": 0}}, "type": "value"}]}, {"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}, {"id": "custom.displayMode", "value": "color-background"}, {"id": "custom.width"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Active Status"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "mappings", "value": [{"options": {"0": {"color": "green", "index": 1, "text": "Inactive"}}, "type": "value"}, {"options": {"from": 0, "result": {"color": "blue", "index": 0, "text": "Active"}, "to": 10000000}, "type": "range"}, {"options": {"match": "null+nan", "result": {"color": "yellow", "index": 2, "text": "Unknown"}}, "type": "special"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "UpTime"}, "properties": [{"id": "unit", "value": "dtdhms"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Cluster Name"}, "properties": [{"id": "noValue"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Tenant Name"}, "properties": [{"id": "noValue"}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 17}, "id": 14, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "sum (avg (label_replace(up{component=\"tidb\",provider_type=~\"$provider_type\"},\"tidb\",\"$1\",\"tidb_cluster\",\".*db(.*)\") * on(tidb)  group_left(kubernetes) dispatcher_cluster_status_phase{provider_type=~\"$provider_type\",kubernetes=~\"$cluster_name\"}) by (instance,kubernetes,tidb)) by (kubernetes,tidb)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "sum (avg(label_replace(up{component=\"pd\",provider_type=~\"$provider_type\"},\"tidb\",\"$1\",\"tidb_cluster\",\".*db(.*)\") * on(tidb)  group_left(kubernetes) dispatcher_cluster_status_phase{provider_type=~\"$provider_type\",kubernetes=~\"$cluster_name\"}) by (instance,kubernetes,tidb)) by (kubernetes,tidb)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "sum (avg(label_replace(up{component=\"tikv\",provider_type=~\"$provider_type\"},\"tidb\",\"$1\",\"tidb_cluster\",\".*db(.*)\") * on(tidb)  group_left(kubernetes) dispatcher_cluster_status_phase{provider_type=~\"$provider_type\",kubernetes=~\"$cluster_name\"}) by (instance,kubernetes,tidb)) by (kubernetes,tidb)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "sum(avg (label_replace(up{component=\"tiflash\",provider_type=~\"$provider_type\"},\"tidb\",\"$1\",\"tidb_cluster\",\".*db(.*)\") * on(tidb)  group_left(kubernetes) dispatcher_cluster_status_phase{provider_type=~\"$provider_type\",kubernetes=~\"$cluster_name\"}) by (instance,kubernetes,tidb)) by (kubernetes,tidb)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "count (dispatcher_cluster_status_phase{provider_type=~\"$provider_type\",kubernetes=~\"$cluster_name|\"}==1) by(kubernetes,tidb,phase)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "label_replace(sum(increase(tidb_executor_statement_total{cluster_id!=\"\"}[$__range])) by (cluster_id),\"tidb\",\"$1\",\"cluster_id\",\"(.*)\") * on(tidb) dispatcher_cluster_status_phase{provider_type=~\"$provider_type\",kubernetes=~\"$cluster_name\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "avg by (kubernetes,tidb) ((time() - dispatcher_cluster_creation_time) * on(namespace,tidb) group_left(kubernetes) dispatcher_cluster_status_phase{provider_type=~\"$provider_type\",kubernetes=~\"$cluster_name\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "G"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "avg by (tidb,name,tenant_name) (label_replace(dbaas_tidb_cluster_info{dev_tier=\"true\"},\"tidb\",\"$1\",\"cluster_id\",\"(.*)\")) * on(tidb) group_left() dispatcher_cluster_status_phase{provider_type=~\"$provider_type\",kubernetes=~\"$cluster_name\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "H"}], "title": "TiDB Cluster Info", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #E": true, "Value #H": true}, "indexByName": {"Time": 0, "Value #A": 6, "Value #B": 7, "Value #C": 8, "Value #D": 9, "Value #E": 11, "Value #F": 10, "Value #G": 12, "Value #H": 13, "kubernetes": 1, "name": 3, "phase": 5, "tenant_name": 4, "tidb": 2}, "renameByName": {"Value #A": "TiDB Count", "Value #B": "PD Count", "Value #C": "TiKV Count", "Value #D": "TiFlash Count", "Value #F": "Active Status", "Value #G": "UpTime", "kubernetes": "EKS Cluster", "name": "Cluster Name", "phase": "Phase", "tenant_name": "Tenant Name", "tidb": "TiDB Cluster"}}}], "type": "table"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 16, "panels": [], "title": "Control Plane", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Kubernetes"}, "properties": [{"id": "custom.filterable", "value": true}, {"id": "noValue", "value": "⚠️Unscheduled"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "TiDB Cluster Count"}, "properties": [{"id": "custom.width"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 27}, "id": 18, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Network Count"}]}, "pluginVersion": "8.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "editorMode": "code", "exemplar": true, "expr": "count by (kubernetes) (dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name|\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "Cluster"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "count by (kubernetes) (dispatcher_network_status_phase{kubernetes=~\"$cluster_name|\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "count by (kubernetes) (dispatcher_restore_status_phase{kubernetes=~\"$cluster_name|\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "count by (kubernetes) (dispatcher_backup_status_phase{kubernetes=~\"$cluster_name|\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "count by (kubernetes) (dispatcher_import_status_phase{kubernetes=~\"$cluster_name|\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "E"}], "title": "Infra API Resources", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": false}, "indexByName": {"Time": 1, "Value #A": 2, "Value #B": 3, "kubernetes": 0}, "renameByName": {"Value #A": "TiDB Cluster Count", "Value #B": "Network Count", "Value #C": "Restore Count", "Value #D": "Backup Count", "Value #E": "Import Count", "kubernetes": "Kubernetes"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": true, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "image"}, "properties": [{"id": "custom.width", "value": 727}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster"}, "properties": [{"id": "custom.width", "value": 207}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 35}, "id": 41, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "8.5.2", "targets": [{"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "sum(kube_pod_container_info{cluster=~\".*tier/eks/$cluster_name\", namespace=~\"tidb-admin\"}) by (cluster,namespace, pod, container, image, image_id)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "exemplar": true, "expr": "sum(kube_pod_container_info{cluster=~\".*base.*\", namespace=\"shared-tier\"}) by (cluster,namespace, pod, container, image, image_id)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Shared Tier Control Plane Images", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value #A": true, "Value #B": true}, "indexByName": {"Time": 0, "Value #A": 7, "Value #B": 8, "cluster": 1, "container": 4, "image": 5, "image_id": 6, "namespace": 2, "pod": 3}, "renameByName": {"cluster": "Kubernetes", "container": ""}}}], "type": "table"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 43}, "id": 2, "panels": [{"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "description": "In order to count all the clusters exposing by both dispatcher_cluster_status_phase and kubernetes metrics.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 44}, "id": 10, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"exemplar": true, "expr": "count(count(label_replace(up{job=\"kube-state-metrics\",cluster=~\".*tier/eks/$cluster_name\",provider_type=~\"$provider_type\"},\"kubernetes\",\"$1\",\"cluster\",\"[[tier_prefix]]tier/eks/(.*)\") or dispatcher_cluster_status_phase{kubernetes=~\"$cluster_name\"}) by (kubernetes))", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "EKS Clusters", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Up(apiserver)"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "Jump to apiserver Status", "url": "./d/09ec8aa1e996d6ffcd6817bbaff4db1b/kubernetes-api-server?orgId=1&refresh=10s&var-cluster=${tier_prefix}tier/eks/${__data.fields.kubernetes}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "EKS Cluster"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "Jump to Cluster Compute Resources", "url": "./d/efa86fd1d0c121a26444b636a3f509a8/kubernetes-compute-resources-cluster?orgId=1&refresh=10s&var-cluster=${tier_prefix}tier/eks/${__value.text}"}]}, {"id": "custom.filterable", "value": true}]}]}, "gridPos": {"h": 8, "w": 18, "x": 6, "y": 44}, "id": 12, "options": {"frameIndex": 1, "showHeader": true, "sortBy": [{"desc": true, "displayName": "NetWork Count"}]}, "pluginVersion": "8.0.7", "targets": [{"exemplar": true, "expr": "count(label_replace(kube_node_info{cluster=~\".*tier/eks/$cluster_name\"},\"kubernetes\",\"$1\",\"cluster\",\".*tier/eks/(.*)\")) by (kubernetes)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"exemplar": true, "expr": "count(label_replace(up{job=\"apiserver\",cluster=~\".*tier/eks/$cluster_name\"},\"kubernetes\",\"$1\",\"cluster\",\".*tier/eks/(.*)\")) by (kubernetes)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"exemplar": true, "expr": "sum(count(dispatcher_network_status_phase{kubernetes=~\"$cluster_name\"}) without (phase)) by (kubernetes)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"exemplar": true, "expr": "sum(label_replace(increase(prometheus_notifications_sent_total{cluster=~\".*tier/eks/$cluster_name\"}[$__range]),\"kubernetes\",\"$1\",\"cluster\",\".*tier/eks/(.*)\")) by (kubernetes)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "D"}], "title": "Kubernetes Cluster Info", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Node Count", "Value #B": "Up(apiserver)", "Value #C": "Network Count", "Value #D": "<PERSON><PERSON>s Over Selected Range", "kubernetes": "EKS Cluster"}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 52}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (cluster) (namespace:kube_pod_container_resource_requests_cpu_cores:sum{cluster=~\".*tier/eks/$cluster_name\"})", "interval": "", "legendFormat": "{{cluster}}-Requests", "refId": "A"}, {"exemplar": true, "expr": "sum by (cluster) (kube_node_status_allocatable_cpu_cores{cluster=~\".*tier/eks/$cluster_name\"})", "hide": false, "interval": "", "legendFormat": "{{cluster}}-Allocatable", "refId": "B"}, {"exemplar": true, "expr": "cluster:node_cpu:sum_rate5m{cluster=~\".*tier/eks/$cluster_name\"}", "hide": false, "interval": "", "legendFormat": "{{cluster}}-Actual", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Cluster CPU Cores Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:458", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:459", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 52}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (cluster) (namespace:kube_pod_container_resource_requests_memory_bytes:sum{cluster=~\".*tier/eks/$cluster_name\"})", "interval": "", "legendFormat": "{{cluster}}-Requests", "refId": "A"}, {"exemplar": true, "expr": "sum by (cluster) (kube_node_status_allocatable_memory_bytes{cluster=~\".*tier/eks/$cluster_name\"})", "hide": false, "interval": "", "legendFormat": "{{cluster}}-Allocatable", "refId": "B"}, {"exemplar": true, "expr": "sum by (cluster) (namespace:container_memory_usage_bytes:sum{cluster=~\".*tier/eks/$cluster_name\"})", "hide": false, "interval": "", "legendFormat": "{{cluster}}-Actual", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Cluster Memory Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:702", "format": "bytes", "logBase": 1, "show": true}, {"$$hashKey": "object:703", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1}, "mappings": [], "max": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 60}, "id": 39, "options": {"barWidth": 0.97, "groupWidth": 0.6, "legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "orientation": "horizontal", "showValue": "always", "text": {"valueSize": 10}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "sum by (cluster,node) (kube_pod_container_resource_requests_cpu_cores{cluster=~\".*tier/eks/$cluster_name\"}) / sum by (cluster,node) (kube_node_status_allocatable_cpu_cores{cluster=~\".*tier/eks/$cluster_name\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"exemplar": true, "expr": "sum by (cluster,node) (kube_pod_container_resource_requests_memory_bytes{cluster=~\".*tier/eks/$cluster_name\"}) / sum by (cluster,node) (kube_node_status_allocatable_memory_bytes{cluster=~\".*tier/eks/$cluster_name\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Node Request Resources", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "cluster": true}, "indexByName": {}, "renameByName": {"Value #A": "CPU", "Value #B": "Memory"}}}, {"id": "sortBy", "options": {"fields": {}, "sort": [{"desc": false, "field": "CPU"}]}}], "type": "barchart"}], "title": "EKS Overview", "type": "row"}], "refresh": "", "schemaVersion": 36, "style": "dark", "tags": ["Shared Tier"], "templating": {"list": [{"current": {"selected": true, "text": "aws-shared-tier", "value": "aws-shared-tier"}, "hide": 0, "includeAll": true, "label": "Provider", "multi": false, "name": "provider_type", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": true, "text": "aws-shared-tier", "value": "aws-shared-tier"}, {"selected": false, "text": "gcp-shared-tier", "value": "gcp-shared-tier"}], "query": "aws-shared-tier,gcp-shared-tier", "queryValue": "", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "ka5SznJnz"}, "definition": "query_result(count (max_over_time(dispatcher_cluster_status_phase{provider_type=~\"$provider_type\", namespace!~\".*e2e.*\",tidb!~\"e2e-test-.*\"}[$__range]) or label_replace(up{cluster!~\"db.*\",provider_type=~\"$provider_type\"},\"kubernetes\",\"$1\",\"cluster\",\".*tier/eks/(.*)\")) by (kubernetes))", "hide": 0, "includeAll": true, "label": "Kubernetes", "multi": true, "name": "cluster_name", "options": [], "query": {"query": "query_result(count (max_over_time(dispatcher_cluster_status_phase{provider_type=~\"$provider_type\", namespace!~\".*e2e.*\",tidb!~\"e2e-test-.*\"}[$__range]) or label_replace(up{cluster!~\"db.*\",provider_type=~\"$provider_type\"},\"kubernetes\",\"$1\",\"cluster\",\".*tier/eks/(.*)\")) by (kubernetes))", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/.*kubernetes=\"(.*)\".*/", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Shared Clusters Overview", "uid": "LvCVNFu7z", "version": 5, "weekStart": ""}