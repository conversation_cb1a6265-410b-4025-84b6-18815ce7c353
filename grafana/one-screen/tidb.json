{"annotations": {"list": [{"builtIn": 1, "datasource": "tidb-cluster", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "iteration": 1645071816565, "links": [], "panels": [{"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 174, "panels": [{"columns": [{"text": "Max", "value": "max"}], "datasource": "tidb-cluster", "editable": true, "error": false, "fontSize": "100%", "gridPos": {"h": 7, "w": 12, "x": 0, "y": 9}, "hideTimeOverride": true, "id": 146, "links": [], "pageSize": null, "scroll": false, "showHeader": true, "sort": {"col": null, "desc": false}, "styles": [{"alias": "Count", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "Max", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "Service", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": -3, "mappingType": 1, "pattern": "Metric", "thresholds": [], "type": "string", "unit": "short"}], "targets": [{"expr": "count(process_cpu_seconds_total{cluster_id=~\".*$cluster_id\"} > 0) by (component)", "format": "time_series", "hide": false, "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "B", "step": 20}, {"expr": "count(tiflash_proxy_process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tiflash\"} > 0)", "hide": false, "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "tiflash", "refId": "C"}, {"expr": "count(node_memory_MemTotal_bytes{instance=~\".*$node_instance\"} > 0)", "format": "time_series", "hide": false, "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Node_exporter", "refId": "A", "step": 20}], "timeFrom": "1s", "title": "Latest Up Service", "transform": "timeseries_aggregations", "type": "table-old"}, {"cacheTimeout": null, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 12, "y": 9}, "id": 65, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\", type=\"store_up_count\"})", "format": "time_series", "interval": "15s", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Normal stores", "type": "stat"}, {"cacheTimeout": null, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 16, "y": 9}, "hideTimeOverride": false, "id": 163, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"exemplar": true, "expr": "sum(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"})", "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 60}], "title": "TiKV Storage Size", "type": "stat"}, {"cacheTimeout": null, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 9}, "id": 27, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"expr": "sum(tikv_store_size_bytes{cluster_id=~\".*$cluster_id\", type=\"capacity\"}) ", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 60}], "title": "TiKV Storage Capacity", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The value 1 indicate the service is down.", "editable": true, "error": false, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 166, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "1 - (label_replace((kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"} * on(cluster,node) group_left(provider_id) kube_node_info),\"az_id\",  \"$1\", \"provider_id\", \"(.*/)(\\\\w+.*)\")\n* on(pod,namespace) group_right(az_id)\nlabel_replace(label_replace(up{component=~\"tidb|tikv|pd|tiflash\",cluster_id=~\".*$cluster_id\"},\"pod\",  \"$0\", \"instance\", \".*\"),\"namespace\",  \"$0\", \"kubernetes_namespace\", \".*\") == 0)", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}--{{az_id}}", "refId": "A", "step": 10}, {"expr": "1- (up{instance=~\".*$node_instance\", component=\"node-exporter\"} == 0)", "hide": false, "interval": "$interval", "legendFormat": "node-exporter--{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Down Service", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"cacheTimeout": null, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 12, "y": 16}, "id": 30, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\",type=\"leader_count\"})", "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 60}], "title": "Number of Regions", "type": "stat"}, {"cacheTimeout": null, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 16, "y": 16}, "hideTimeOverride": false, "id": 28, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"expr": "sum(tiflash_system_current_metric_StoreSizeUsed{cluster_id=~\".*$cluster_id\"})", "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 60}], "title": "TiFlash Storage Size", "type": "stat"}, {"cacheTimeout": null, "datasource": "tidb-cluster", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 16}, "id": 164, "interval": null, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"expr": "sum(tiflash_system_current_metric_StoreSizeCapacity{cluster_id=~\".*$cluster_id\"})", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A", "step": 60}], "title": "TiFlash Storage Capacity", "type": "stat"}, {"columns": [{"text": "Current", "value": "current"}], "datasource": "tidb-cluster", "editable": true, "error": false, "fontSize": "100%", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 23}, "hideTimeOverride": true, "id": 90, "links": [], "pageSize": null, "scroll": false, "showHeader": true, "sort": {"col": null, "desc": false}, "styles": [{"align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Metric", "sanitize": false, "type": "string"}, {"align": "auto", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "decimals": 0, "pattern": "Current", "thresholds": ["1", "2"], "type": "number", "unit": "short"}], "targets": [{"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\", type=\"store_disconnected_count\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Disconnect Stores", "refId": "B", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\", type=\"store_unhealth_count\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Unhealth Stores", "refId": "C", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\", type=\"store_low_space_count\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "LowSpace Stores", "refId": "D", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\", type=\"store_down_count\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Down Stores", "refId": "E", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\", type=\"store_offline_count\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Offline Stores", "refId": "F", "step": 20}, {"expr": "sum(pd_cluster_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\", type=\"store_tombstone_count\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Tombstone Stores", "refId": "G", "step": 20}], "timeFrom": "1s", "title": "Abnormal stores", "transform": "timeseries_aggregations", "type": "table-old"}, {"datasource": "tidb-cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "durations"}, "properties": [{"id": "unit", "value": "dtdurations"}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 23}, "id": 157, "links": [], "options": {"showHeader": true}, "pluginVersion": "8.0.7", "targets": [{"exemplar": true, "expr": "dbaas_backup_execution_status_duration_seconds{cluster_id=~\".*$cluster_id\"}", "format": "table", "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Backup/Restore Duration", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true, "cluster": true, "cluster_id": true, "cluster_name": true, "endpoint": true, "instance": true, "job": true, "namespace": true, "pod": true, "prometheus": true, "provider": true, "receive": true, "region": true, "replica": true, "service": true, "tenant_id": true, "tenant_name": true}, "indexByName": {}, "renameByName": {"Value": "durations"}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "It records the unusual Regions' count which may include pending peers, down peers, extra peers, offline peers, missing peers or learner peers", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 31}, "hiddenSeries": false, "id": 148, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "pd_regions_status{cluster_id=~\".*$cluster_id\", instance=\"$pd_instance\", type!~\"learner-peer-region-count|empty-region-count\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [{"colorMode": "warning", "fill": true, "line": true, "op": "gt", "value": 100, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Region health", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The number of Regions on each TiKV instance", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 32}, "hiddenSeries": false, "id": 144, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_raftstore_region_count{cluster_id=~\".*$cluster_id\", type=\"region\"}) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Region Count", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 40}, "hiddenSeries": false, "id": 168, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(pd_regions_abnormal_peer_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (type,le))", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(tikv_raftstore_peer_pending_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) BY (le))", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "pending-peer", "refId": "B", "step": 15}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unhealthy Region Duration", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The storage size per TiKV instance", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 9, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "hiddenSeries": false, "id": 142, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Store size", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": " \tThe number of leaders on each TiKV instance", "editable": true, "error": false, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 49}, "hiddenSeries": false, "id": 150, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(tikv_raftstore_region_count{cluster_id=~\".*$cluster_id\", type=\"leader\"}) by (instance)", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Leader Count", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Overview", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 162, "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "left", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 12}, "id": 140, "options": {"showHeader": true}, "pluginVersion": "8.0.7", "targets": [{"expr": "(count(kube_pod_info{namespace=~\".*$cluster_id\"}) by (pod,node)) \n* on(pod) group_left \nlabel_replace(avg(process_resident_memory_bytes{cluster_id=~\".*$cluster_id\"}) by (instance), \"pod\",  \"$0\", \"instance\", \".*\")", "format": "table", "hide": false, "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Pod -> Node Mapping", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {"Time": 0, "Value": 3, "node": 2, "pod": 1}, "renameByName": {"Time": "", "Value": "pod-current-resident-memory"}}}], "type": "table"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "left", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 12}, "id": 158, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "pod"}]}, "pluginVersion": "8.0.7", "targets": [{"exemplar": false, "expr": "count(kube_pod_info{namespace=~\".*$cluster_id\", node=~\".*$master_node_instance\"}) by (node,pod)", "format": "table", "hide": false, "instant": true, "interval": "$interval", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Node -> Pod Mapping", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true}, "indexByName": {"Time": 0, "Value": 3, "node": 1, "pod": 2}, "renameByName": {"Time": "", "Value": ""}}}], "type": "table"}], "title": "Node Mapping", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 93, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 39}, "hiddenSeries": false, "id": 95, "legend": {"alignAsTable": "true", "avg": true, "current": false, "max": true, "min": false, "rightSide": "true", "show": "true", "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\navg(100 - irate(node_cpu_seconds_total{mode=\"idle\", cluster=~\".*$shoot_cluster\"}[$interval]) * 100) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "{{pod}} - {{instance}}", "refId": "B"}, {"expr": "avg(100 - irate(node_cpu_seconds_total{mode=\"idle\", instance=~\".*$master_node_instance\", cluster=~\".*$shoot_cluster\"}[$interval]) * 100) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "{{instance}}", "refId": "C"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Utilization", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": 100, "min": 0, "show": true}, {"format": "percent", "label": null, "logBase": 1, "max": 100, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 39}, "hiddenSeries": false, "id": 152, "legend": {"alignAsTable": "true", "avg": true, "current": false, "max": true, "min": false, "rightSide": "true", "show": "true", "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/Logical/", "color": "#FF9830", "hideTooltip": true, "legend": false, "linewidth": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\", pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\navg(node_load1{job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "{{pod}} - {{instance}}", "refId": "B"}, {"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\", pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\ncount(node_cpu_seconds_total{job=\"node-exporter\", mode=\"idle\", cluster=~\".*$shoot_cluster\"}) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "Logical Core {{pod}} - {{instance}}", "refId": "C"}, {"exemplar": true, "expr": "node_load1{job=\"node-exporter\", instance=~\"$master_node_instance\", cluster=~\".*$shoot_cluster\"}", "hide": false, "interval": "$interval", "legendFormat": "{{ instance }}", "refId": "E"}, {"exemplar": true, "expr": "count(node_cpu_seconds_total{job=\"node-exporter\", instance=~\"$master_node_instance\", mode=\"idle\", cluster=~\".*$shoot_cluster\"}) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "Logical Core {{instance}}", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Load Average-1m", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 49}, "hiddenSeries": false, "id": 97, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "( ( max(node_memory_MemTotal_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}) by (instance) \r\n  - max(node_memory_MemFree_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}) by (instance)\r\n  - max(node_memory_Buffers_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}) by (instance)\r\n  - max(node_memory_Cached_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}) by (instance) ) \r\n  / max(node_memory_MemTotal_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}) by (instance) )\r\n* on(instance)\r\ngroup_right  label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")", "hide": false, "interval": "$interval", "legendFormat": "{{pod}} - {{instance}}", "refId": "F"}, {"expr": "( ( max(node_memory_MemTotal_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\", instance=~\".*$master_node_instance\"}) by (instance)  \r\n  - max(node_memory_MemFree_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\", instance=~\".*$master_node_instance\"}) by (instance)\r\n  - max(node_memory_Buffers_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\", instance=~\".*$master_node_instance\"}) by (instance)\r\n  - max(node_memory_Cached_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\", instance=~\".*$master_node_instance\"}) by (instance) ) \r\n  / max(node_memory_MemTotal_bytes{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\", instance=~\".*$master_node_instance\"}) by (instance) )", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "H"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 49}, "hiddenSeries": false, "id": 103, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "read", "yaxis": 1}, {"alias": "io time", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\", pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\nsum(rate(node_disk_io_time_seconds_total{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "{{pod}} - {{instance}}", "refId": "A"}, {"expr": "sum(rate(node_disk_io_time_seconds_total{ job=\"node-exporter\",  instance=~\"$master_node_instance\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 0.8, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk I/O Util", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": "", "logBase": 1, "max": "1", "min": "0", "show": true}, {"format": "ms", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 59}, "hiddenSeries": false, "id": 171, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/Write/", "transform": "negative-Y"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\", pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\nsum(rate(node_disk_reads_completed_total{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "-Read: {{pod}} - {{instance}}", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(node_disk_reads_completed_total{ job=\"node-exporter\",  instance=~\"$master_node_instance\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "-Read: {{instance}}", "refId": "B"}, {"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\", pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\nsum(rate(node_disk_writes_completed_total{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "+Write: {{pod}} - {{instance}}", "refId": "C"}, {"exemplar": true, "expr": "sum(rate(node_disk_writes_completed_total{ job=\"node-exporter\",  instance=~\"$master_node_instance\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "+Write: {{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk IOPS", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "iops", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "iops", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 59}, "hiddenSeries": false, "id": 101, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "seriesOverrides": [{"alias": "/Read/", "transform": "negative-Y"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\nsum(rate(node_disk_read_bytes_total{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "-Read: {{pod}} - {{instance}}", "refId": "C"}, {"exemplar": true, "expr": "sum(rate(node_disk_read_bytes_total{ job=\"node-exporter\", instance=~\".*$master_node_instance\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "-Read: {{instance}}", "refId": "D"}, {"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\nsum(rate(node_disk_written_bytes_total{ job=\"node-exporter\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "+Write: {{pod}} - {{instance}}", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(node_disk_written_bytes_total{ job=\"node-exporter\", instance=~\".*$master_node_instance\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "+Write: {{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Throughput", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 69}, "hiddenSeries": false, "id": 99, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\nsum(rate(node_network_receive_bytes_total{ job=\"node-exporter\", device != \"lo\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "{{pod}} - {{instance}}", "refId": "C"}, {"expr": "sum(rate(node_network_receive_bytes_total{ job=\"node-exporter\", instance=~\".*$master_node_instance\", device != \"lo\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "legendFormat": "{{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network Received", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 69}, "hiddenSeries": false, "id": 172, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")\n* on(instance)\ngroup_left\nsum(rate(node_network_transmit_bytes_total{ job=\"node-exporter\", device!=\"lo\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{pod}} - {{instance}}", "refId": "C"}, {"expr": "sum(rate(node_network_transmit_bytes_total{ job=\"node-exporter\", instance=~\".*$master_node_instance\", device!=\"lo\", cluster=~\".*$shoot_cluster\"}[$interval])) by (instance)", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network Transmitted", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 79}, "hiddenSeries": false, "id": 105, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "paceLength": 10, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "((max by(instance) (node_filesystem_size_bytes{device!~\"rootfs|tmpfs\", cluster=~\".*$shoot_cluster\"}) -\n max by(instance) (node_filesystem_avail_bytes{device!~\"rootfs|tmpfs\", cluster=~\".*$shoot_cluster\"})) /\n max by(instance) (node_filesystem_size_bytes{device!~\"rootfs|tmpfs\", cluster=~\".*$shoot_cluster\"})) * 100 * on(instance)\ngroup_right  label_replace(count(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"}) by (node,pod), \"instance\",  \"$0\", \"node\", \".*\")", "hide": false, "interval": "$interval", "legendFormat": "{{pod}} - {{instance}}", "refId": "B"}, {"expr": "((max by(instance) (node_filesystem_size_bytes{device!~\"rootfs|tmpfs\", instance=~\"$master_node_instance\", cluster=~\".*$shoot_cluster\"}) -\n max by(instance) (node_filesystem_avail_bytes{device!~\"rootfs|tmpfs\", instance=~\"$master_node_instance\", cluster=~\".*$shoot_cluster\"})) /\n max by(instance) (node_filesystem_size_bytes{device!~\"rootfs|tmpfs\",  instance=~\"$master_node_instance\", cluster=~\".*$shoot_cluster\"})) * 100", "hide": false, "interval": "$interval", "legendFormat": "{{instance}}", "refId": "C"}], "thresholds": [{"colorMode": "critical", "fill": true, "line": true, "op": "gt", "value": 80, "yaxis": "left"}], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Space Usage", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "transformations": [{"id": "renameByRegex", "options": {"regex": "\\.[^\\.]+\\.compute.internal", "renamePattern": ""}}], "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": "100", "min": "0", "show": true}, {"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Hardware usage", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 82, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB cpu usage calculated with process cpu running seconds", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 4}, "hiddenSeries": false, "id": 107, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}, {"alias": "/Al<PERSON>/", "color": "#FA6400", "fill": 0, "hideTooltip": true, "legend": false, "nullPointMode": "null"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tidb\"}[$interval])", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 40}, {"exemplar": true, "expr": "tidb_server_maxprocs{cluster_id=~\".*$cluster_id\", component=\"tidb\"} * 0.8", "interval": "$interval", "legendFormat": "Alert-{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 4}, "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Process: {{instance}}", "refId": "A", "step": 10}, {"exemplar": true, "expr": "go_memstats_heap_inuse_bytes{cluster_id=~\".*$cluster_id\", component=\"tidb\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "HeapInuse: {{instance}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 11}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tidb_executor_statement_total{cluster_id=~\".*$cluster_id\"}[$interval])) by (type)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Statement OPS", "tooltip": {"msResolution": true, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 11}, "hiddenSeries": false, "id": 72, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(increase(tidb_server_execute_error_total{cluster_id=~\".*$cluster_id\"}[$interval])) by (type)", "format": "time_series", "intervalFactor": 1, "legendFormat": " {{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Failed Query OPM", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 18}, "id": 206, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(rate(container_network_receive_bytes_total{namespace=~\".*$cluster_id\",tenant=\"$tenant_id\", pod=~\"db-tidb-.*\"}[5m])) by (namespace, tenant, shoot_name, pod) ", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Network rate", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 34, "legend": {"alignAsTable": false, "avg": false, "current": false, "hideEmpty": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.999, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (le))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "999", "refId": "A", "step": 10}, {"expr": "histogram_quantile(0.99, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (le))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "99", "refId": "B", "step": 15}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (le))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "95", "refId": "C"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_server_handle_query_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (le))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "80", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Query Duration", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 25}, "hiddenSeries": false, "id": 71, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (le))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "99", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (le))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "95", "refId": "B"}, {"expr": "histogram_quantile(0.80, sum(rate(tidb_session_transaction_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (le))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "80", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction Duration", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 25}, "hiddenSeries": false, "id": 38, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[$interval])) by (le, type))", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Cmd Duration 99", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiDB current connection counts", "editable": true, "error": false, "fill": 9, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 32}, "hiddenSeries": false, "id": 170, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "tidb_server_connections{cluster_id=~\".*$cluster_id\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 40}, {"exemplar": true, "expr": "sum(tidb_server_connections{cluster_id=~\".*$cluster_id\"})", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "total", "refId": "B", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connection Count", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "TiDB Usage", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 83, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "hiddenSeries": false, "id": 75, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tikv\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "hiddenSeries": false, "id": 74, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "avg(process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"tikv\"}) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 13}, "id": 208, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "sum(rate(container_network_receive_bytes_total{namespace=~\".*$cluster_id\",tenant=\"$tenant_id\", pod=~\"db-tikv-.*\"}[5m])) by (namespace, tenant, shoot_name, pod) ", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Network rate", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The CPU utilization of scheduler worker", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 13}, "hiddenSeries": false, "id": 117, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Capacity", "color": "#F2495C", "fillBelowTo": "90% AlertLine", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "90% AlertLine", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"sched_.*\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"sched_.*\"}) by (instance)) * 0.9", "interval": "$interval", "intervalFactor": 1, "legendFormat": "90% AlertLine", "refId": "B"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"sched_.*\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Scheduler worker CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Capacity": "dark-red"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The CPU utilization of gRPC", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 20}, "hiddenSeries": false, "id": 121, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Capacity", "color": "#F2495C", "fillBelowTo": "80% AlertLine", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "80% AlertLine", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"grpc.*\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 4}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"grpc.*\"}) by (instance)) * 0.8", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "80% AlertLine", "refId": "B"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"grpc.*\"}) by (instance))", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [{"colorMode": "background6", "fill": true, "fillColor": "rgba(234, 112, 112, 0.12)", "line": false, "lineColor": "rgba(237, 46, 24, 0.60)", "op": "time"}], "timeShift": null, "title": "gRPC poll CPU", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The CPU utilization of async apply", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 20}, "hiddenSeries": false, "id": 115, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Capacity", "color": "#F2495C", "fill": 0, "fillBelowTo": "90% AlertLine", "hideTooltip": true}, {"alias": "90% AlertLine", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"apply_[0-9]+\"}[$interval])) by (instance)", "format": "time_series", "instant": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"apply_[0-9]+\"}) by (instance)) * 0.9", "interval": "$interval", "intervalFactor": 1, "legendFormat": "90% AlertLine", "refId": "B"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"apply_[0-9]+\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Async apply CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The CPU utilization of raftstore thread", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 113, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Capacity", "color": "#F2495C", "fillBelowTo": "80% AlertLine", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "80% AlertLine", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"raftstore_.*\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"raftstore_.*\"}) by (instance)) * 0.8", "interval": "$interval", "intervalFactor": 1, "legendFormat": "80% AlertLine", "refId": "B"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"raftstore_.*\"}) by (instance))", "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Raft store CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The CPU utilization of the unified read pool", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 28}, "hiddenSeries": false, "id": 133, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Capacity", "color": "#F2495C", "fillBelowTo": "90% AlertLine", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "90% AlertLine", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\".*$tikv_instance\", name=~\"unified_read_po*\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 4}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\".*$tikv_instance\", name=~\"unified_read_po*\"}) by (instance)) * 0.9", "interval": "$interval", "intervalFactor": 1, "legendFormat": "90% AlertLine", "refId": "B"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\".*$tikv_instance\", name=~\"unified_read_po*\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Unified read pool CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The CPU utilization of coprocessor. Starting from TiKV v5.0, all coprocessor requests use the unified read pool for queries by default.", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 35}, "hiddenSeries": false, "id": 123, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Capacity - Normal", "color": "#F2495C", "fillBelowTo": "80% AlertLine - Normal", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "80% AlertLine - Normal", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "Capacity - High", "color": "#F2495C", "fillBelowTo": "80% AlertLine - High", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "80% AlertLine - High", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "Capacity - Low", "color": "#F2495C", "fillBelowTo": "80% AlertLine - Low", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "80% AlertLine - Low", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_normal.*\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}} - normal", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_high.*\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}} - high", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_low.*\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}} - low", "refId": "C", "step": 4}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_normal.*\"}) by (instance)) * 0.8", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "80% AlertLine - Normal", "refId": "D"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_normal.*\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity - Normal", "refId": "E"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_high.*\"}) by (instance)) * 0.8", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "80% AlertLine - High", "refId": "F"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_high.*\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity - High", "refId": "G"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_low.*\"}) by (instance)) * 0.8", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "80% AlertLine - Low", "refId": "H"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"cop_low.*\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity - Low", "refId": "I"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Coprocessor CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": 1, "description": "The CPU utilization of readpool", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 42}, "hiddenSeries": false, "id": 135, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideEmpty": true, "hideZero": true, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "Capacity - Normal", "color": "#F2495C", "fillBelowTo": "80% AlertLine - Normal", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "80% AlertLine - Normal", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "Capacity - High", "color": "#F2495C", "fillBelowTo": "80% AlertLine - High", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "80% AlertLine - High", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "Capacity - Low", "color": "#F2495C", "fillBelowTo": "80% AlertLine - Low", "hideTooltip": true, "nullPointMode": "connected"}, {"alias": "80% AlertLine - Low", "color": "#F2495C", "fill": 0, "hideTooltip": true, "nullPointMode": "connected"}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_norm.*\"}[$interval])) by (instance)", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}} - normal", "metric": "tikv_thread_cpu_seconds_total", "refId": "A", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_high.*\"}[$interval])) by (instance)", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}} - high", "metric": "tikv_thread_cpu_seconds_total", "refId": "B", "step": 4}, {"expr": "sum(rate(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_low.*\"}[$interval])) by (instance)", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}} - low", "metric": "tikv_thread_cpu_seconds_total", "refId": "C", "step": 4}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_norm.*\"}) by (instance)) * 0.8", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "80% AlertLine - Normal", "refId": "D"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_norm.*\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity - Normal", "refId": "E"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_high.*\"}) by (instance)) * 0.8", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "80% AlertLine - High", "refId": "F"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_high.*\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity - High", "refId": "G"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_low.*\"}) by (instance)) * 0.8", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "80% AlertLine - Low", "refId": "H"}, {"expr": "min(count(tikv_thread_cpu_seconds_total{cluster_id=~\".*$cluster_id\", instance=~\"$tikv_instance\", name=~\"store_read_low.*\"}) by (instance))", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "Capacity - Low", "refId": "I"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Storage ReadPool CPU", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "TiKV Usage", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 127, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "TiFlash CPU usage calculated with process CPU running seconds.", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "hiddenSeries": false, "id": 129, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "total", "fill": 0, "lines": false}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(tiflash_proxy_process_cpu_seconds_total{cluster_id=~\".*$cluster_id\", component=\"tiflash\"}[$interval])", "format": "time_series", "hide": false, "interval": "$interval", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 40}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"msResolution": false, "shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 1, "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "The memory usage per TiFlash instance", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "hiddenSeries": false, "id": 131, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": false, "rightSide": true, "show": true, "sideWidth": null, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "tiflash_proxy_process_resident_memory_bytes{cluster_id=~\".*$cluster_id\", component=\"tiflash\"}", "format": "time_series", "interval": "$interval", "intervalFactor": 1, "legendFormat": "ProcessResident: {{instance}}", "refId": "H"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "title": "TiFlash Usage", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 176, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 2}, "hiddenSeries": false, "id": 178, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 0, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"exemplar": true, "expr": "    max (\n      (\n        increase(reporter_service_level_probe_cluster_counter{cluster_id=\"$cluster_id\", status=\"alive\"}[1m:1m])   # How many successful probes in the last 1 minute for each minute\n        /ignoring(status)\n        sum without (status)(increase(reporter_service_level_probe_cluster_counter{cluster_id=\"$cluster_id\"}[1m:1m]))   # How many total probes (include failure probes) in the last 1 minute for each minute\n      ) OR on() vector(0)  # If there is no data, treat it as 0%\n    )   # There are two reporters deployed. As long as one reporter can establish connection we treat it as available.\n    > bool 0   # As long as there is >0% availability in 1 minute, this minute is available and treat it as a 100% available minute.", "interval": "", "legendFormat": "SuccessRate", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connection Availability", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 2}, "hiddenSeries": false, "id": 180, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 0, "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.7", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"exemplar": true, "expr": "1 - max((\n  (sum(rate(tidb_server_execute_error_total{cluster_id=~\".*$cluster_id\", type=~\"[^:]*:(8021|8023|8024|8026|8027|8028|8039|8041|8042|8043|8044|8045|8046|8049|8050|8051|8052|8053|8054|8056|8057|8058|8059|8062|8106|8114|8117|8118|8120|8122|8201|8202|8203|8204|8205|8206|8207|8208|8209|8210|8211|8213|8214|8215|8217|8218|8219|8220|8221|8222|8223|8235|9001|9002|9003|9004|9005|9009|9010|9011|9012|9013)\"}[$__rate_interval])) or on() vector(0))\n  / sum(rate(tidb_server_query_total{cluster_id=~\".*$cluster_id\", type=~\"Query|StmtExecute\", result=\"OK\"}[$__rate_interval]))\n) >= 0 or on() vector(0))", "hide": false, "interval": "", "legendFormat": "SuccessRate", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "SQL Success Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 2}, "id": 186, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, rate(tidb_sli_small_txn_write_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[30d]))", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "tidb small txn write duration P99", "transformations": [], "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 8}, "id": 196, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, rate(tidb_sli_tikv_small_read_duration_bucket{cluster_id=~\".*$cluster_id\"}[30d]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "tidb read duration P99", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 8}, "id": 198, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.999, rate(tidb_sli_tikv_small_read_duration_bucket{cluster_id=~\".*$cluster_id\"}[30d]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "tidb read duration P99.9", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 8}, "id": 192, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.999, rate(tidb_sli_txn_write_throughput_bucket{cluster_id=~\".*$cluster_id\"}[30d]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "tidb txn write throughput P99.9", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 15}, "id": 194, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, rate(tidb_sli_tikv_small_read_duration_bucket{cluster_id=~\".*$cluster_id\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "tikv read duration in minute", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "axisWidth": 1, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 15}, "id": 184, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, rate(tidb_sli_txn_write_throughput_bucket{cluster_id=~\".*$cluster_id\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "tidb txn write throughput in minutes", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 15}, "id": 182, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"exemplar": true, "expr": " histogram_quantile(0.99, rate(tidb_sli_small_txn_write_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[1m]))", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "tidb small txn write duration in minutes", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 21}, "id": 190, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, rate(tidb_sli_txn_write_throughput_bucket{cluster_id=~\".*$cluster_id\"}[30d]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "tidb txn write throughput P99", "type": "timeseries"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 21}, "id": 188, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.0.2", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.999, rate(tidb_sli_small_txn_write_duration_seconds_bucket{cluster_id=~\".*$cluster_id\"}[30d]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "tidb small txn write duration P99.9", "type": "timeseries"}], "title": "SLI", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 200, "panels": [{"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3}, "id": 202, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"exemplar": true, "expr": "sum(max_over_time(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}[$__range]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Dev Tier Storage Size", "type": "stat"}, {"datasource": null, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 3}, "id": 204, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.7", "targets": [{"exemplar": true, "expr": "sum(increase(tidb_server_query_total{cluster_id=~\".*$cluster_id\"}[$__range]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Dev Tier SQL Statement Count", "type": "stat"}], "title": "<PERSON>", "type": "row"}], "refresh": "1m", "schemaVersion": 30, "style": "dark", "tags": ["tidb-mixin"], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "Colopl", "value": "Colopl"}, "datasource": "thanos", "definition": "query_result(sum by (tenant, name)(dbaas_tenant_info) and on(tenant) sum by (tenant)(dbaas_tidb_cluster_info))", "description": "The name of the organization (tenant)", "error": null, "hide": 0, "includeAll": false, "label": "Org", "multi": false, "name": "tenant_name", "options": [], "query": {"query": "query_result(sum by (tenant, name)(dbaas_tenant_info) and on(tenant) sum by (tenant)(dbaas_tidb_cluster_info))", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "/\"([^\"]+)\"/", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "1326455380288475136", "value": "1326455380288475136"}, "datasource": "thanos", "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "description": null, "error": null, "hide": 1, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "options": [], "query": {"query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"},tenant)", "refId": "prometheus-tenant_id-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "sakura-prod", "value": "sakura-prod"}, "datasource": "thanos", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "description": "The name of the cluster", "error": null, "hide": 0, "includeAll": false, "label": "Cluster", "multi": false, "name": "cluster_name", "options": [], "query": {"query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refId": "prometheus-cluster_name-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": true, "text": "1379661944579474076", "value": "1379661944579474076"}, "datasource": "thanos", "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "description": null, "error": null, "hide": 1, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": {"query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refId": "prometheus-cluster_id-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "db-tikv-2", "value": "db-tikv-2"}, "datasource": "thanos", "definition": "label_values(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"},pod)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "", "multi": false, "name": "node_pod", "options": [], "query": {"query": "label_values(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"db-tikv-\\\\d+|db-pd-\\\\d+|db-tidb-\\\\d+|db-tiflash-\\\\d+\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "thanos", "definition": "label_values(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"$node_pod\"},node)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "node_instance", "options": [], "query": {"query": "label_values(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"$node_pod\"},node)", "refId": "thanos-node_instance-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "tidb-cluster", "definition": "label_values(pd_cluster_status{cluster_id=~\".*$cluster_id\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "pd_instance", "multi": true, "name": "pd_instance", "options": [], "query": {"query": "label_values(pd_cluster_status{cluster_id=~\".*$cluster_id\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "tidb-cluster", "definition": "label_values(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "tikv_instance", "multi": true, "name": "tikv_instance", "options": [], "query": {"query": "label_values(tikv_engine_size_bytes{cluster_id=~\".*$cluster_id\"}, instance)", "refId": "tidb-cluster-tikv_instance-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "thanos", "definition": "label_values(kube_pod_info{namespace=~\".*$cluster_id\",pod!~\"$node_pod\"},node)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": true, "name": "master_node_instance", "options": [], "query": {"query": "label_values(kube_pod_info{namespace=~\".*$cluster_id\",pod!~\"$node_pod\"},node)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "thanos", "definition": "label_values(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"$node_pod\"},cluster)", "description": null, "error": null, "hide": 2, "includeAll": true, "label": null, "multi": true, "name": "shoot_cluster", "options": [], "query": {"query": "label_values(kube_pod_info{namespace=~\".*$cluster_id\",pod=~\"$node_pod\"},cluster)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"auto": true, "auto_count": 50, "auto_min": "15s", "current": {"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, "description": null, "error": null, "hide": 0, "label": null, "name": "interval", "options": [{"selected": true, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,5m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "queryValue": "", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "TiDB in One Screen", "uid": "one-screen-tidb", "version": 74}