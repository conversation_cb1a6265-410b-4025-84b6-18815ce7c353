{"__inputs": [{"name": "DS_PLAYGROUND", "label": "playground", "description": "", "type": "datasource", "pluginId": "prometheus", "pluginName": "Prometheus"}], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "7.5.7"}, {"type": "datasource", "id": "prometheus", "name": "Prometheus", "version": "1.0.0"}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Create time need for infra network/cluster", "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "iteration": 1662713691705, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 6, "panels": [], "title": "Create Time", "type": "row"}, {"description": "Network time to be Normal after created", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "string"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 4, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "createtime"}]}, "pluginVersion": "7.5.7", "targets": [{"exemplar": true, "expr": "avg by(resource_namespace, name, stage, createtime) (infra_api_resources_first_pass_stage_when_creating_network_duration_seconds{stage=\"All\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "{{stage}}", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Network Create Time", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "stage": true}, "indexByName": {}, "renameByName": {}}}], "type": "table"}, {"description": "Cluster time to be Normal after created", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "string"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 7, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "createtime"}]}, "pluginVersion": "7.5.7", "targets": [{"exemplar": true, "expr": "avg by(resource_namespace, name, stage, createtime) (infra_api_resources_first_pass_stage_when_creating_cluster_duration_seconds{stage=\"All\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "{{stage}}", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Cluster Create Time", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "stage": true}, "indexByName": {}, "renameByName": {"name": ""}}}], "type": "table"}, {"description": "First time pass stage for specify network", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 2, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "sinceCreate"}]}, "pluginVersion": "7.5.7", "targets": [{"exemplar": true, "expr": "avg by(stage) (infra_api_resources_first_pass_stage_when_creating_network_duration_seconds{resource_namespace=\"$network_namespace\", name=\"$network_name\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "title": "Network", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Time": "", "Value": "sinceCreate", "stage": ""}}}], "type": "table"}, {"description": "First time pass stage for specify cluster", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 8, "options": {"showHeader": true, "sortBy": [{"desc": false, "displayName": "sinceCreate"}]}, "pluginVersion": "7.5.7", "targets": [{"exemplar": true, "expr": "avg by(stage) (infra_api_resources_first_pass_stage_when_creating_cluster_duration_seconds{name=\"$cluster_name\"})", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "title": "Cluster", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Time": "", "Value": "sinceCreate", "stage": ""}}}], "type": "table"}], "refresh": "30s", "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {}, "definition": "label_values(infra_api_resources_first_pass_stage_when_creating_network_duration_seconds, resource_namespace)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "network_namespace", "options": [], "query": {"query": "label_values(infra_api_resources_first_pass_stage_when_creating_network_duration_seconds, resource_namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 6, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "definition": "label_values(infra_api_resources_first_pass_stage_when_creating_network_duration_seconds, name)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "network_name", "options": [], "query": {"query": "label_values(infra_api_resources_first_pass_stage_when_creating_network_duration_seconds, name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 6, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {}, "definition": "label_values(infra_api_resources_first_pass_stage_when_creating_cluster_duration_seconds, name)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "cluster_name", "options": [], "query": {"query": "label_values(infra_api_resources_first_pass_stage_when_creating_cluster_duration_seconds, name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 6, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Infra Cluster Create Time", "uid": "_oeAu0G4z", "version": 16}