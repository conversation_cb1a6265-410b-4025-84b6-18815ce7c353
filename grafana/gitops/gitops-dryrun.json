{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 331, "iteration": 1655883221325, "links": [], "liveNow": false, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.filterable", "value": true}, {"id": "custom.width", "value": 196}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_id"}, "properties": [{"id": "unit", "value": "string"}, {"id": "links", "value": [{"title": "dryrun logs", "url": "https://grafana.oauth.prod.aws.tidbcloud.com/explore?orgId=1&left=%7B%22datasource%22:%22loki%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bapp%3D%5C%22config-service%5C%22%7D%7C%3D%5C%22dryrun%5C%22%7C%3D%5C%22${__data.fields.cluster_id}%5C%22%22,%22queryType%22:%22range%22%7D%5D,%22range%22:%7B%22from%22:%22now-15m%22,%22to%22:%22now%22%7D%7D"}]}, {"id": "custom.filterable", "value": true}, {"id": "custom.width", "value": 199}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "version"}, "properties": [{"id": "custom.width", "value": 105}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "name"}, "properties": [{"id": "custom.width", "value": 124}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_kind"}, "properties": [{"id": "custom.width", "value": 103}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_name"}, "properties": [{"id": "custom.width", "value": 199}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "pd"}, "properties": [{"id": "mappings", "value": [{"options": {" ": {"color": "green", "index": 2, "text": "No Side Effect"}}, "type": "value"}, {"options": {"pattern": ".*updated.*", "result": {"color": "red", "index": 0}}, "type": "regex"}, {"options": {"match": "null+nan", "result": {"color": "green", "index": 1, "text": "No Side Effect"}}, "type": "special"}]}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tidb"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "mappings", "value": [{"options": {" ": {"color": "green", "index": 1, "text": "No Side Effect"}}, "type": "value"}, {"options": {"pattern": ".*updated.*", "result": {"color": "red", "index": 0}}, "type": "regex"}, {"options": {"match": "null+nan", "result": {"color": "green", "index": 2, "text": "No Side Effect"}}, "type": "special"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tikv"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "mappings", "value": [{"options": {" ": {"index": 1, "text": "No Side Effect"}}, "type": "value"}, {"options": {"pattern": ".*updated.*", "result": {"color": "red", "index": 0}}, "type": "regex"}, {"options": {"match": "null+nan", "result": {"index": 2, "text": "No Side Effect"}}, "type": "special"}]}, {"id": "noValue", "value": "No Side Effect"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tiflash"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "noValue", "value": "No Side Effect"}, {"id": "mappings", "value": [{"options": {" ": {"index": 1, "text": "No Side Effect"}}, "type": "value"}, {"options": {"pattern": ".*updated.*", "result": {"color": "red", "index": 0}}, "type": "regex"}, {"options": {"match": "null+nan", "result": {"index": 2, "text": "No Side Effect"}}, "type": "special"}]}]}]}, "gridPos": {"h": 17, "w": 24, "x": 0, "y": 0}, "id": 3, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Affected Components"}]}, "pluginVersion": "8.5.4", "targets": [{"datasource": null, "editorMode": "code", "exemplar": false, "expr": "avg by(cluster_id,pd) (label_replace(gitops_config_dryrun_affected{cluster_id=~\"$cluster\",component=\"pd\"},\"pd\",\"$1 \",\"reason\",\"(.*)\"))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "B"}, {"datasource": null, "editorMode": "code", "exemplar": false, "expr": "avg by(cluster_id,tidb) (label_replace(gitops_config_dryrun_affected{cluster_id=~\"$cluster\",component=\"tidb\"},\"tidb\",\"$1 \",\"reason\",\"(.*)\"))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "C"}, {"datasource": null, "editorMode": "code", "exemplar": false, "expr": "avg by(cluster_id,tikv) (label_replace(gitops_config_dryrun_affected{cluster_id=~\"$cluster\",component=\"tikv\"},\"tikv\",\"$1 \",\"reason\",\"(.*)\"))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "D"}, {"datasource": null, "editorMode": "code", "exemplar": false, "expr": "avg by(cluster_id,tiflash) (label_replace(gitops_config_dryrun_affected{cluster_id=~\"$cluster\",component=\"tiflash\"},\"tiflash\",\"$1 \",\"reason\",\"(.*)\"))", "format": "table", "hide": false, "instant": true, "legendFormat": "__auto", "range": false, "refId": "E"}, {"datasource": null, "editorMode": "code", "exemplar": false, "expr": "count by (tenant,tenant_kind,tenant_name,plan,cluster,cluster_id,name,version)(dbaas_tidb_cluster_info{tenant=~\"$tenant\",dev_tier!=\"true\",cluster_id=~\"$cluster\"})", "format": "table", "instant": true, "range": false, "refId": "A"}], "title": "Dryrun Clusters", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Value": true, "Value #A": true, "Value #B": true, "Value #C": true, "Value #D": true, "Value #E": true, "cluster": true}, "indexByName": {"Time": 0, "Value": 8, "cluster": 1, "cluster_id": 5, "name": 6, "tenant": 2, "tenant_kind": 4, "tenant_name": 3, "version": 7}, "renameByName": {"Affected": "Affected Components", "cluster_id": "Dryrun Logs for cluster", "name": "Cluster Name"}}}], "type": "table"}], "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": null, "definition": "query_result(count by (tenant,tenant_kind,tenant_name,plan,cluster,cluster_id,name,version)(dbaas_tidb_cluster_info{dev_tier!=\"true\"} * on (tenant) group_left() count by (tenant) (dbaas_tenant_info{plan!=\"free_trial\"})))", "hide": 0, "includeAll": true, "multi": true, "name": "tenant", "options": [], "query": {"query": "query_result(count by (tenant,tenant_kind,tenant_name,plan,cluster,cluster_id,name,version)(dbaas_tidb_cluster_info{dev_tier!=\"true\"} * on (tenant) group_left() count by (tenant) (dbaas_tenant_info{plan!=\"free_trial\"})))", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/.*tenant=\"([^\"]*).*/", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": null, "definition": "label_values(dbaas_tidb_cluster_info{tenant=~\"$tenant\",dev_tier!=\"true\"},cluster_id)", "hide": 0, "includeAll": true, "multi": true, "name": "cluster", "options": [], "query": {"query": "label_values(dbaas_tidb_cluster_info{tenant=~\"$tenant\",dev_tier!=\"true\"},cluster_id)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "GitOps Dryrun", "uid": "90I1Tiqnk", "version": 11, "weekStart": ""}