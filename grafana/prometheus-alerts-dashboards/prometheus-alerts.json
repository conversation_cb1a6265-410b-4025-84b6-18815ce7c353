{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}, {"datasource": "$datasource", "enable": false, "expr": "ALERTS_FOR_STATE{instance=~\"$instance\", alertname=~\"$alertname\"} * 1000", "hide": false, "iconColor": "#37872D", "limit": 100, "name": "Alerts annotations:", "showIn": 0, "step": "2s", "tagKeys": "alertname", "tags": [], "textFormat": "", "titleFormat": "{{instance}}", "type": "tags", "useValueForTime": true}, {"datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "Display comments", "showIn": 0, "tags": ["note"], "type": "tags"}]}, "description": "Dashboard showing Prometheus Alerts (both pending and firing) for alerts adjustment and debugging", "editable": true, "gnetId": 11098, "graphTooltip": 1, "id": 46, "iteration": 1599538023289, "links": [{"icon": "external link", "includeVars": true, "keepTime": true, "tags": ["OS"], "type": "dashboards"}, {"icon": "external link", "tags": ["alerts-advanced"], "type": "dashboards"}], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 10, "gridPos": {"h": 10, "w": 17, "x": 0, "y": 0}, "hiddenSeries": false, "id": 440, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": null, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"dataLinks": []}, "percentage": false, "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/firing/", "stack": "B", "transform": "negative-Y", "zindex": -3}, {"alias": "/pending/", "stack": "A"}, {"alias": ""}, {"alias": "/help_series_x_axes_in_center/", "bars": false, "hideTooltip": true, "legend": false, "lines": false, "stack": false, "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"expr": "ALERTS{instance=~\"$instance\", alertname=~\"$alertname\", alertstate=~\"$alertstate\"}", "legendFormat": "{{alertname}} {{instance}} {{alertstate}} {{datacenter}}", "refId": "C"}, {"expr": "1", "hide": false, "legendFormat": "help_series_x_axes_in_center", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON><PERSON> History", "tooltip": {"shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": "Firing (-)   /  Pending (+)  ", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": "1", "min": "-1", "show": false}], "yaxis": {"align": true, "alignLevel": 0}}, {"columns": [], "datasource": "thanos", "description": "Alerts list for all alerts in a selected time range. For filling this table `Alerts annotations` have to be switched on.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 10, "w": 7, "x": 17, "y": 0}, "id": 442, "pageSize": null, "showHeader": true, "sort": {"col": 3, "desc": false}, "styles": [{"alias": "time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "link": false, "linkUrl": "", "pattern": "Time", "type": "date"}, {"alias": "instance", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Title", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "alertname", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Tags", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Text", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "<PERSON><PERSON>s Start List", "transform": "annotations", "type": "table-old"}, {"columns": [], "datasource": "thanos", "description": "Shows how many times was particular alert started in a defined time range. Alert can be started either directly as `firing` or as a `pending`. Pending alerts wait for a defined time before it flips to a `firing` alert. This is specified with the `FOR` clause in a Prometheus `rules` file.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 13, "w": 24, "x": 0, "y": 10}, "id": 414, "pageSize": null, "showHeader": true, "sort": {"col": 10, "desc": true}, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "link": false, "pattern": "Time", "type": "hidden"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "instance", "thresholds": [], "type": "string", "unit": "short"}, {"alias": "count", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "component", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "endpoint", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "group", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "job", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "namespace", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "prometheus", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "receive", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "resource", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "scope", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "service", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "tenant_id", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "verb", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "version", "thresholds": [], "type": "hidden", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "replica", "thresholds": [], "type": "hidden", "unit": "short"}], "targets": [{"expr": "changes( ALERTS_FOR_STATE{instance=~\"$instance\",alertname=~\"$alertname\"}[$__range] ) + 1", "format": "table", "hide": false, "instant": true, "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Total Alert Counts", "transform": "table", "type": "table-old"}], "refresh": false, "schemaVersion": 25, "style": "dark", "tags": ["prometheus", "alerts"], "templating": {"list": [{"current": {"selected": false, "text": "default", "value": "default"}, "hide": 2, "includeAll": false, "label": "Prometheus datasource", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": true, "text": "All", "value": ["$__all"]}, "datasource": "$datasource", "definition": "label_values(ALERTS_FOR_STATE,instance)", "hide": 0, "includeAll": true, "label": "Instance:", "multi": true, "name": "instance", "options": [], "query": "label_values(ALERTS_FOR_STATE,instance)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "All", "value": ["$__all"]}, "datasource": "thanos", "definition": "label_values(ALERTS_FOR_STATE,alertname)", "hide": 0, "includeAll": true, "label": "Alert:", "multi": true, "name": "alertname", "options": [], "query": "label_values(ALERTS_FOR_STATE,alertname)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "thanos", "definition": "label_values(ALERTS, alertstate)", "hide": 0, "includeAll": true, "label": "State:", "multi": true, "name": "alertstate", "options": [], "query": "label_values(ALERTS, alertstate)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"datasource": " <PERSON><PERSON>", "filters": [], "hide": 0, "label": "", "name": "Filters", "skipUrlSync": false, "type": "adhoc"}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Prometheus Alerts", "uid": "eea-9_sik", "version": 10}