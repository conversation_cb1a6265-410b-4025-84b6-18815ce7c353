apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: grafana
  name: grafana-v2
  namespace: monitoring
spec:
  replicas: 3
  selector:
    matchLabels:
      app: grafana-v2
  template:
    metadata:
      labels:
        app: grafana-v2
    spec:
      initContainers:
      - name: init-database
        image: 385595570414.dkr.ecr.us-west-2.amazonaws.com/third-party/mysql:5.6
        command: ['sh', '-c', "mysql -h mysql -uroot -ppassword -e 'CREATE DATABASE IF NOT EXISTS grafana;'"]
      containers:
      - env:
        - name: GF_PATHS_PLUGINS
          value: "/grafana-plugins"
        - name: GF_PLUGINS_ALLOW_LOADING_UNSIGNED_PLUGINS
          value: customized-loki-datasource
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-password
              key: admin-password
        image: gcr.io/pingcap-public/grafana:dffc2ba
        imagePullPolicy: Always
        name: grafana
        ports:
        - containerPort: 3000
          name: http
        readinessProbe:
          httpGet:
            path: /api/health
            port: http
        resources:
          requests:
            cpu: 1000m
            memory: 1024Mi
          limits:
            cpu: 1000m
            memory: 1024Mi
        volumeMounts:
        - mountPath: "/etc/grafana/"
          name: config
        - mountPath: /etc/grafana/provisioning/datasources
          name: grafana-datasources
          readOnly: false
        - mountPath: /etc/grafana/provisioning/dashboards
          name: grafana-dashboards
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/apiserver
          name: grafana-dashboard-apiserver
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/cluster-total
          name: grafana-dashboard-cluster-total
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/controller-manager
          name: grafana-dashboard-controller-manager
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/k8s-resources-cluster
          name: grafana-dashboard-k8s-resources-cluster
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/k8s-resources-multicluster
          name: grafana-dashboard-k8s-resources-multicluster
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/k8s-resources-namespace
          name: grafana-dashboard-k8s-resources-namespace
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/k8s-resources-node
          name: grafana-dashboard-k8s-resources-node
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/k8s-resources-pod
          name: grafana-dashboard-k8s-resources-pod
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/k8s-resources-workload
          name: grafana-dashboard-k8s-resources-workload
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/k8s-resources-workloads-namespace
          name: grafana-dashboard-k8s-resources-workloads-namespace
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/kubelet
          name: grafana-dashboard-kubelet
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/namespace-by-pod
          name: grafana-dashboard-namespace-by-pod
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/namespace-by-workload
          name: grafana-dashboard-namespace-by-workload
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/node-cluster-rsrc-use
          name: grafana-dashboard-node-cluster-rsrc-use
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/node-rsrc-use
          name: grafana-dashboard-node-rsrc-use
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/nodes
          name: grafana-dashboard-nodes
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/persistentvolumesusage
          name: grafana-dashboard-persistentvolumesusage
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/pod-total
          name: grafana-dashboard-pod-total
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/prometheus-remote-write
          name: grafana-dashboard-prometheus-remote-write
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/prometheus
          name: grafana-dashboard-prometheus
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/proxy
          name: grafana-dashboard-proxy
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/scheduler
          name: grafana-dashboard-scheduler
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/statefulset
          name: grafana-dashboard-statefulset
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/0/workload-total
          name: grafana-dashboard-workload-total
          readOnly: false
        # Todo(zhengjiajin): only add dbaas grafana to base cluster grafana
        - mountPath: /grafana-dashboard-definitions/1/central
          name: grafana-dashboard-central
          readOnly: false
        - mountPath: /grafana-dashboard-definitions/1/central-grpc-server
          name: grafana-dashboard-central-grpc-server
          readOnly: false
      nodeSelector:
        beta.kubernetes.io/os: linux
      securityContext:
        runAsNonRoot: true
        runAsUser: 472
        fsGroup: 472
      serviceAccountName: grafana-v2
      volumes:
      - name: config
        configMap:
          name: grafana-ini-v2
      - name: grafana-datasources
        secret:
          secretName: grafana-datasources-v2
      - configMap:
          name: grafana-dashboards-v2
        name: grafana-dashboards
      - configMap:
          name: grafana-dashboard-apiserver-v2
        name: grafana-dashboard-apiserver
      - configMap:
          name: grafana-dashboard-cluster-total-v2
        name: grafana-dashboard-cluster-total
      - configMap:
          name: grafana-dashboard-controller-manager-v2
        name: grafana-dashboard-controller-manager
      - configMap:
          name: grafana-dashboard-k8s-resources-cluster-v2
        name: grafana-dashboard-k8s-resources-cluster
      - configMap:
          name: grafana-dashboard-k8s-resources-multicluster-v2
        name: grafana-dashboard-k8s-resources-multicluster
      - configMap:
          name: grafana-dashboard-k8s-resources-namespace-v2
        name: grafana-dashboard-k8s-resources-namespace
      - configMap:
          name: grafana-dashboard-k8s-resources-node-v2
        name: grafana-dashboard-k8s-resources-node
      - configMap:
          name: grafana-dashboard-k8s-resources-pod-v2
        name: grafana-dashboard-k8s-resources-pod
      - configMap:
          name: grafana-dashboard-k8s-resources-workload-v2
        name: grafana-dashboard-k8s-resources-workload
      - configMap:
          name: grafana-dashboard-k8s-resources-workloads-namespace-v2
        name: grafana-dashboard-k8s-resources-workloads-namespace
      - configMap:
          name: grafana-dashboard-kubelet-v2
        name: grafana-dashboard-kubelet
      - configMap:
          name: grafana-dashboard-namespace-by-pod-v2
        name: grafana-dashboard-namespace-by-pod
      - configMap:
          name: grafana-dashboard-namespace-by-workload-v2
        name: grafana-dashboard-namespace-by-workload
      - configMap:
          name: grafana-dashboard-node-cluster-rsrc-use-v2
        name: grafana-dashboard-node-cluster-rsrc-use
      - configMap:
          name: grafana-dashboard-node-rsrc-use-v2
        name: grafana-dashboard-node-rsrc-use
      - configMap:
          name: grafana-dashboard-nodes-v2
        name: grafana-dashboard-nodes
      - configMap:
          name: grafana-dashboard-persistentvolumesusage-v2
        name: grafana-dashboard-persistentvolumesusage
      - configMap:
          name: grafana-dashboard-pod-total-v2
        name: grafana-dashboard-pod-total
      - configMap:
          name: grafana-dashboard-prometheus-remote-write-v2
        name: grafana-dashboard-prometheus-remote-write
      - configMap:
          name: grafana-dashboard-prometheus-v2
        name: grafana-dashboard-prometheus
      - configMap:
          name: grafana-dashboard-proxy-v2
        name: grafana-dashboard-proxy
      - configMap:
          name: grafana-dashboard-scheduler-v2
        name: grafana-dashboard-scheduler
      - configMap:
          name: grafana-dashboard-statefulset-v2
        name: grafana-dashboard-statefulset
      - configMap:
          name: grafana-dashboard-workload-total-v2
        name: grafana-dashboard-workload-total
      - configMap:
          name: grafana-dashboard-central-v2
        name: grafana-dashboard-central
      - configMap:
          name: grafana-dashboard-central-grpc-server-v2
        name: grafana-dashboard-central-grpc-server
