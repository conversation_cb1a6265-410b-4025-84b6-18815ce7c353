{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 39, "iteration": 1599045520176, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 11, "panels": [], "repeat": null, "title": "Headlines", "type": "row"}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 4, "x": 0, "y": 1}, "id": 1, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "options": {}, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false, "ymax": null, "ymin": null}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "1 - avg(rate(node_cpu_seconds_total{mode=\"idle\"}[$__interval]))", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "CPU Utilisation", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 4, "x": 4, "y": 1}, "id": 2, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "options": {}, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false, "ymax": null, "ymin": null}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\"}) / sum(kube_node_status_allocatable{resource=\"cpu\"})", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "CPU Requests Commitment", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 4, "x": 8, "y": 1}, "id": 3, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "options": {}, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false, "ymax": null, "ymin": null}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "sum(kube_pod_container_resource_limits{resource=\"cpu\"}) / sum(kube_node_status_allocatable{resource=\"cpu\"})", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "CPU Limits Commitment", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 4, "x": 12, "y": 1}, "id": 4, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "options": {}, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false, "ymax": null, "ymin": null}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "1 - sum(:node_memory_MemAvailable_bytes:sum) / sum(kube_node_status_allocatable{resource=\"memory\"})", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "Memory Utilisation", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 4, "x": 16, "y": 1}, "id": 5, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "options": {}, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false, "ymax": null, "ymin": null}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "sum(kube_pod_container_resource_requests{resource=\"memory\"}) / sum(kube_node_status_allocatable{resource=\"memory\"})", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "Memory Requests Commitment", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"aliasColors": {}, "bars": false, "cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "format": "percentunit", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 4, "x": 20, "y": 1}, "id": 6, "interval": null, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "null as zero", "nullText": null, "options": {}, "percentage": false, "pointradius": 5, "points": false, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false, "ymax": null, "ymin": null}, "stack": false, "steppedLine": false, "tableColumn": "", "targets": [{"expr": "sum(kube_pod_container_resource_limits{resource=\"memory\"}) / sum(kube_node_status_allocatable{resource=\"memory\"})", "format": "time_series", "instant": true, "intervalFactor": 2, "refId": "A"}], "thresholds": "70,80", "timeFrom": null, "timeShift": null, "title": "Memory Limits Commitment", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 12, "panels": [], "repeat": null, "title": "CPU", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 5}, "hiddenSeries": false, "id": 7, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate) by (cluster)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{cluster}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 13, "panels": [], "repeat": null, "title": "CPU Quota", "type": "row"}, {"aliasColors": {}, "bars": false, "columns": [], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "fontSize": "100%", "gridPos": {"h": 7, "w": 24, "x": 0, "y": 13}, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {}, "pageSize": null, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "showHeader": true, "sort": {"col": 0, "desc": true}, "spaceLength": 10, "stack": false, "steppedLine": false, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "CPU Usage", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #A", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "CPU Requests", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #B", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "CPU Requests %", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #C", "thresholds": [], "type": "number", "unit": "percentunit"}, {"alias": "CPU Limits", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #D", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "CPU Limits %", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #E", "thresholds": [], "type": "number", "unit": "percentunit"}, {"alias": "Cluster", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": true, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "/d/efa86fd1d0c121a26444b636a3f509a8/k8s-resources-cluster?var-datasource=$datasource&var-cluster=$__cell", "pattern": "cluster", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "string", "unit": "short"}], "targets": [{"expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 10}, {"expr": "sum(kube_pod_container_resource_requests{resource=\"cpu\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "B", "step": 10}, {"expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate) by (cluster) / sum(kube_pod_container_resource_requests{resource=\"cpu\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "C", "step": 10}, {"expr": "sum(kube_pod_container_resource_limits{resource=\"cpu\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "D", "step": 10}, {"expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate) by (cluster) / sum(kube_pod_container_resource_limits{resource=\"cpu\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "E", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "CPU Quota", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "transform": "table", "type": "table", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 14, "panels": [], "repeat": null, "title": "Memory", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 0, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 21}, "hiddenSeries": false, "id": 9, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(container_memory_rss{container!=\"\"}) by (cluster)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{cluster}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage (w/o cache)", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 15, "panels": [], "repeat": null, "title": "Memory Requests", "type": "row"}, {"aliasColors": {}, "bars": false, "columns": [], "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "fontSize": "100%", "gridPos": {"h": 7, "w": 24, "x": 0, "y": 29}, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {}, "pageSize": null, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "showHeader": true, "sort": {"col": 0, "desc": true}, "spaceLength": 10, "stack": false, "steppedLine": false, "styles": [{"alias": "Time", "align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "hidden"}, {"alias": "Memory Usage", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #A", "thresholds": [], "type": "number", "unit": "bytes"}, {"alias": "Memory Requests", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #B", "thresholds": [], "type": "number", "unit": "bytes"}, {"alias": "Memory Requests %", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #C", "thresholds": [], "type": "number", "unit": "percentunit"}, {"alias": "Memory Limits", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #D", "thresholds": [], "type": "number", "unit": "bytes"}, {"alias": "Memory Limits %", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "", "pattern": "Value #E", "thresholds": [], "type": "number", "unit": "percentunit"}, {"alias": "Cluster", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": true, "linkTargetBlank": false, "linkTooltip": "Drill down", "linkUrl": "/d/efa86fd1d0c121a26444b636a3f509a8/k8s-resources-cluster?var-datasource=$datasource&var-cluster=$__cell", "pattern": "cluster", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "", "align": "auto", "colorMode": null, "colors": [], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "string", "unit": "short"}], "targets": [{"expr": "sum(container_memory_rss{container!=\"\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 10}, {"expr": "sum(kube_pod_container_resource_requests{resource=\"memory\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "B", "step": 10}, {"expr": "sum(container_memory_rss{container!=\"\"}) by (cluster) / sum(kube_pod_container_resource_requests{resource=\"memory\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "C", "step": 10}, {"expr": "sum(kube_pod_container_resource_limits{resource=\"memory\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "D", "step": 10}, {"expr": "sum(container_memory_rss{container!=\"\"}) by (cluster) / sum(kube_pod_container_resource_limits{resource=\"memory\"}) by (cluster)", "format": "table", "instant": true, "intervalFactor": 2, "legendFormat": "", "refId": "E", "step": 10}], "thresholds": [], "timeFrom": null, "timeShift": null, "title": "Requests by Namespace", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "transform": "table", "type": "table", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}]}], "refresh": "10s", "schemaVersion": 22, "style": "dark", "tags": ["Capacity Planning"], "templating": {"list": [{"current": {"text": "default", "value": "default"}, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "UTC", "title": "Multi-Cluster Quota", "uid": "b59e6c9f2fcbe2e16d77fc492374cc4fs", "version": 4}