{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 40, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 6, "panels": [], "title": "Gauge Bar", "type": "row"}, {"datasource": null, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 1}, "id": 4, "options": {"displayMode": "lcd", "fieldOptions": {"calcs": ["mean"], "defaults": {"links": [], "mappings": [], "max": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 0.75}, {"color": "red", "value": 0.9}]}, "title": "", "unit": "percentunit"}, "limit": 10, "overrides": [], "values": true}, "orientation": "horizontal", "showUnfilled": true}, "pluginVersion": "6.6.0", "targets": [{"expr": "sort_desc(sum(gcp_quota_usage{})by(region,metric)/sum(gcp_quota_limit{})by(region,metric))", "format": "time_series", "instant": true, "legendFormat": "{{metric}}-{{ region }}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "GCP Cloud Quota", "type": "bargauge"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 8, "panels": [], "title": "Table", "type": "row"}, {"columns": [], "datasource": null, "fontSize": "100%", "gridPos": {"h": 12, "w": 24, "x": 0, "y": 11}, "id": 2, "options": {}, "pageSize": null, "showHeader": true, "sort": {"col": 5, "desc": true}, "styles": [{"alias": "Resources", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "metric", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "Region", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "region", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "Limit", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "Value #A", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "Used", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 0, "mappingType": 1, "pattern": "Value #B", "thresholds": [], "type": "number", "unit": "short"}, {"alias": "Usage", "align": "auto", "colorMode": "cell", "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "mappingType": 1, "pattern": "Value #C", "thresholds": ["0.75", "0.90"], "type": "number", "unit": "percentunit"}, {"alias": "", "align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "dateFormat": "YYYY-MM-DD HH:mm:ss", "decimals": 2, "link": false, "mappingType": 1, "pattern": "Time", "thresholds": [], "type": "hidden", "unit": "short"}], "targets": [{"expr": "sum(gcp_quota_limit{})by(region,metric)", "format": "table", "instant": true, "refId": "A"}, {"expr": "sum(gcp_quota_usage{})by(region,metric)", "format": "table", "instant": true, "refId": "B"}, {"expr": "sum(gcp_quota_usage{})by(region,metric)/sum(gcp_quota_limit{})by(region,metric)", "format": "table", "instant": true, "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "GCP Cloud Quota", "transform": "table", "type": "table"}], "refresh": "10s", "schemaVersion": 22, "style": "dark", "tags": ["Capacity Planning"], "templating": {"list": []}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "GCP Cloud Quota", "uid": "cAjl0vvMz", "version": 21}