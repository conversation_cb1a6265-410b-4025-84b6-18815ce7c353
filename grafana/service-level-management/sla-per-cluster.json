{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 2, "iteration": 1655951514975, "links": [], "liveNow": false, "panels": [{"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 6, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.9999}, {"color": "green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  value\nFROM sla\nWHERE\n  cluster_id='${cluster_id}' and month='${month}'", "refId": "A"}], "title": "Current SLA", "type": "stat"}, {"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "alive_count"}, "properties": [{"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "green", "value": 0.1}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 4, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT FROM_UNIXTIME(FLOOR(timestamp/60)*60) as time,\n  SUM(IF(status='alive' or status='skipped_by_not_ready' or status='skipped_by_suspend',increment, 0)) as alive_count, \n  SUM(IF(status='dead' or status='alive_outdated',increment,0)) as dead_count\nFROM reporter_service_level_probe_cluster_counter \nWHERE FLOOR(timestamp/60) in (\n  SELECT FLOOR(timestamp/60) as TS\n  FROM reporter_service_level_probe_cluster_counter \n  WHERE timestamp>UNIX_TIMESTAMP(DATE(DATE_SUB(concat('${month}','/01'),INTERVAL DAYOFMONTH(concat('${month}','/01'))-1 DAY))) \n  AND timestamp<UNIX_TIMESTAMP(TIMESTAMPADD(SECOND,-1,DATE(DATE_ADD(DATE_SUB(concat('${month}','/01'),INTERVAL DAYOFMONTH(concat('${month}','/01'))-1 DAY), INTERVAL 1 MONTH)))) \n  AND cluster_id='${cluster_id}' AND increment>0\n  AND status!=\"alive\" AND status!=\"skipped_by_not_ready\" AND status!=\"skipped_by_suspend\" \n) AND cluster_id='${cluster_id}' AND increment>0\nGROUP BY time\nORDER BY time ASC", "refId": "A"}], "title": "Minutes existing DEAD", "type": "table"}, {"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 5}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 6, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  value\nFROM mttr\nWHERE\n  cluster_id='${cluster_id}' and month='${month}'", "refId": "A"}], "title": "Current MTTR", "type": "stat"}, {"description": "", "datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 8}, "id": 9, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  downtime_minutes\nFROM mttr\nWHERE\n  cluster_id='${cluster_id}' and month='${month}'", "refId": "A"}], "title": "Downtime", "type": "stat"}, {"description": "", "datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 8}, "id": 8, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  incidents\nFROM mttr\nWHERE\n  cluster_id='${cluster_id}' and month='${month}'", "refId": "A"}], "title": "Incidents", "type": "stat"}], "refresh": false, "schemaVersion": 35, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": false, "text": "dbaastest3", "value": "dbaastest3"}, "datasource": "telemetry-db", "definition": "SELECT\n  DISTINCT tenant\nFROM cluster_history_telemetry\nWHERE\n  date >= DATE('${__from:date:YYYY-MM-DD}')\n  AND cluster_type='standard'", "hide": 0, "includeAll": false, "label": "org", "multi": false, "name": "tenant_name", "options": [], "query": "SELECT\n  DISTINCT tenant\nFROM cluster_history_telemetry\nWHERE\n  date >= DATE('${__from:date:YYYY-MM-DD}')\n  AND cluster_type='standard'", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "1372813089187791289", "value": "1372813089187791289"}, "datasource": "telemetry-db", "definition": "SELECT\n  DISTINCT tenant_id\nFROM cluster_history_telemetry\nWHERE\n  date >= DATE('${__from:date:YYYY-MM-DD}')\n  AND tenant='${tenant_name}'\n  AND cluster_type='standard'", "hide": 1, "includeAll": false, "label": "", "multi": false, "name": "tenant_id", "options": [], "query": "SELECT\n  DISTINCT tenant_id\nFROM cluster_history_telemetry\nWHERE\n  date >= DATE('${__from:date:YYYY-MM-DD}')\n  AND tenant='${tenant_name}'\n  AND cluster_type='standard'", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": false, "text": "1654053428", "value": "1654053428"}, "datasource": "telemetry-db", "definition": "SELECT  DISTINCT cluster_name\nFROM cluster_history_telemetry \nWHERE   date >= DATE('${__from:date:YYYY-MM-DD}')\n  AND cluster_type='standard'\n  AND tenant_id='${tenant_id}'\n  AND cluster_name IS NOT NULL", "hide": 0, "includeAll": false, "label": "cluster", "multi": false, "name": "cluster_name", "options": [], "query": "SELECT  DISTINCT cluster_name\nFROM cluster_history_telemetry \nWHERE   date >= DATE('${__from:date:YYYY-MM-DD}')\n  AND cluster_type='standard'\n  AND tenant_id='${tenant_id}'\n  AND cluster_name IS NOT NULL", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "1379661944614394066", "value": "1379661944614394066"}, "datasource": "telemetry-db", "definition": "SELECT  DISTINCT cluster_id \nFROM cluster_history_telemetry \nWHERE   date >= DATE('${__from:date:YYYY-MM-DD}')\n  AND cluster_type='standard'\n  AND tenant_id='${tenant_id}'\n  AND cluster_name='${cluster_name}'", "hide": 1, "includeAll": false, "label": "", "multi": false, "name": "cluster_id", "options": [], "query": "SELECT  DISTINCT cluster_id \nFROM cluster_history_telemetry \nWHERE   date >= DATE('${__from:date:YYYY-MM-DD}')\n  AND cluster_type='standard'\n  AND tenant_id='${tenant_id}'\n  AND cluster_name='${cluster_name}'", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "2022/6", "value": "2022/6"}, "datasource": "telemetry-db", "definition": "SELECT month \nFROM sla\nWHERE\n  DATE(CONCAT(month, '/01')) >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n  AND DATE(CONCAT(month, '/01')) <= LAST_DAY($__timeTo())\nGROUP BY month\nORDER BY month ASC", "hide": 0, "includeAll": false, "label": "month", "multi": false, "name": "month", "options": [], "query": "SELECT month \nFROM sla\nWHERE\n  DATE(CONCAT(month, '/01')) >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n  AND DATE(CONCAT(month, '/01')) <= LAST_DAY($__timeTo())\nGROUP BY month\nORDER BY month ASC", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6M", "to": "now"}, "timepicker": {"refresh_intervals": ["1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "utc", "title": "SLA Per Cluster", "uid": "sla-per-cluster", "version": 39, "weekStart": ""}