{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 1, "links": [], "liveNow": false, "panels": [{"datasource": "telemetry-db", "gridPos": {"h": 6, "w": 24, "x": 0, "y": 0}, "id": 30, "options": {"content": "This dashboard visualizes SLAs and MTTRs for all **STANDARD** clusters (excluding Dev Tier Clusters). The data of this dashboard is near real-time: `T+2h`. If you have any questions, please refer to this [user guide](https://pingcap.feishu.cn/wiki/wikcnxyKPDDjGMsm3Aw9KDZqVQf#).\n\nAs our SLA target is `99.99%`, it's devided into three categories according to SLAs:\n\n- **RED**, clusters that miss SLA, **please FOCUS ON these clsuters!**\n- **YELLOW**, clusters that meet SLA but not meet 100%, need to take a look.\n- **GREEN**, perfect clusters! No downtime :)\n\nAnd our MTTR target is `5min`, the formula for calculating MTTR is `sum(downtime)/incidents`, `incidents` means that a continuous downtime.\n\nYou could get more information about missed SLA clusters on `Missed SLA` Panel:\n\n- click the `value` of one cluster, visit the SLA detail of this cluster, it will show you the minutes of downtime.\n- click the `go to debug-portal`, visit debug-portal, it will show you more debug info about this cluster.\n\nWhy use `UTC` as default timezone?\n\nSLA data is calculated by month, it's quite sensitive to timezones.\n\nFor example, if we use `China, CST` timezone here, and want to get SLA data for `2022/5`, then we choose the time range from `2022/05/01 00:00:00` to `2022/05/10 23:59:59`. The result will include SLA data for `2022/4` month, which is not expected!", "mode": "markdown"}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "time_series", "group": [], "metricColumn": "none", "rawQuery": false, "rawSql": "SELECT\n  date AS \"time\",\n  value\nFROM dev_tier_cluster_import\nWHERE\n  $__timeFilter(date)\nORDER BY date", "refId": "A"}], "title": "NOTE", "type": "text"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 10, "panels": [], "title": "Overview", "type": "row"}, {"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 7}, "id": 17, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT\n  count(DISTINCT cluster_id) as metric\nFROM cluster_history_telemetry\nWHERE\n  date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n  AND date <= LAST_DAY($__timeTo())\n  AND cluster_type='standard'", "refId": "A"}], "title": "Total Clusters (active+deleted)", "type": "stat"}, {"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "missed"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "met"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "perfect"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 6, "x": 12, "y": 7}, "id": 6, "options": {"displayLabels": ["percent"], "legend": {"displayMode": "list", "placement": "right", "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  COUNT(IF(value>=1.0000, 1, NULL)) AS perfect,\n  COUNT(IF(value>=0.9999 AND value<1, 1, NULL)) AS met,\n  COUNT(IF(value<0.9999, 1, NULL)) AS missed\nFROM sla\nWHERE \n  (month, cluster_id) in (\n  SELECT \n    DATE_FORMAT(date, '%Y/%c') AS month,\n    cluster_id\n  FROM cluster_history_telemetry\n  WHERE \n    date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n    AND date <= LAST_DAY($__timeTo())\n    AND cluster_type='standard'\n  GROUP BY month, cluster_id\n  )", "refId": "A"}], "title": "SLA Percent", "type": "piechart"}, {"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "missed"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "met"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "perfect"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 7}, "id": 36, "options": {"displayLabels": ["percent"], "legend": {"displayMode": "list", "placement": "right", "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  COUNT(IF(value=0, 1, NULL)) AS perfect,\n  COUNT(IF(value<5 AND value>0, 1, NULL)) AS met,\n  COUNT(IF(value>=5, 1, NULL)) AS missed\nFROM mttr\nWHERE \n  (month, cluster_id) in (\n  SELECT \n    DATE_FORMAT(date, '%Y/%c') AS month,\n    cluster_id\n  FROM cluster_history_telemetry\n  WHERE \n    date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n    AND date <= LAST_DAY($__timeTo())\n    AND cluster_type='standard'\n  GROUP BY month, cluster_id\n  )", "refId": "A"}], "title": "MTTR Percent", "type": "piechart"}, {"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_id"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.width", "value": 203}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.width", "value": 203}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SLA"}, "properties": [{"id": "decimals", "value": 6}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.9999}, {"color": "green", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}, {"id": "custom.width", "value": 90}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "month"}, "properties": [{"id": "custom.width", "value": 90}, {"id": "mappings", "value": [{"options": {"pattern": "(\\d{4}\\/1)", "result": {"color": "semi-dark-red", "index": 0}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/2)", "result": {"color": "semi-dark-orange", "index": 1}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/3)", "result": {"color": "semi-dark-yellow", "index": 2}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/4)", "result": {"color": "semi-dark-green", "index": 3}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/5)", "result": {"color": "semi-dark-blue", "index": 4}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/6)", "result": {"color": "semi-dark-purple", "index": 5}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/7)", "result": {"color": "super-light-red", "index": 6}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/8)", "result": {"color": "super-light-orange", "index": 7}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/9)", "result": {"color": "super-light-yellow", "index": 8}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/10)", "result": {"color": "super-light-green", "index": 9}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/11)", "result": {"color": "super-light-blue", "index": 10}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/12)", "result": {"color": "super-light-purple", "index": 11}}, "type": "regex"}]}, {"id": "custom.displayMode", "value": "color-text"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "diagnosis_link"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "diagnosis link", "url": "https://debug.tidbcloud.com/orgs/${__data.fields.tenant}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_type"}, "properties": [{"id": "mappings", "value": [{"options": {"external": {"color": "orange", "index": 0, "text": "external"}, "internal": {"color": "#8e8e93", "index": 1, "text": "internal"}}, "type": "value"}]}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_plan"}, "properties": [{"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "status"}, "properties": [{"id": "mappings", "value": [{"options": {"active": {"color": "orange", "index": 0, "text": "active"}, "deleted": {"color": "#8e8e93", "index": 1, "text": "deleted"}}, "type": "value"}]}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "custom.width", "value": 77}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "MTTR"}, "properties": [{"id": "custom.width", "value": 130}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 5}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}, {"id": "displayName", "value": "MTTR (minute)"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 13}, "id": 2, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "value"}]}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  sla.month AS month,\n  sla.cluster_id AS cluster_id,\n  cluster_last_update.cluster_name AS cluster_name,\n  IF(cluster_last_update.last_update_date>=DATE(NOW()), 'active', 'deleted') AS status,\n  cluster_records.tenant_id AS tenant,\n  cluster_records.tenant AS tenant_name,\n  'goto debug-portal' AS diagnosis_link,\n  sla.value AS SLA,\n  mttr.value AS MTTR,\n  cluster_records.tenant_type AS tenant_type,\n  cluster_records.tenant_plan AS tenant_plan,\n  cluster_records.cloud_provider AS cloud_provider,\n  cluster_records.region AS region\nFROM sla\nINNER JOIN (\n  SELECT\n    cluster_id,\n    DATE_FORMAT(date, '%Y/%c') as month,\n    tenant,\n    tenant_id,\n    cloud_provider,\n    region,\n    tenant_type,\n    tenant_plan,\n    cluster_name\n  FROM cluster_history_telemetry\n  WHERE\n    (cluster_id,date) IN (\n      SELECT\n        cluster_id,\n        MAX(date) AS date\n      FROM cluster_history_telemetry\n      WHERE\n        date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n        AND date <= LAST_DAY($__timeTo())\n        AND cluster_type='standard'\n      GROUP BY DATE_FORMAT(date, '%Y/%c'), cluster_id\n    )\n) AS cluster_records ON sla.cluster_id=cluster_records.cluster_id AND sla.month=cluster_records.month\nINNER JOIN (\n  SELECT\n    cluster_id, \n    cluster_name,\n    date AS last_update_date\n  FROM cluster_history_telemetry\n  WHERE\n    (cluster_id,date) IN (\n      SELECT\n          cluster_id,\n          MAX(date) AS date\n      FROM cluster_history_telemetry\n      WHERE\n          date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n          AND date <= LAST_DAY($__timeTo())\n          AND cluster_type='standard'\n      GROUP BY cluster_id\n  )\n) AS cluster_last_update ON sla.cluster_id=cluster_last_update.cluster_id\nLEFT JOIN mttr ON sla.cluster_id=mttr.cluster_id AND sla.month=mttr.month\nWHERE sla.value<0.9999\nORDER BY month DESC, status ASC, tenant_type ASC, tenant ASC, SLA ASC", "refId": "A"}], "title": "Missed SLA", "type": "table"}, {"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_id"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.width", "value": 203}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.width", "value": 203}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SLA"}, "properties": [{"id": "decimals", "value": 6}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 0.9999}, {"color": "green", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}, {"id": "custom.width", "value": 90}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "month"}, "properties": [{"id": "custom.width", "value": 90}, {"id": "mappings", "value": [{"options": {"pattern": "(\\d{4}\\/1)", "result": {"color": "semi-dark-red", "index": 0}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/2)", "result": {"color": "semi-dark-orange", "index": 1}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/3)", "result": {"color": "semi-dark-yellow", "index": 2}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/4)", "result": {"color": "semi-dark-green", "index": 3}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/5)", "result": {"color": "semi-dark-blue", "index": 4}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/6)", "result": {"color": "semi-dark-purple", "index": 5}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/7)", "result": {"color": "super-light-red", "index": 6}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/8)", "result": {"color": "super-light-orange", "index": 7}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/9)", "result": {"color": "super-light-yellow", "index": 8}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/10)", "result": {"color": "super-light-green", "index": 9}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/11)", "result": {"color": "super-light-blue", "index": 10}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/12)", "result": {"color": "super-light-purple", "index": 11}}, "type": "regex"}]}, {"id": "custom.displayMode", "value": "color-text"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "diagnosis_link"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "diagnosis link", "url": "https://debug.tidbcloud.com/orgs/${__data.fields.tenant}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_type"}, "properties": [{"id": "mappings", "value": [{"options": {"external": {"color": "orange", "index": 0, "text": "external"}, "internal": {"color": "#8e8e93", "index": 1, "text": "internal"}}, "type": "value"}]}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_plan"}, "properties": [{"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "status"}, "properties": [{"id": "mappings", "value": [{"options": {"active": {"color": "orange", "index": 0, "text": "active"}, "deleted": {"color": "#8e8e93", "index": 1, "text": "deleted"}}, "type": "value"}]}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "custom.width", "value": 77}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "MTTR"}, "properties": [{"id": "custom.width", "value": 128}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 5}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}, {"id": "displayName", "value": "MTTR (minute)"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 21}, "id": 34, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "value"}]}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  sla.month AS month,\n  sla.cluster_id AS cluster_id,\n  cluster_last_update.cluster_name AS cluster_name,\n  IF(cluster_last_update.last_update_date>=DATE(NOW()), 'active', 'deleted') AS status,\n  cluster_records.tenant_id AS tenant,\n  cluster_records.tenant AS tenant_name,\n  'goto debug-portal' AS diagnosis_link,\n  sla.value AS SLA,\n  mttr.value AS MTTR,\n  cluster_records.tenant_type AS tenant_type,\n  cluster_records.tenant_plan AS tenant_plan,\n  cluster_records.cloud_provider AS cloud_provider,\n  cluster_records.region AS region\nFROM sla\nINNER JOIN (\n  SELECT\n    cluster_id,\n    DATE_FORMAT(date, '%Y/%c') as month,\n    tenant,\n    tenant_id,\n    cloud_provider,\n    region,\n    tenant_type,\n    tenant_plan,\n    cluster_name\n  FROM cluster_history_telemetry\n  WHERE\n    (cluster_id,date) IN (\n      SELECT\n        cluster_id,\n        MAX(date) AS date\n      FROM cluster_history_telemetry\n      WHERE\n        date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n        AND date <= LAST_DAY($__timeTo())\n        AND cluster_type='standard'\n      GROUP BY DATE_FORMAT(date, '%Y/%c'), cluster_id\n    )\n) AS cluster_records ON sla.cluster_id=cluster_records.cluster_id AND sla.month=cluster_records.month\nINNER JOIN (\n  SELECT\n    cluster_id, \n    cluster_name,\n    date AS last_update_date\n  FROM cluster_history_telemetry\n  WHERE\n    (cluster_id,date) IN (\n      SELECT\n          cluster_id,\n          MAX(date) AS date\n      FROM cluster_history_telemetry\n      WHERE\n          date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n          AND date <= LAST_DAY($__timeTo())\n          AND cluster_type='standard'\n      GROUP BY cluster_id\n  )\n) AS cluster_last_update ON sla.cluster_id=cluster_last_update.cluster_id\nLEFT JOIN mttr ON sla.cluster_id=mttr.cluster_id AND sla.month=mttr.month\nWHERE sla.value>=0.9999 AND sla.value<1\nORDER BY month DESC, status ASC, tenant_type ASC, tenant ASC, SLA ASC", "refId": "A"}], "title": "Met SLA But Not 100%", "type": "table"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 29}, "id": 24, "panels": [{"datasource": "telemetry-db", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "cluster_id"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.width", "value": 203}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant"}, "properties": [{"id": "unit", "value": "string"}, {"id": "custom.width", "value": 203}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SLA"}, "properties": [{"id": "decimals", "value": 6}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "yellow", "value": 0.9999}, {"color": "green", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}, {"id": "custom.width", "value": 90}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "month"}, "properties": [{"id": "custom.width", "value": 90}, {"id": "mappings", "value": [{"options": {"pattern": "(\\d{4}\\/1)", "result": {"color": "semi-dark-red", "index": 0}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/2)", "result": {"color": "semi-dark-orange", "index": 1}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/3)", "result": {"color": "semi-dark-yellow", "index": 2}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/4)", "result": {"color": "semi-dark-green", "index": 3}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/5)", "result": {"color": "semi-dark-blue", "index": 4}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/6)", "result": {"color": "semi-dark-purple", "index": 5}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/7)", "result": {"color": "super-light-red", "index": 6}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/8)", "result": {"color": "super-light-orange", "index": 7}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/9)", "result": {"color": "super-light-yellow", "index": 8}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/10)", "result": {"color": "super-light-green", "index": 9}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/11)", "result": {"color": "super-light-blue", "index": 10}}, "type": "regex"}, {"options": {"pattern": "(\\d{4}\\/12)", "result": {"color": "super-light-purple", "index": 11}}, "type": "regex"}]}, {"id": "custom.displayMode", "value": "color-text"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "diagnosis_link"}, "properties": [{"id": "links", "value": [{"targetBlank": true, "title": "diagnosis link", "url": "https://debug.tidbcloud.com/orgs/${__data.fields.tenant}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_type"}, "properties": [{"id": "mappings", "value": [{"options": {"external": {"color": "orange", "index": 0, "text": "external"}, "internal": {"color": "#8e8e93", "index": 1, "text": "internal"}}, "type": "value"}]}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "tenant_plan"}, "properties": [{"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "status"}, "properties": [{"id": "mappings", "value": [{"options": {"active": {"color": "orange", "index": 0, "text": "active"}, "deleted": {"color": "#8e8e93", "index": 1, "text": "deleted"}}, "type": "value"}]}, {"id": "custom.displayMode", "value": "color-text"}, {"id": "custom.width", "value": 77}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "MTTR"}, "properties": [{"id": "custom.width", "value": 130}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 5}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}, {"id": "links", "value": [{"targetBlank": true, "title": "cluster detail", "url": "/d/sla-per-cluster/sla-per-cluster?from=${__from}&to=${__to}&var-month=${__data.fields.month}&var-cluster_id=${__data.fields.cluster_id}&var-cluster_name=${__data.fields.cluster_name}&var-tenant_id=${__data.fields.tenant}&var-tenant_name=${__data.fields.tenant_name}﻿"}]}, {"id": "displayName", "value": "MTTR (minute)"}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 30}, "id": 35, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "value"}]}, "pluginVersion": "8.4.7", "targets": [{"datasource": "telemetry-db", "format": "table", "group": [], "metricColumn": "none", "rawQuery": true, "rawSql": "SELECT \n  sla.month AS month,\n  sla.cluster_id AS cluster_id,\n  cluster_last_update.cluster_name AS cluster_name,\n  IF(cluster_last_update.last_update_date>=DATE(NOW()), 'active', 'deleted') AS status,\n  cluster_records.tenant_id AS tenant,\n  cluster_records.tenant AS tenant_name,\n  'goto debug-portal' AS diagnosis_link,\n  sla.value AS SLA,\n  mttr.value AS MTTR,\n  cluster_records.tenant_type AS tenant_type,\n  cluster_records.tenant_plan AS tenant_plan,\n  cluster_records.cloud_provider AS cloud_provider,\n  cluster_records.region AS region\nFROM sla\nINNER JOIN (\n  SELECT\n    cluster_id,\n    DATE_FORMAT(date, '%Y/%c') as month,\n    tenant,\n    tenant_id,\n    cloud_provider,\n    region,\n    tenant_type,\n    tenant_plan,\n    cluster_name\n  FROM cluster_history_telemetry\n  WHERE\n    (cluster_id,date) IN (\n      SELECT\n        cluster_id,\n        MAX(date) AS date\n      FROM cluster_history_telemetry\n      WHERE\n        date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n        AND date <= LAST_DAY($__timeTo())\n        AND cluster_type='standard'\n      GROUP BY DATE_FORMAT(date, '%Y/%c'), cluster_id\n    )\n) AS cluster_records ON sla.cluster_id=cluster_records.cluster_id AND sla.month=cluster_records.month\nINNER JOIN (\n  SELECT\n    cluster_id, \n    cluster_name,\n    date AS last_update_date\n  FROM cluster_history_telemetry\n  WHERE\n    (cluster_id,date) IN (\n      SELECT\n          cluster_id,\n          MAX(date) AS date\n      FROM cluster_history_telemetry\n      WHERE\n          date >= DATE(DATE_SUB($__timeFrom(),INTERVAL DAYOFMONTH($__timeFrom())-1 DAY))\n          AND date <= LAST_DAY($__timeTo())\n          AND cluster_type='standard'\n      GROUP BY cluster_id\n  )\n) AS cluster_last_update ON sla.cluster_id=cluster_last_update.cluster_id\nLEFT JOIN mttr ON sla.cluster_id=mttr.cluster_id AND sla.month=mttr.month\nORDER BY month DESC, status ASC, tenant_type ASC, tenant ASC, SLA ASC", "refId": "A"}], "title": "ALL", "type": "table"}], "title": "Additional", "type": "row"}], "schemaVersion": 35, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-6M", "to": "now"}, "timepicker": {"hidden": false, "refresh_intervals": ["15m", "30m", "1h", "2h", "1d"]}, "timezone": "utc", "title": "SLA Overview", "uid": "sla-overview", "version": 81, "weekStart": ""}