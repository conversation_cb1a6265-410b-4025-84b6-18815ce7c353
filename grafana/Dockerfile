FROM grafana/grafana:9.1.5

USER root

ENV GRAFANA_LOKI_PLUGIN_VERSION=0.1.2

# copy dashboards from https://github.com/pingcap/monitoring/tree/auto-generate-for-v4.0.7/monitor-snapshot/v4.0.7/operator/dashboards
# 1. add `kubernetes_namespace=\"$cluster_id\"` in every panels
# 2. update some exprs duration from [30s] to [1m], otherwise will no datapoints
# on the other hands, i donot use configmap manage dashboards like kubernetes grafana dashboards.
# becuase tidb.json and tikv_details.json is too large, and it will exceed the size allowed by configmap.
COPY tidb-cluster-dashboards/ /grafana-dashboard-definitions/2/

COPY capacity-planning-dashboards /grafana-dashboard-definitions/3/

COPY dbaas-logging-dashboards /grafana-dashboard-definitions/1

COPY dbaas-dashboards /grafana-dashboard-definitions/1

COPY nginx-ingress-dashboards /grafana-dashboard-definitions/4

COPY prometheus-alerts-dashboards /grafana-dashboard-definitions/5

COPY loki-logging-quick-search-dashboards /grafana-dashboard-definitions/6

COPY loki-dashboards /grafana-dashboard-definitions/7

COPY thanos /grafana-dashboard-definitions/8

COPY fluent-bit-dashboards /grafana-dashboard-definitions/9

COPY cert-manager-dashboards /grafana-dashboard-definitions/10

COPY one-screen /grafana-dashboard-definitions/11

COPY sli /grafana-dashboard-definitions/12

COPY user-alerts /grafana-dashboard-definitions/13

COPY service-level-management /grafana-dashboard-definitions/14

COPY gitops /grafana-dashboard-definitions/15

COPY ecosystem-dashboards /grafana-dashboard-definitions/16

# Copy to the Kubernetes folder
COPY kubernetes-dashboards /grafana-dashboard-definitions/0

RUN mkdir /grafana-plugins \
  && wget https://github.com/baurine/customized-loki-ds-plugin/releases/download/v${GRAFANA_LOKI_PLUGIN_VERSION}/pingcap-customized-loki-${GRAFANA_LOKI_PLUGIN_VERSION}.zip \
  && unzip pingcap-customized-loki-${GRAFANA_LOKI_PLUGIN_VERSION}.zip -d /grafana-plugins
