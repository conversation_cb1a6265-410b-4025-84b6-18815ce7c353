{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 79, "iteration": 1603953067415, "links": [], "panels": [{"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 24, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 6}, "hiddenSeries": false, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_memstats_alloc_bytes{namespace=\"$namespace\",job=~\"$job\",kubernetes_pod_name=~\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "alloc all {{pod}}", "legendLink": null, "refId": "A", "step": 10}, {"expr": "go_memstats_heap_alloc_bytes{namespace=\"$namespace\",job=~\"$job\",kubernetes_pod_name=~\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "alloc heap {{pod}}", "legendLink": null, "refId": "B", "step": 10}, {"expr": "rate(go_memstats_alloc_bytes_total{namespace=\"$namespace\",job=~\"$job\",kubernetes_pod_name=~\"$pod\"}[30s])", "format": "time_series", "intervalFactor": 2, "legendFormat": "alloc rate all {{pod}}", "legendLink": null, "refId": "C", "step": 10}, {"expr": "rate(go_memstats_heap_alloc_bytes{namespace=\"$namespace\",job=~\"$job\",kubernetes_pod_name=~\"$pod\"}[30s])", "format": "time_series", "intervalFactor": 2, "legendFormat": "alloc rate heap {{pod}}", "legendLink": null, "refId": "D", "step": 10}, {"expr": "go_memstats_stack_inuse_bytes{namespace=\"$namespace\",job=~\"$job\",kubernetes_pod_name=~\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "inuse stack {{pod}}", "legendLink": null, "refId": "E", "step": 10}, {"expr": "go_memstats_heap_inuse_bytes{namespace=\"$namespace\",job=~\"$job\",kubernetes_pod_name=~\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "inuse heap {{pod}}", "legendLink": null, "refId": "F", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Used", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 6}, "hiddenSeries": false, "id": 17, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_goroutines{namespace=\"$namespace\",job=~\"$job\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Goroutines", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 6}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "go_gc_duration_seconds{namespace=\"$namespace\",job=~\"$job\",kubernetes_pod_name=~\"$pod\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{quantile}} {{pod}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC Time Quantiles", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Resources", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 19, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 1}, "hiddenSeries": false, "id": 1, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (strategy) (rate(prometheus_rule_evaluations_total{namespace=\"$namespace\",job=\"$job\"}[$interval]))\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ strategy }}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rule Group Evaluations", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 1}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (strategy) (increase(prometheus_rule_group_iterations_missed_total{namespace=\"$namespace\",job=\"$job\"}[$interval]))\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ strategy }}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rule Group Evaluations Missed", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 1}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(\n  max by(rule_group) (prometheus_rule_group_last_duration_seconds{namespace=\"$namespace\",job=\"$job\"})\n  >\n  sum by(rule_group) (prometheus_rule_group_interval_seconds{namespace=\"$namespace\",job=\"$job\"})\n)\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{ rule_group }}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rule Group Evlauations Too Slow", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "Rule Group Evaluations", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 20, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows rate of dropped alerts.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 2}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(thanos_alert_sender_alerts_dropped_total{namespace=\"$namespace\",job=~\"$job\"}[$interval])) by (job, alertmanager)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{alertmanager}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Dropped Rate", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows rate of alerts that successfully sent to alert manager.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 2}, "hiddenSeries": false, "id": 5, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(thanos_alert_sender_alerts_sent_total{namespace=\"$namespace\",job=~\"$job\"}[$interval])) by (job, alertmanager)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{alertmanager}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Sent Rate", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"error": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows ratio of errors compared to the total number of sent alerts.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 2}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(thanos_alert_sender_errors_total{namespace=\"$namespace\",job=~\"$job\"}[$interval])) / sum(rate(thanos_alert_sender_alerts_sent_total{namespace=\"$namespace\",job=~\"$job\"}[$interval]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "error", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows how long has it taken to send alerts to alert manager.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 2}, "hiddenSeries": false, "id": 7, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(thanos_alert_sender_latency_seconds_bucket{namespace=\"$namespace\",job=~\"$job\"}[$interval])) by (job, le)) * 1", "format": "time_series", "intervalFactor": 2, "legendFormat": "P99 {{job}}", "refId": "A", "step": 10}, {"expr": "sum(rate(thanos_alert_sender_latency_seconds_sum{namespace=\"$namespace\",job=~\"$job\"}[$interval])) by (job) * 1 / sum(rate(thanos_alert_sender_latency_seconds_count{namespace=\"$namespace\",job=~\"$job\"}[$interval])) by (job)", "format": "time_series", "intervalFactor": 2, "legendFormat": "mean {{job}}", "refId": "B", "step": 10}, {"expr": "histogram_quantile(0.50, sum(rate(thanos_alert_sender_latency_seconds_bucket{namespace=\"$namespace\",job=~\"$job\"}[$interval])) by (job, le)) * 1", "format": "time_series", "intervalFactor": 2, "legendFormat": "P50 {{job}}", "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "<PERSON><PERSON>", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "<PERSON><PERSON>", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 21, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows rate of queued alerts.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 3}, "hiddenSeries": false, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(thanos_alert_queue_alerts_dropped_total{namespace=\"$namespace\",job=~\"$job\"}[$interval])) by (job, pod)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod}}", "legendLink": null, "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Push Rate", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"error": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows ratio of dropped alerts compared to the total number of queued alerts.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 3}, "hiddenSeries": false, "id": 9, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(thanos_alert_queue_alerts_dropped_total{namespace=\"$namespace\",job=~\"$job\"}[$interval])) / sum(rate(thanos_alert_queue_alerts_pushed_total{namespace=\"$namespace\",job=~\"$job\"}[$interval]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "error", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Drop Ratio", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "<PERSON><PERSON>", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 22, "panels": [{"aliasColors": {"Aborted": "#EAB839", "AlreadyExists": "#7EB26D", "Canceled": "#E24D42", "DataLoss": "#E24D42", "DeadlineExceeded": "#E24D42", "FailedPrecondition": "#6ED0E0", "Internal": "#E24D42", "InvalidArgument": "#EF843C", "NotFound": "#EF843C", "OK": "#7EB26D", "OutOfRange": "#E24D42", "PermissionDenied": "#EF843C", "ResourceExhausted": "#E24D42", "Unauthenticated": "#EF843C", "Unavailable": "#E24D42", "Unimplemented": "#6ED0E0", "Unknown": "#E24D42", "error": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows rate of handled Unary gRPC requests.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 4}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(grpc_server_handled_total{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"unary\"}[$interval])) by (job, grpc_method, grpc_code)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} {{grpc_method}} {{grpc_code}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rate", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"error": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows ratio of errors compared to the total number of handled requests.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 4}, "hiddenSeries": false, "id": 11, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(grpc_server_handled_total{grpc_code=~\"Unknown|ResourceExhausted|Internal|Unavailable|DataLoss\",namespace=\"$namespace\",job=~\"$job\",grpc_type=\"unary\"}[$interval])) / sum(rate(grpc_server_started_total{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"unary\"}[$interval]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "error", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Errors", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows how long has it taken to handle requests, in quantiles.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 4}, "hiddenSeries": false, "id": 12, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(grpc_server_handling_seconds_bucket{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"unary\"}[$interval])) by (job, grpc_method, le)) * 1", "format": "time_series", "intervalFactor": 2, "legendFormat": "P99 {{job}} {{grpc_method}}", "legendLink": null, "refId": "A", "step": 10}, {"expr": "sum(rate(grpc_server_handling_seconds_sum{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"unary\"}[$interval])) by (job) * 1\n/\nsum(rate(grpc_server_handling_seconds_count{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"unary\"}[$interval])) by (job)\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "mean {{job}} {{grpc_method}}", "legendLink": null, "refId": "B", "step": 10}, {"expr": "histogram_quantile(0.50, sum(rate(grpc_server_handling_seconds_bucket{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"unary\"}[$interval])) by (job, grpc_method, le)) * 1", "format": "time_series", "intervalFactor": 2, "legendFormat": "P50 {{job}} {{grpc_method}}", "legendLink": null, "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "gRPC (Unary)", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 23, "panels": [{"aliasColors": {"Aborted": "#EAB839", "AlreadyExists": "#7EB26D", "Canceled": "#E24D42", "DataLoss": "#E24D42", "DeadlineExceeded": "#E24D42", "FailedPrecondition": "#6ED0E0", "Internal": "#E24D42", "InvalidArgument": "#EF843C", "NotFound": "#EF843C", "OK": "#7EB26D", "OutOfRange": "#E24D42", "PermissionDenied": "#EF843C", "ResourceExhausted": "#E24D42", "Unauthenticated": "#EF843C", "Unavailable": "#E24D42", "Unimplemented": "#6ED0E0", "Unknown": "#E24D42", "error": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows rate of handled Streamed gRPC requests.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 5}, "hiddenSeries": false, "id": 13, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(grpc_server_handled_total{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"server_stream\"}[$interval])) by (job, grpc_method, grpc_code)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{job}} {{grpc_method}} {{grpc_code}}", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Rate", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"error": "#E24D42"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows ratio of errors compared to the total number of handled requests.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 5}, "hiddenSeries": false, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 0, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(grpc_server_handled_total{grpc_code=~\"Unknown|ResourceExhausted|Internal|Unavailable|DataLoss\",namespace=\"$namespace\",job=~\"$job\",grpc_type=\"server_stream\"}[$interval])) / sum(rate(grpc_server_started_total{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"server_stream\"}[$interval]))", "format": "time_series", "intervalFactor": 2, "legendFormat": "error", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Errors", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Shows how long has it taken to handle requests, in quantiles", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 5}, "hiddenSeries": false, "id": 15, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"dataLinks": []}, "percentage": false, "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "histogram_quantile(0.99, sum(rate(grpc_server_handling_seconds_bucket{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"server_stream\"}[$interval])) by (job, grpc_method, le)) * 1", "format": "time_series", "intervalFactor": 2, "legendFormat": "P99 {{job}} {{grpc_method}}", "legendLink": null, "refId": "A", "step": 10}, {"expr": "sum(rate(grpc_server_handling_seconds_sum{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"server_stream\"}[$interval])) by (job) * 1\n/\nsum(rate(grpc_server_handling_seconds_count{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"server_stream\"}[$interval])) by (job)\n", "format": "time_series", "intervalFactor": 2, "legendFormat": "mean {{job}} {{grpc_method}}", "legendLink": null, "refId": "B", "step": 10}, {"expr": "histogram_quantile(0.50, sum(rate(grpc_server_handling_seconds_bucket{namespace=\"$namespace\",job=~\"$job\",grpc_type=\"server_stream\"}[$interval])) by (job, grpc_method, le)) * 1", "format": "time_series", "intervalFactor": 2, "legendFormat": "P50 {{job}} {{grpc_method}}", "legendLink": null, "refId": "C", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Duration", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "repeat": null, "title": "gRPC (Stream)", "type": "row"}], "refresh": "10s", "schemaVersion": 25, "style": "dark", "tags": ["thanos-mixin"], "templating": {"list": [{"current": {"selected": false, "text": "prometheus", "value": "prometheus"}, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "monitoring", "value": "monitoring"}, "datasource": "$datasource", "definition": "", "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": "label_values(kube_pod_info{}, namespace)", "refresh": 1, "regex": "monitoring", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "thanos-rule.*", "current": {"selected": false, "text": "thanos-rule", "value": "thanos-rule"}, "datasource": "$datasource", "definition": "label_values(up{namespace=\"$namespace\",job=~\"thanos-rule.*\"}, job)", "hide": 0, "includeAll": false, "label": "job", "multi": false, "name": "job", "options": [], "query": "label_values(up{namespace=\"$namespace\",job=~\"thanos-rule.*\"}, job)", "refresh": 1, "regex": "thanos-rule", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "$datasource", "definition": "", "hide": 0, "includeAll": true, "label": "pod", "multi": false, "name": "pod", "options": [], "query": "label_values(kube_pod_info{namespace=\"$namespace\",created_by_name=~\"thanos-rule.*\"}, pod)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"auto": true, "auto_count": 300, "auto_min": "10s", "current": {"selected": false, "text": "5m", "value": "5m"}, "hide": 0, "label": "interval", "name": "interval", "options": [{"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": true, "text": "5m", "value": "5m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}], "query": "5m,10m,30m,1h,6h,12h", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "UTC", "title": "Thanos / Rule", "uid": "35da848f5f92b2dc612e0c3a0577b8a1", "version": 4}