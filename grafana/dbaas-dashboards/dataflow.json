{"annotations": {"list": [{"builtIn": 1, "datasource": "thanos", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 437, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 93, "panels": [], "title": "Eventbus", "type": "row"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 1}, "id": 95, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(dataflow_eventbus_events_received_total{source=\"mq\"}[2m])) by (event_type, group) / 2", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Event Recieved From MQ", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 1}, "id": 103, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(dataflow_eventbus_execution_time_seconds_bucket{source=\"mq\"}[2m])) by (le)) / 2", "legendFormat": "p99", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(dataflow_eventbus_execution_time_seconds_bucket{source=\"mq\"}[2m])) by (le)) / 2", "hide": false, "legendFormat": "p90", "range": true, "refId": "B"}, {"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(dataflow_eventbus_execution_time_seconds_bucket{source=\"mq\"}[2m])) by (le)) / 2", "hide": false, "legendFormat": "p50", "range": true, "refId": "C"}], "title": "Event Handler Process Duration", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["dev-tier-cluster-created"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 11}, "id": 97, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(dataflow_eventbus_events_published_total{source=\"publisher\", target=\"mq\"}[2m])) by (event_type) / 2", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Event Published To MQ Directly", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 11}, "id": 99, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(dataflow_eventbus_events_published_total{source=\"publisher\", target=\"db\"}[2m])) by (event_type) / 2", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(dataflow_eventbus_events_published_total{source=\"db\", target=\"mq\"}[2m])) by (event_type) / 2", "hide": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Events Published Transactionly", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 21}, "id": 101, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(dataflow_eventbus_events_published_total{source=\"consumer\", target=\"dlq\" }[2m])) by (event_type, group) / 2", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Events Enter DLQ", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 21}, "id": 105, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(dataflow_eventbus_events_published_total{success=\"false\"}[2m])) by (source, target, event_type) / 2", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Publishing Failed", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "dataflow", "uid": "wl4IKaK4z", "version": 7, "weekStart": ""}