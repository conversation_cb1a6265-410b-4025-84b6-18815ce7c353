{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Dashboard for monitoring jaeger-all-in-one running in a k8s environment. Relying on the official mirror, four core monitoring indicators of Jaeger Collector are added.", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 12535, "graphTooltip": 0, "id": 349, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 71, "panels": [], "title": "Jaeger Collector", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 68, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(jaeger_collector_traces_received_total{}[5m])) by (svc)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Collector <PERSON> Received", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 69, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(jaeger_collector_traces_saved_by_svc_total{}[5m])) by (svc, result)", "legendFormat": "{{svc}}:{{result}}", "range": true, "refId": "A"}], "title": "Collector Trace Save Result", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 77, "panels": [], "title": "<PERSON><PERSON><PERSON>", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 75, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(jaeger_rpc_http_requests_total[5m]))*60", "legendFormat": "total", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "sum(rate(jaeger_rpc_http_requests_total{status_code=~\"2xx\"}[5m]))*60", "hide": false, "legendFormat": "2xx", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sum(rate(jaeger_rpc_http_requests_total{status_code!=\"2xx\"}[5m]))*60", "hide": false, "legendFormat": "non2xx", "range": true, "refId": "C"}], "title": "QPM", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 73, "options": {"legend": {"calcs": ["max", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(jaeger_rpc_request_latency_sum[5m]))  / sum(rate(jaeger_rpc_request_latency_count[5m])) * 1000 ", "legendFormat": "Latency", "range": true, "refId": "A"}], "title": "Average Latency", "type": "timeseries"}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": ["jaeger", "jaeger-all-in-one"], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "<PERSON><PERSON><PERSON>", "uid": "zLOi95xmk", "version": 6, "weekStart": ""}