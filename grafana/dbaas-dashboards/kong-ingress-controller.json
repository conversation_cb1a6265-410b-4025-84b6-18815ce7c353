{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "gnetId": 15662, "graphTooltip": 0, "id": 131, "links": [], "liveNow": false, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 5000}]}, "unit": "ms"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "ingress_controller_configuration_push_duration_milliseconds_bucket{container=\"ingress-controller\", endpoint=\"cmetrics\", instance=\"***********:10255\", job=\"trk-kong-proxy\", le=\"+Inf\", namespace=\"default\", pod=\"trk-kong-688c5dbdf7-p7vjc\", protocol=\"db-less\", service=\"trk-kong-proxy\", success=\"true\"}"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}]}, "gridPos": {"h": 16, "w": 12, "x": 0, "y": 0}, "id": 2, "options": {"bucketOffset": 0, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "targets": [{"exemplar": true, "expr": "ingress_controller_configuration_push_duration_milliseconds_bucket", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Configuration push duration (ms)", "type": "histogram"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.5.2", "targets": [{"exemplar": true, "expr": "ingress_controller_configuration_push_count{success=\"true\"}", "format": "time_series", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Successful configuration pushes", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": -7, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}, {"color": "red", "value": 10}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 4}, "id": 11, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.5.2", "targets": [{"exemplar": true, "expr": "ingress_controller_configuration_push_count{success=\"false\"}", "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Failed configuration pushes", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 8}, "id": 10, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.5.2", "targets": [{"exemplar": true, "expr": "ingress_controller_translation_count{success=\"true\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Successful configuration translations", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 20}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 12, "x": 12, "y": 12}, "id": 12, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.5.2", "targets": [{"exemplar": true, "expr": "ingress_controller_translation_count{success=\"false\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Failed configuration translations", "type": "stat"}], "refresh": "5s", "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Kong ingress controller", "uid": "SaGwMOtnz", "version": 2, "weekStart": ""}