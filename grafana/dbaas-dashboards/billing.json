{"annotations": {"list": [{"builtIn": 1, "datasource": "thanos", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 84, "panels": [], "title": "SLO", "type": "row"}, {"datasource": "thanos", "description": "credits_summary  ||  measures ", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 95}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 86, "interval": "60s", "maxDataPoints": 600, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": "thanos", "editorMode": "code", "exemplar": false, "expr": "sum(increase (billing2_requests_total{service=\"billing2\",code=\"200\", url=~\"(.*)credits_summary||(.*)measures\"   }[2m]) or vector(1) ) / sum(increase (billing2_requests_total{service=\"billing2\",url=~\"(.*)credits_summary||(.*)measures\"  }[2m] )or\n      vector(1) ) ", "instant": false, "interval": "", "legendFormat": "success/all", "range": true, "refId": "A"}], "title": "Inner-RPC-SLO", "type": "timeseries"}, {"datasource": "thanos", "description": "账单查询&下载\n询价\n用户页面修改支付相关信息", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 95}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 10, "x": 13, "y": 1}, "id": 88, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase (billing2_requests_total{service=\"billing2\",code=\"200\", url=~\"(.*)month||(.*)export||(.*)dryrun||(.*)payment_methods||(.*)invoices||(.*)setup_intent\"   }[60m]   )or\n      vector(1)) / sum(increase (billing2_requests_total{service=\"billing2\",url=~\"(.*)month||(.*)export||(.*)dryrun||(.*)payment_methods||(.*)invoices||(.*)setup_intent\"  }[60m] )  or\n      vector(1) )", "interval": "", "legendFormat": "success/all", "range": true, "refId": "A"}], "title": "OfficialWebsiteUserExperience-SLO", "type": "timeseries"}, {"datasource": "thanos", "description": "(.*)tenant_ids||(.*)support_plan||(.*)billing_info||(.*)binded_packages||(.*)binded_package_id||(.*)binded_support_plan||(.*)credits||(.*)invoices||(.*)payments||(.*)customer", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 95}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 90, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase (billing2_requests_total{service=\"billing2\",code=\"200\", url=~\"(.*)tenant_ids||(.*)support_plan||(.*)billing_info||(.*)binded_packages||(.*)binded_package_id||(.*)binded_support_plan||(.*)credits||(.*)invoices||(.*)payments||(.*)customer\"   }[60m]) or\n      vector(1)) / sum(increase (billing2_requests_total{service=\"billing2\",url=~\"(.*)tenant_ids||(.*)support_plan||(.*)billing_info||(.*)binded_packages||(.*)binded_package_id||(.*)binded_support_plan||(.*)credits||(.*)invoices||(.*)payments||(.*)customer\"   }[60m]) or\n      vector(1)) ", "legendFormat": "success/all", "range": true, "refId": "A"}], "title": "InnerUserExperience-SLO", "type": "timeseries"}, {"datasource": "thanos", "description": "影响用户账单感知体验SLO", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_invoice{status=\"init\"}[10m]) ) -sum( delta(dbaas_billing_invoice{status=\"true\"}[10m])) "}, "properties": [{"id": "displayName", "value": "failed"}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["Value"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 10, "x": 13, "y": 9}, "id": 10, "interval": "5min", "maxDataPoints": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum( increase(dbaas_billing_invoice{status=\"true\"}[60m])or\n      vector(1) ) /sum(increase(dbaas_billing_invoice{status=\"init\"}[60m]) or\n      vector(1) ) ", "hide": false, "legendFormat": "success/all", "range": true, "refId": "A"}], "title": "GenerateInvoice-SLO", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 46, "panels": [{"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_invoice{status=\"init\"}[10m]) ) -sum( delta(dbaas_billing_invoice{status=\"true\"}[10m])) "}, "properties": [{"id": "displayName", "value": "failed"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 2}, "id": 48, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_invoice{status=\"init\"}[10m]) ) -sum( delta(dbaas_billing_invoice{status=\"true\"}[10m])) ", "refId": "A"}], "title": "GenerateInvoiceFailedCount", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "byRegexp", "options": "{status=\"init\"}"}, "properties": [{"id": "displayName", "value": "input"}]}, {"matcher": {"id": "byRegexp", "options": "{status=\"true\"}"}, "properties": [{"id": "displayName", "value": "success"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 2}, "id": 50, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_invoice_pay{}[10m])) by (status) ", "refId": "A"}], "title": "PaymentCountInfo", "type": "timeseries"}, {"datasource": "thanos", "description": "API的错误分部", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 10}, "id": 62, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(rate(billing2_requests_total{code!=\"200\"}[1m])) by (path, code) > 0    ", "refId": "A"}], "title": "API Error Per Path", "type": "timeseries"}], "title": "Key-Monitor", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 6, "panels": [{"datasource": "thanos", "description": "measureToInovice", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_measures_success{}[10m]))"}, "properties": [{"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}, {"id": "displayName", "value": "measure-success"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_measures_invoice_success{}[10m]))"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}, {"id": "displayName", "value": "invoice_success"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_measures_input{}[10m])) "}, "properties": [{"id": "displayName", "value": "measure-input"}, {"id": "color", "value": {"mode": "palette-classic"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "measure-input"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 3}, "id": 4, "interval": "5m", "maxDataPoints": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_measures_input{}[10m])) ", "hide": false, "refId": "A"}, {"datasource": "thanos", "expr": "sum(delta(dbaas_billing_measures_success{}[10m]))", "hide": false, "refId": "measures"}, {"datasource": "thanos", "expr": "sum(delta(dbaas_billing_measures_invoice_success{}[10m]))", "hide": false, "refId": "D"}], "title": "billingMeasuresCount", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_measures_failed{}[10m])) "}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}, {"id": "displayName", "value": "measures-failed"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_measures_input{}[10m]) - delta(dbaas_billing_measures_success{}[10m]))"}, "properties": [{"id": "color", "value": {"fixedColor": "yellow", "mode": "fixed"}}, {"id": "displayName", "value": "delta(input - success)"}]}]}, "gridPos": {"h": 8, "w": 10, "x": 13, "y": 3}, "id": 2, "maxDataPoints": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_measures_failed{}[10m])) ", "hide": false, "refId": "A"}, {"datasource": "thanos", "expr": "sum(delta(dbaas_billing_measures_input{}[10m]) - delta(dbaas_billing_measures_success{}[10m]))", "hide": false, "refId": "B"}, {"datasource": "thanos", "expr": "sum(delta(dbaas_billing_measures_input{}[10m])-delta(dbaas_billing_invoice_success{}[10m])  )", "hide": false, "refId": "C"}], "title": "billingMeasureFailedCount", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["{status=\"true\"}"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": false}}]}, {"matcher": {"id": "byRegexp", "options": "{status=\"init\"}"}, "properties": [{"id": "displayName", "value": "inpu-count"}]}, {"matcher": {"id": "byRegexp", "options": "{status=\"true\"}"}, "properties": [{"id": "displayName", "value": "success"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 11}, "id": 8, "interval": "5", "maxDataPoints": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_invoice{}[10m])) by (status) ", "refId": "A"}], "title": "GenerateInvoiceCount", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_invoice{status=\"init\"}[10m]) ) -sum( delta(dbaas_billing_invoice{status=\"true\"}[10m])) "}, "properties": [{"id": "displayName", "value": "failed"}]}]}, "gridPos": {"h": 8, "w": 10, "x": 13, "y": 11}, "id": 91, "interval": "5min", "maxDataPoints": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_invoice{status=\"init\"}[10m]) ) -sum( delta(dbaas_billing_invoice{status=\"true\"}[10m])) ", "hide": false, "refId": "A"}], "title": "GenerateInvoiceFailedCount", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_invoice_send{}[10m])) by (status) ", "refId": "A"}], "title": "InvoiceSendCount", "type": "timeseries"}], "title": "Measures&Invoice", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 44, "panels": [{"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "byRegexp", "options": "{status=\"init\"}"}, "properties": [{"id": "displayName", "value": "input"}]}, {"matcher": {"id": "byRegexp", "options": "{status=\"true\"}"}, "properties": [{"id": "displayName", "value": "success"}]}, {"matcher": {"id": "byRegexp", "options": "{status=\"managedBySales\"}"}, "properties": [{"id": "displayName", "value": "managedBySales"}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["input", "success"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}, "id": 30, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(delta(dbaas_billing_invoice_pay{}[10m])) by (status) ", "legendFormat": "", "range": true, "refId": "A"}], "title": "PaymentCountAll", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_invoice_pay{status=\"init\",paymentType=\"gcp\"}[10m]) ) -sum( delta(dbaas_billing_invoice_pay{status=\"true\",paymentType=\"gcp\"}[10m])) "}, "properties": [{"id": "displayName", "value": "GCP-failed"}]}]}, "gridPos": {"h": 8, "w": 10, "x": 13, "y": 4}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_invoice_pay{status=\"init\",paymentType=\"gcp\"}[10m]) ) -sum( delta(dbaas_billing_invoice_pay{status=\"true\",paymentType=\"gcp\"}[10m])) ", "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum( delta(dbaas_billing_invoice_pay{status=\"true\",paymentType=\"gcp\"}[10m]))", "hide": false, "legendFormat": "GCP-success", "range": true, "refId": "B"}], "title": "GCPPaymentCount", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(delta(dbaas_billing_invoice_pay{status=\"init\",paymentType=\"stripe\"}[10m]) ) -sum( delta(dbaas_billing_invoice_pay{status=\"true\",paymentType=\"stripe\"}[10m]))", "legendFormat": "stripe-failed", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum( delta(dbaas_billing_invoice_pay{status=\"true\",paymentType=\"stripe\"}[10m]))", "hide": false, "legendFormat": "stripe-success", "range": true, "refId": "B"}], "title": "StripePaymentCount", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 10, "x": 13, "y": 12}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(delta(dbaas_billing_invoice_pay{status=\"init\",paymentType=\"aws\"}[10m]) ) -sum( delta(dbaas_billing_invoice_pay{status=\"true\",paymentType=\"aws\"}[10m])) ", "legendFormat": "AWS-failed", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum( delta(dbaas_billing_invoice_pay{status=\"true\",paymentType=\"aws\"}[10m])) ", "hide": false, "legendFormat": "AWS-success", "range": true, "refId": "B"}], "title": "AWSPaymentCount", "type": "timeseries"}], "title": "Payment", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 14, "panels": [{"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_package_change{ method=\"bind\"}[10m]))"}, "properties": [{"id": "displayName", "value": "bind"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_package_change{ method=\"bind\"}[10m]))", "refId": "A"}], "title": "PackageBind", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(delta(dbaas_billing_package_change{ method=\"unbind\"}[10m]))"}, "properties": [{"id": "displayName"}, {"id": "displayName", "value": "unbind"}]}]}, "gridPos": {"h": 8, "w": 10, "x": 13, "y": 5}, "id": 38, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_package_change{ method=\"unbind\"}[10m]))", "refId": "A"}], "title": "PackageUnbind", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 40, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_package_change{ method=\"chargeCredit\"}[10m]))", "refId": "A"}], "title": "PackageChangeCreditCount", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 10, "x": 13, "y": 13}, "id": 42, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(delta(dbaas_billing_package_change{ method=\"chargeBindedDiscount\"}[10m]))\n", "refId": "A"}], "title": "PackageChangeBindedDiscount", "type": "timeseries"}], "title": "PackageInfo", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 12, "panels": [{"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 6}, "id": 26, "options": {"legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase (billing2_requests_total{service=\"billing2\"}[1m]))  by (code)", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "http-code/Min", "type": "piechart"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "sum(rate(billing2_requests_total{}[1m]))"}, "properties": [{"id": "displayName", "value": "QPS-all"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 6}, "id": 52, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(rate(billing2_requests_total{}[1m]))", "refId": "A"}], "title": "QPS", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "id": 56, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(rate(billing2_requests_total{}[1m])) by (code) > 0", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "QPS Per Status Code", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 66, "options": {"legend": {"calcs": ["max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(billing2_request_duration_seconds_bucket{}[1m])) by (le))", "interval": "", "legendFormat": "P95", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(billing2_request_duration_seconds_bucket{}[1m])) by (le))", "hide": false, "legendFormat": "P90", "range": true, "refId": "B"}, {"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(billing2_request_duration_seconds_bucket{}[1m])) by (le))", "hide": false, "legendFormat": "P50", "range": true, "refId": "C"}], "title": "Percentile Latency", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 22}, "id": 58, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(rate(billing2_requests_total{}[1m])) by (url) > 0", "refId": "A"}], "title": "QPS Per Path", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 29}, "id": 60, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(rate(billing2_requests_total{code!=\"200\"}[1m])) by (path, code) > 0", "refId": "A"}], "title": "Error <PERSON>", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 36}, "id": 64, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "expr": "sum(rate(billing2_request_duration_seconds_sum{}[1m])) by (url) / sum(rate(billing2_request_duration_seconds_count{}[1m])) by (url) ", "refId": "A"}], "title": "Average Latency Per Path", "type": "timeseries"}], "title": "Billing-API", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}, "id": 68, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 7}, "hiddenSeries": false, "id": 70, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(kube_pod_container_status_restarts_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"billing(.*)\"}) by (pod)", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:44", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:45", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 7}, "id": 72, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "kube_deployment_spec_replicas{deployment=~\"billing(.*)\", namespace=~\"nightly-ms|staging-ms|prod-ms\"}", "legendFormat": "{{deployment}}.spec", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "kube_deployment_status_replicas{deployment=~\"billing(.*)\", namespace=~\"nightly-ms|staging-ms|prod-ms\"}", "hide": false, "legendFormat": "{{deployment}}.status", "range": true, "refId": "B"}], "title": "Deployment Spec vs Status", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 15}, "hiddenSeries": false, "id": 74, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{ namespace=~\"nightly-ms|staging-ms|prod-ms\",pod=~\"billing(.*)\"}) by (pod)/\nsum(kube_pod_container_resource_limits{ namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"cpu\",pod=~\"billing(.*)\"}) by (pod)", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod CPU Percentage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:192", "format": "percentunit", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:193", "format": "short", "logBase": 1, "max": "1", "min": "0", "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 15}, "hiddenSeries": false, "id": 76, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{ namespace=~\"nightly-ms|staging-ms|prod-ms\",pod=~\"billing(.*)\"})by(pod)/\nsum(kube_pod_container_resource_limits{ namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"memory\",pod=~\"billing(.*)\"}) by (pod)", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod Memory Percentage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:280", "format": "percentunit", "logBase": 1, "show": true}, {"$$hashKey": "object:281", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 23}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{ namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"cpu\",pod=~\"billing(.*)\"}) by (pod)", "legendFormat": "{{pod}}.{request}", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum(kube_pod_container_resource_limits{ namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"cpu\",pod=~\"billing(.*)\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}.{limit}", "range": true, "refId": "B"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{ namespace=~\"nightly-ms|staging-ms|prod-ms\",pod=~\"billing(.*)\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}.{usage}", "range": true, "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Panel TitlePod CPU Usage vs Request vs Limit", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "thanos", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 23}, "hiddenSeries": false, "id": 80, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(container_memory_working_set_bytes{ namespace=~\"nightly-ms|staging-ms|prod-ms\",pod=~\"billing(.*)\"})by(pod)", "legendFormat": "{{pod}}.usage", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{ namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"memory\",pod=~\"billing(.*)\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}.request", "range": true, "refId": "B"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum(kube_pod_container_resource_limits{ namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"memory\",pod=~\"billing(.*)\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}.limit", "range": true, "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Pod Memory Usage vs Request vs Limit", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:444", "format": "bytes", "logBase": 1, "show": true}, {"$$hashKey": "object:445", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "thanos", "description": "检查监控是否拉取正常", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 31}, "id": 82, "options": {"legend": {"displayMode": "list", "placement": "bottom", "showLegend": true}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase (promhttp_metric_handler_requests_total{service=\"billing2\"}[10m]))  by (code)", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "promhttp_metric", "type": "piechart"}], "title": "Pod Status", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 93, "panels": [{"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 24}, "id": 95, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(billing_eventbus_events_received_total{source=\"mq\"}[1m])) by (event_type, group) ", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Event Recieved From MQ", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 24}, "id": 103, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(billing_eventbus_execution_time_seconds_bucket{source=\"mq\"}[1m])) by (le))", "legendFormat": "p99", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(billing_eventbus_execution_time_seconds_bucket{source=\"mq\"}[1m])) by (le))", "hide": false, "legendFormat": "p90", "range": true, "refId": "B"}, {"datasource": "thanos", "editorMode": "code", "expr": "histogram_quantile(0.50, sum(rate(billing_eventbus_execution_time_seconds_bucket{source=\"mq\"}[1m])) by (le))", "hide": false, "legendFormat": "p50", "range": true, "refId": "C"}], "title": "Event Handler Process Duration", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 34}, "id": 97, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(billing_eventbus_events_published_total{source=\"publisher\", target=\"mq\"}[1m])) by (event_type) ", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Event Published To MQ Directly", "type": "timeseries"}, {"datasource": "thanos", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 34}, "id": 99, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(billing_eventbus_events_published_total{source=\"publisher\", target=\"db\"}[1m])) by (event_type) ", "legendFormat": "__auto", "range": true, "refId": "A"}, {"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(billing_eventbus_events_published_total{source=\"db\", target=\"mq\"}[1m])) by (event_type) ", "hide": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "title": "Events Published Transactionly", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 0, "y": 44}, "id": 101, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(billing_eventbus_events_published_total{source=\"consumer\", target=\"dlq\" }[1m])) by (event_type, group)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Events Enter DLQ", "type": "timeseries"}, {"datasource": "thanos", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 11, "x": 11, "y": 44}, "id": 105, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": "thanos", "editorMode": "code", "expr": "sum(increase(billing_eventbus_events_published_total{success=\"false\"}[1m])) by (source, target, event_type) ", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Publishing Failed", "type": "timeseries"}], "title": "Eventbus", "type": "row"}], "refresh": "5m", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "billing", "uid": "oqevsWWMz", "version": 137, "weekStart": ""}