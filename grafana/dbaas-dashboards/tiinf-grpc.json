{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 319, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "Server Traffic", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 4, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\"}[5m]))", "legendFormat": "QPS", "range": true, "refId": "A"}], "title": "QPS", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 12, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\"}[5m])) by (grpc_service, grpc_method)", "legendFormat": "{{grpc_service}}.{{grpc_method}}", "range": true, "refId": "A"}], "title": "QPS Per gRPC Service & Method", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 15, "panels": [], "title": "Server Error (grpc_code!=OK)", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "id": 6, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\", grpc_code!=\"OK\"}[5m])) by (grpc_code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Error <PERSON>", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "id": 24, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\", grpc_code!=\"OK\"}[5m])) by (grpc_service, grpc_method, grpc_code) > 0", "legendFormat": "{{grpc_service}}.{{grpc_method}}:{{grpc_code}}", "range": true, "refId": "A"}], "title": "Error QPS Per gRPC Service & Method", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 16, "options": {"legend": {"calcs": ["min", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Min", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "1 - sum(rate(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\", grpc_code!=\"OK\"}[5m])) / sum(rate(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\"}[5m])) ", "legendFormat": "Success Rate", "range": true, "refId": "A"}], "title": "Success Rate", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 19, "options": {"legend": {"calcs": ["min", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Min", "sortDesc": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "1 - sum(rate(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\", grpc_code!=\"OK\"}[5m])) by (grpc_service, grpc_method) / sum(rate(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\"}[5m])) by (grpc_service, grpc_method)", "legendFormat": "{{grpc_service}}.{{grpc_method}}", "range": true, "refId": "A"}], "title": "Success Rate Per gRPC Service & Method", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 8, "panels": [], "title": "Server Latency", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "id": 9, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(grpc_server_handling_seconds_bucket{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\"}[5m])) by (le)) * 1000", "legendFormat": "P90", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(grpc_server_handling_seconds_bucket{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\"}[5m])) by (le)) * 1000", "hide": false, "legendFormat": "P95", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(grpc_server_handling_seconds_bucket{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\"}[5m])) by (le)) * 1000", "hide": false, "legendFormat": "P99", "range": true, "refId": "C"}], "title": "Percentile Latency", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 27}, "id": 10, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(grpc_server_handling_seconds_bucket{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_service=~\"$grpc_service\", grpc_method=~\"$grpc_method\", grpc_type=\"unary\"}[5m])) by (grpc_service, grpc_method, le)) * 1000 > 0", "hide": false, "legendFormat": "{{grpc_service}}.{{grpc_method}}", "range": true, "refId": "B"}], "title": "P95 Per gRPC Service & Method", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 22, "panels": [], "title": "Client View", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "id": 25, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(grpc_client_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_type=\"unary\"}[5m])) by (grpc_service, grpc_method)", "legendFormat": "{{grpc_service}}.{{grpc_method}}", "range": true, "refId": "A"}], "title": "Client QPS Per gRPC Service & Method", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}, "id": 26, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(grpc_client_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_type=\"unary\", grpc_code!=\"OK\"}[5m])) by (grpc_service, grpc_method, grpc_code) > 0", "legendFormat": "{{grpc_service}}.{{grpc_method}}:{{grpc_code}}", "range": true, "refId": "A"}], "title": "Client Error QPS Per gRPC Service & Method", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 44}, "id": 27, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(grpc_client_handling_seconds_bucket{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_type=\"unary\"}[5m])) by (grpc_service, grpc_method, le)) * 1000 > 0", "hide": false, "legendFormat": "{{grpc_service}}.{{grpc_method}}", "range": true, "refId": "B"}], "title": "Client P95 Per gRPC Service & Method", "type": "timeseries"}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kube_deployment_spec_replicas{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\"}, deployment)", "hide": 0, "includeAll": true, "multi": true, "name": "kube_deployment", "options": [], "query": {"query": "label_values(kube_deployment_spec_replicas{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\"}, deployment)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_type=\"unary\"}, grpc_service)", "hide": 0, "includeAll": true, "multi": true, "name": "grpc_service", "options": [], "query": {"query": "label_values(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_type=\"unary\"}, grpc_service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "definition": "label_values(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_type=\"unary\", grpc_service=~\"$grpc_service\"}, grpc_method)", "hide": 0, "includeAll": true, "multi": true, "name": "grpc_method", "options": [], "query": {"query": "label_values(grpc_server_handled_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\", grpc_type=\"unary\", grpc_service=~\"$grpc_service\"}, grpc_method)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "TiInf gRPC", "uid": "QdHZ0jMVk", "version": 26, "weekStart": ""}