{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 87, "links": [], "liveNow": false, "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 72, "panels": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "id": 78, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(kong_http_status{exported_service=~\".*-ms.account-central-http(.*)\"}[1m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "QPM (account)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "dark-red", "value": 95}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "id": 76, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",code=~\"2(.*)\"} [1m]) or vector(1)) / sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",code=~\"5(.*)|2(.*)\"}[1m]) or vector(1))", "range": true, "refId": "A"}], "title": "SLA (account)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 8}, "id": 80, "options": {"legend": {"calcs": ["max", "mean", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "exemplar": false, "expr": "sum(increase(kong_http_status{exported_service=~\"prod-ms.account-.*\", code=~\"5.*|4.*\"}[1m])) by (exported_service, code)", "instant": false, "interval": "", "legendFormat": "{{exported_service , code}}", "range": true, "refId": "A"}], "title": "错误状态码", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 15}, "hiddenSeries": false, "id": 88, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_container_status_restarts_total{namespace=\"prod-ms\", pod=~\"account-(.*)\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 23}, "hiddenSeries": false, "id": 82, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{cluster=\"base/eks/us-west-2\", namespace=~\".*-ms\", pod=~\"account-(.*)\"}) by (pod)", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "CPU usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:98", "format": "percentunit", "logBase": 1, "show": true}, {"$$hashKey": "object:99", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 32}, "id": 84, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "expr": "sum(container_memory_working_set_bytes{cluster=\"base/eks/us-west-2\",container!=\"\", image!=\"\", pod=~\"account-.*\"}) by (pod) / sum(kube_pod_container_resource_requests{cluster=\"base/eks/us-west-2\",resource=\"memory\",pod=~\"account-.*\"}) by (pod)", "hide": false, "refId": "A"}], "title": "Memory Usage", "type": "timeseries"}], "title": "Overview", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 58, "panels": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 3, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "dark-red", "value": 95}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 11, "x": 0, "y": 2}, "id": 69, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",code=~\"2(.*)\"} [1m]) or vector(1)) / sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",code=~\"5(.*)|2(.*)\"}[1m]) or vector(1))", "range": true, "refId": "A"}], "title": "所有核心接口", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 11, "y": 2}, "id": 60, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "(sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",code=~\"2(.*)\"} [10m])) / sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",code=~\"5(.*)|2(.*)\"}[10m]))) * 100", "range": true, "refId": "A"}], "title": "所有核心接口", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 14, "y": 2}, "id": 70, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "(sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",code=~\"2(.*)\"} [10m])) / sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",code=~\"5(.*)|2(.*)\"}[10m]))) * 100", "range": true, "refId": "A"}], "title": "所有核心接口", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 17, "y": 2}, "id": 64, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "(sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",path=~\"/api/v1/orgs/.*/projects.*\\\\$\",code=~\"2(.*)\"}[1d])) / sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",path=~\"/api/v1/orgs/.*/projects.*\\\\$\",code=~\"5(.*)|2(.*)\"}[1d]))) * 100", "range": true, "refId": "A"}], "title": "project 核心接口", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 20, "y": 2}, "id": 62, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "(1 - sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",path=~\"/api/v1/orgs/.*/api_keys.*\",code=~\"5(.*)\"}[10m])) / sum(increase(kong_custom_http_status{kube_service=~\"(.*)account-central(.*)\",path=~\"/api/v1/orgs/.*/api_keys.*\",code=~\"5(.*)|2(.*)\"}[10m]))) * 100\n", "range": true, "refId": "A"}], "title": "api key 核心接口", "type": "gauge"}], "title": "SLA", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 9, "panels": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 10}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(kong_http_status{exported_service=~\".*-ms.account-central-http(.*)\"}[1m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "QPM (account)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 10}, "id": 31, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(kong_http_status{exported_service=~\".*-ms.account-admin(.*)\"}[1m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "QPM(admin)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 17}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(kong_http_status{exported_service=~\".*-ms.account-openapi(.*)\"}[1m]))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "QPM (openAPI)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 17}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(dbaas_account_login_count{}[2m])) /2", "hide": false, "legendFormat": "{{eventResult}}", "range": true, "refId": "A"}], "title": "平台登录次数(分钟)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 24}, "id": 38, "interval": "3h", "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(dbaas_account_poc_counter{})-sum(dbaas_account_poc_counter{} offset 1d)", "legendFormat": "过去1天申请量", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(dbaas_account_poc_counter{})", "hide": false, "legendFormat": "申请总数量", "range": true, "refId": "C"}], "title": "申请POC", "type": "timeseries"}], "title": "Traffic", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 11, "panels": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["TP90"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 4}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-central-(.*)\"}[1m])) by (le))", "legendFormat": "TP90", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-central-(.*)\"}[1m])) by (le))", "hide": false, "legendFormat": "TP95", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-central-(.*)\"}[1m])) by (le))", "hide": false, "legendFormat": "TP99", "range": true, "refId": "C"}], "title": "account-时延", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["TP90"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 4}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-admin-(.*)\"}[1m])) by (le))", "legendFormat": "TP90", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-admin-(.*)\"}[1m])) by (le))", "hide": false, "legendFormat": "TP95", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-admin-(.*)\"}[1m])) by (le))", "hide": false, "legendFormat": "TP99", "range": true, "refId": "C"}], "title": "admin -时延", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 10}, "id": 39, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.openapi.*\"}[1m])) by (le))", "legendFormat": "central", "range": true, "refId": "A"}], "title": "openApi-时延", "type": "timeseries"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 10}, "hiddenSeries": false, "id": 43, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "exemplar": false, "expr": "dbaas_account_login_cost_summary{quantile=\"0.99\",pod=~\"account-central.*\"}", "hide": false, "legendFormat": "{{pod}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "expr": "dbaas_account_login_cost_gauge", "hide": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "登录耗时", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:134", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:135", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "id": 51, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-.*\",path=\"/api/v1/orgs/[0-9A-Za-z]+$\"}[1m])) by (le))", "legendFormat": "TP90", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-.*\",path=\"/api/v1/orgs/[0-9A-Za-z]+$\"}[1m])) by (le))", "hide": false, "legendFormat": "TP95", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\".*-ms.account-.*\",path=\"/api/v1/orgs/[0-9A-Za-z]+$\"}[1m])) by (le))", "hide": false, "legendFormat": "TP99", "range": true, "refId": "C"}], "title": "核心接口 -时延", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 7, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "id": 44, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "exemplar": false, "expr": "dbaas_account_login_cost_summary{quantile=\"0.99\",pod=~\"account-central.*\"}", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "登录耗时>7S", "type": "timeseries"}], "title": "Latency", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 15, "panels": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 5}, "id": 56, "options": {"displayLabels": ["percent"], "legend": {"displayMode": "list", "placement": "bottom", "showLegend": true, "values": []}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(dbaas_account_login_count{eventResult=\"SUCCESS\"}[$__range]) ) ", "legendFormat": "SUCCESS", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(dbaas_account_login_count{eventResult=\"FAILD\"}[$__range]) ) ", "hide": false, "legendFormat": "FAIL", "range": true, "refId": "B"}], "title": "登录成功率", "type": "piechart"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds", "seriesBy": "last"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 5}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "expr": "sum(increase(dbaas_account_login_count{eventResult=\"FAILD\"}[2m])) by (eventResult)/2", "refId": "A"}], "title": "登录失败", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 12}, "id": 19, "options": {"legend": {"calcs": ["max", "mean", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase (kong_http_status{route=~\".*account-central.*\",code=~\"5.*|4.*\"}[1m]))  by (code)", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "account错误状态码", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 19}, "id": 21, "options": {"legend": {"calcs": ["max", "mean", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase (kong_http_status{route=~\".*account-admin(.*)\",code=~\"5.*|4.*\"}[1m]))  by (code)", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "admin错误状态码", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 26}, "id": 36, "options": {"legend": {"calcs": ["max", "mean", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase (kong_http_status{exported_service=~\".*-ms.account-openapi(.*)\",code=~\"5.*|4.*\"}[1m]))  by (code)", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "opnAPI错误状态码", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 33}, "id": 37, "options": {"legend": {"calcs": ["max", "mean", "sum"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(dbaas_account_third_http_code_counter{module=\"billing2\", code=~\"5.*|4.*\"}[5m])) by (code)", "legendFormat": "billing2:{{code}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(increase(dbaas_account_third_http_code_counter{module=\"salesforce\", code=~\"5.*|4.*\"}[5m])) by (code)", "hide": false, "legendFormat": "salesforce:{{code}}", "range": true, "refId": "B"}], "title": "第三方接口错误状态码", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 10}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 33}, "id": 54, "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.1.5", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "expr": " (sum(dbaas_account_login_count{eventResult=\"FAILD\", namespace=~\".*-ms\"})-sum(dbaas_account_login_count{eventResult=\"FAILD\" ,namespace=~\".*-ms\"} offset 30m))", "refId": "A"}], "title": "登录报警指标", "type": "gauge"}], "title": "Errors", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 13, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 6}, "hiddenSeries": false, "id": 29, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "editorMode": "code", "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{cluster=\"base/eks/us-west-2\", namespace=~\".*-ms\", pod=~\"account-(.*)\"}) by (pod)", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "CPU usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:98", "format": "percentunit", "logBase": 1, "show": true}, {"$$hashKey": "object:99", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 15}, "hiddenSeries": false, "id": 27, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "expr": "sum(container_memory_working_set_bytes{cluster=\"base/eks/us-west-2\", namespace=~\".*-ms\", container!=\"\", image!=\"\",pod=~\"account-.*\"}) by (pod)", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Memory Usage (w/o cache)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:41", "format": "bytes", "logBase": 1, "show": true}, {"$$hashKey": "object:42", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 23}, "id": 50, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"datasource": {"type": "prometheus", "uid": "H0fL_sCGz"}, "expr": "sum(container_memory_working_set_bytes{cluster=\"base/eks/us-west-2\",container!=\"\", image!=\"\", pod=~\"account-.*\"}) by (pod) / sum(kube_pod_container_resource_requests{cluster=\"base/eks/us-west-2\",resource=\"memory\",pod=~\"account-.*\"}) by (pod)", "hide": false, "refId": "A"}], "title": "Memory Usage", "type": "timeseries"}], "title": "Saturation", "type": "row"}], "refresh": "", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "account", "uid": "HeZD7XqGk", "version": 181, "weekStart": ""}