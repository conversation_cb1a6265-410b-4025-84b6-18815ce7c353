{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 279, "iteration": 1659957551506, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 44, "panels": [], "title": "Pod Status", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(kube_pod_container_status_restarts_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:799", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:800", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 6, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "exemplar": true, "expr": "kube_deployment_spec_replicas{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",deployment=~\"$kube_deployment\"}", "hide": false, "interval": "", "legendFormat": "{{deployment}}.{spec}", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "kube_deployment_status_replicas{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",deployment=~\"$kube_deployment\"}", "hide": false, "legendFormat": "{{deployment}}.{status}", "range": true, "refId": "B"}], "title": "Deployment Spec vs Status", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 12, "panels": [], "title": "Pod Resource Usage", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "hiddenSeries": false, "id": 48, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.4", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",pod=~\"$kube_deployment(.*)\"}) by (pod)/\nsum(kube_pod_container_resource_limits{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"cpu\",pod=~\"$kube_deployment(.*)\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Pod CPU Percentage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:79", "format": "percentunit", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:80", "format": "percentunit", "logBase": 1, "max": "1", "min": "0", "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "hiddenSeries": false, "id": 50, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(container_memory_working_set_bytes{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",pod=~\"$kube_deployment(.*)\"})by(pod)/\nsum(kube_pod_container_resource_limits{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"memory\",pod=~\"$kube_deployment(.*)\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod Memory Percentage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:948", "format": "percentunit", "logBase": 1, "show": true}, {"$$hashKey": "object:949", "format": "percentunit", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"editorMode": "code", "exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",pod=~\"$kube_deployment(.*)\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}.{usage}", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"cpu\",pod=~\"$kube_deployment(.*)\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}.{request}", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sum(kube_pod_container_resource_limits{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"cpu\",pod=~\"$kube_deployment(.*)\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}.{limit}", "range": true, "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Pod CPU Usage vs Request vs Limit", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:341", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:342", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 51, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"editorMode": "code", "exemplar": true, "expr": "sum(container_memory_working_set_bytes{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",pod=~\"$kube_deployment(.*)\"})by(pod)", "interval": "", "legendFormat": "{{pod}}.{usage}", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "sum(kube_pod_container_resource_requests{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"memory\",pod=~\"$kube_deployment(.*)\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}.{request}", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sum(kube_pod_container_resource_limits{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\",resource=\"memory\",pod=~\"$kube_deployment(.*)\"}) by (pod)", "hide": false, "legendFormat": "{{pod}}.{limit}", "range": true, "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Pod Memory Usage vs Request vs Limit", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:341", "format": "bytes", "logBase": 1, "show": true}, {"$$hashKey": "object:342", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 53, "panels": [], "title": "Pod Network", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 56, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_transmit_packets_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\"}[5m])) by (pod)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod}}", "refId": "A", "step": 10}], "thresholds": [], "timeRegions": [], "title": "Rate of Pod Transmitted Packets", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:152", "format": "pps", "logBase": 1, "show": true}, {"$$hashKey": "object:153", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 27}, "hiddenSeries": false, "id": 57, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_transmit_packets_dropped_total{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\", pod=~\"$kube_deployment(.*)\"}[1m])) by (pod)", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{pod}}", "refId": "A", "step": 10}], "thresholds": [], "timeRegions": [], "title": "Rate of Pod Transmitted Packets Dropped", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:220", "format": "pps", "logBase": 1, "show": true}, {"$$hashKey": "object:221", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".*", "current": {"selected": false, "text": "All", "value": "$__all"}, "definition": "label_values(kube_deployment_spec_replicas{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\"}, deployment)", "description": "", "hide": 0, "includeAll": true, "label": "", "multi": true, "name": "kube_deployment", "options": [], "query": {"query": "label_values(kube_deployment_spec_replicas{cluster=\"base/eks/us-west-2\", namespace=~\"nightly-ms|staging-ms|prod-ms\"}, deployment)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Pod Overview", "uid": "SzQVcRiVz", "version": 33, "weekStart": ""}