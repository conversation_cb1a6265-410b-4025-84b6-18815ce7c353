{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 240, "iteration": 1659337488293, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "Overview", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fieldConfig": {"defaults": {"unit": "bps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 1}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.5.4", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"editorMode": "code", "expr": "8* sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m]))", "legendFormat": "Overall", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "8* sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (type)", "hide": false, "legendFormat": "__auto", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Bandwidth (kbps)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:50", "format": "bps", "logBase": 1, "show": true}, {"$$hashKey": "object:51", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisGridShow": true, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m]))", "legendFormat": "Request/sec", "range": true, "refId": "A"}], "title": "Requests/sec (1min avg)", "type": "timeseries"}, {"description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_http_status{code=~\"^.+\", instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Status code rate", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 8, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "$__interval_ms / 1000 * sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Status codes stat", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "orange", "value": null}, {"color": "green", "value": -50}, {"color": "#EAB839", "value": 50}, {"color": "super-light-red", "value": 100}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 124, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "($__interval_ms / 1000 * sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (code)) - ($__interval_ms / 1000 * sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m] offset $__range)) by (code))", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Status codes diff (Prev $__range)", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"fillOpacity": 70, "lineWidth": 2, "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "light-yellow", "value": 5}, {"color": "orange", "value": 10}, {"color": "red", "value": 20}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 16, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom"}, "mergeValues": true, "rowHeight": 0.9, "showValue": "auto", "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(increase(kong_http_status{code=~\"^[45].+\", instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Error codes", "type": "state-timeline"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 25}, "id": 12, "options": {"displayLabels": ["name", "percent", "value"], "legend": {"displayMode": "table", "placement": "right", "values": ["value"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}) by (code) - sum(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}offset $__range ) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Status codes", "type": "piechart"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 25}, "id": 14, "options": {"displayLabels": ["name", "percent", "value"], "legend": {"displayMode": "table", "placement": "right", "values": ["value"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(kong_http_status{code=~\"^[45].+\", instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}) by (code) - sum(kong_http_status{code=~\"^[45].+\", instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"} offset $__range ) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Error codes", "type": "piechart"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 20, "options": {"legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(rate(kong_latency_sum{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (type) /sum(rate(kong_latency_count{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (type)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Latency", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisGridShow": true, "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 34, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "percent"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "multi", "sort": "asc"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"1\"}[1m]))", "format": "time_series", "hide": false, "legendFormat": "<1ms", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "sum(increase(kong_latency_bucket{type=\"request\",service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"2\"}[1m])) - sum(increase(kong_latency_bucket{type=\"request\",service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"1\"}[1m]))", "hide": false, "legendFormat": "1-2ms", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"5\"}[1m])) - sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"2\"}[1m]))", "hide": false, "legendFormat": "2-5ms", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"10\"}[1m])) - sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"5\"}[1m]))", "hide": false, "legendFormat": "5-10ms", "range": true, "refId": "D"}, {"editorMode": "code", "expr": "sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"50\"}[1m])) - sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"10\"}[1m]))", "hide": false, "legendFormat": "10-50ms", "range": true, "refId": "E"}, {"editorMode": "code", "expr": "sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"100\"}[1m])) - sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"50\"}[1m]))", "hide": false, "legendFormat": "50-100ms", "range": true, "refId": "F"}, {"editorMode": "code", "expr": "sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"500\"}[1m])) - sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"100\"}[1m]))", "hide": false, "legendFormat": "100-500ms", "range": true, "refId": "G"}, {"editorMode": "code", "expr": "sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"+Inf\"}[1m])) - sum(increase(kong_latency_bucket{type=\"request\", service=~\"$service\", instance=~\"$instance\", route=~\"$route\", exported_service=~\"$exported_service\", le=\"500\"}[1m]))", "hide": false, "legendFormat": "> 500ms", "range": true, "refId": "H"}], "title": "Latency (range)", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "id": 122, "options": {"legend": {"calcs": ["lastNotNull", "mean", "min", "max"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.9, sum(increase(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "90%", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "95%", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "99%", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "histogram_quantile(0.999, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "99.9%", "range": true, "refId": "D"}], "title": "Percentile trend", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 100}, {"color": "orange", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "id": 123, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.9, sum(increase(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "90%", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "95%", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "99%", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "histogram_quantile(0.999, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "99.9%", "range": true, "refId": "D"}], "title": "Percentile stat", "type": "stat"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 49}, "id": 28, "panels": [], "title": "Per node", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 50}, "id": 38, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (instance)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Bandwidth by node (kbps)", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 50}, "id": 36, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (instance)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Requests per node", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 58}, "id": 44, "maxPerRow": 12, "options": {"displayLabels": ["name", "percent", "value"], "legend": {"displayMode": "table", "placement": "right", "values": ["value", "percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "repeat": "instance", "repeatDirection": "h", "targets": [{"editorMode": "code", "expr": "sum(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}) by (code) - sum(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"} offset $__range ) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Status codes on $instance", "type": "piechart"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 66}, "id": 50, "maxPerRow": 12, "options": {"displayLabels": ["name", "value", "percent"], "legend": {"displayMode": "table", "placement": "right", "values": ["value", "percent"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "repeat": "instance", "repeatDirection": "h", "targets": [{"editorMode": "code", "expr": "sum(kong_http_status{code=~\"^[45].+\", instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}) by (code) - sum(kong_http_status{code=~\"^[45].+\", instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"} offset $__range ) by (code) ", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Error codes on $instance", "type": "piechart"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 74}, "id": 58, "maxPerRow": 12, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.4", "repeat": "instance", "repeatDirection": "h", "targets": [{"editorMode": "code", "expr": "sum(rate(kong_latency_sum{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (type) / sum(rate(kong_latency_count{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (type)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Latency on $instance", "type": "stat"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 100}, {"color": "orange", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 82}, "id": 123, "maxPerRow": 12, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.4", "repeat": "instance", "repeatDirection": "h", "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.9, sum(increase(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "90%", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "95%", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "99%", "range": true, "refId": "C"}, {"editorMode": "code", "expr": "histogram_quantile(0.999, sum(rate(kong_latency_bucket{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (le) )", "hide": false, "legendFormat": "99.9%", "range": true, "refId": "D"}], "title": "Percentile stat on $instance", "type": "stat"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 90}, "id": 30, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 91}, "id": 32, "options": {"legend": {"calcs": ["last"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "8* sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (exported_service)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Bandwidth by upstream", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 99}, "id": 34, "options": {"legend": {"calcs": ["last"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (exported_service)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Requests per second by upstream (1m/avg)", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 107}, "id": 26, "options": {"legend": {"calcs": ["last"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(rate(kong_latency_sum{type=\"upstream\", instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (exported_service) / sum(rate(kong_latency_count{type=\"upstream\", instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (exported_service)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Latency by upstream", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 115}, "id": 120, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.4", "repeat": "exported_service", "repeatDirection": "v", "targets": [{"editorMode": "code", "expr": "$__interval_ms / 1000 * sum(irate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Status code on $exported_service", "type": "stat"}], "title": "Per upstream", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 91}, "id": 68, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bps"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 92}, "id": 80, "options": {"legend": {"calcs": ["last", "mean"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "8* sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (route)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Bandwidth by route", "transformations": [], "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 100}, "id": 78, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) by (route)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Requests per route", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 108}, "id": 82, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "8.5.4", "repeat": "route", "repeatDirection": "v", "targets": [{"editorMode": "code", "expr": "sum(increase(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1h])) by (code)", "legendFormat": "__auto", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "sum(increase(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1h]))", "hide": false, "legendFormat": "Total", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m]))", "hide": false, "legendFormat": "Bandwidth", "range": true, "refId": "E"}, {"editorMode": "code", "expr": "sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m]))", "hide": false, "legendFormat": "Request/s", "range": true, "refId": "D"}, {"editorMode": "code", "expr": "sum(rate(kong_latency_sum{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) / sum(rate(kong_latency_count{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m]))", "hide": false, "legendFormat": "Latency", "range": true, "refId": "C"}], "title": "Status code on $route", "type": "stat"}], "title": "Per route", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 92}, "id": 239, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 93}, "id": 241, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m])) / sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m]))", "hide": false, "legendFormat": "total", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\", type=\"egress\"}[1m])) / sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m]))", "hide": false, "legendFormat": "egress", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "sum(rate(kong_bandwidth{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\", type=\"ingress\"}[1m])) / sum(rate(kong_http_status{instance=~\"$instance\", service=~\"$service\", route=~\"$route\", exported_service=~\"$exported_service\"}[1m]))", "hide": false, "legendFormat": "ingress", "range": true, "refId": "C"}], "title": "Traffic per request", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"fillOpacity": 70, "lineWidth": 0, "spanNulls": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red"}, {"color": "orange", "value": 1}, {"color": "orange", "value": 2}, {"color": "yellow", "value": 3}, {"color": "purple", "value": 4}, {"color": "blue", "value": 5}, {"color": "green", "value": 6}, {"color": "#BA43A9", "value": 7}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 93}, "id": 243, "options": {"alignValue": "left", "legend": {"displayMode": "list", "placement": "bottom"}, "mergeValues": true, "rowHeight": 0.9, "showValue": "auto", "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "8.5.4", "targets": [{"editorMode": "code", "expr": "sum(kong_upstream_target_health) by (upstream)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Panel Title", "type": "state-timeline"}], "title": "Other experimental panels", "type": "row"}], "refresh": "", "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_http_status, service)", "hide": 0, "includeAll": true, "multi": true, "name": "service", "options": [], "query": {"query": "label_values(kong_http_status, service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_http_status, instance)", "hide": 0, "includeAll": true, "multi": true, "name": "instance", "options": [], "query": {"query": "label_values(kong_http_status, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_http_status, route)", "hide": 0, "includeAll": true, "multi": true, "name": "route", "options": [], "query": {"query": "label_values(kong_http_status, route)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_http_status, exported_service)", "hide": 0, "includeAll": true, "multi": true, "name": "exported_service", "options": [], "query": {"query": "label_values(kong_http_status, exported_service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}, {"allValue": ".+", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_upstream_target_health, upstream)", "hide": 0, "includeAll": true, "multi": true, "name": "upstream", "options": [], "query": {"query": "label_values(kong_upstream_target_health, upstream)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "type": "query"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Gateway Overview", "uid": "uOEzmugVz", "version": 87, "weekStart": ""}