{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 300, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "Traffic", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 4, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"$status_code\"}[5m]))", "legendFormat": "Request/s", "range": true, "refId": "A"}], "title": "QPS", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 43, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"$status_code\"}[5m])) by (code) > 0", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "QPS Per Status Code", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 9}, "id": 5, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Mean", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"$status_code\"}[5m])) by (path) > 0", "legendFormat": "{{route}}", "range": true, "refId": "A"}], "title": "QPS Per Path", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 8, "panels": [], "title": "Error(4xx/5xx)", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 18}, "id": 20, "options": {"displayLabels": ["percent", "value"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}) by (code) - sum(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"} offset $__range) by (code)", "hide": false, "legendFormat": "__auto", "range": true, "refId": "A"}, {"expr": "sum(kong_http_status{}) by (code) - sum(kong_http_status{}offset $__range ) by (code)", "hide": true, "refId": "B"}], "title": "Status Code Percentage", "type": "piechart"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 18}, "id": 22, "options": {"displayLabels": ["percent", "value"], "legend": {"displayMode": "table", "placement": "right", "showLegend": true, "values": ["percent", "value"]}, "pieType": "donut", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"4(.*)|5(.*)\"}) by (code) - sum(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"4(.*)|5(.*)\"} offset $__range) by (code)", "hide": false, "legendFormat": "__auto", "range": true, "refId": "A"}, {"expr": "sum(kong_http_status{}) by (code) - sum(kong_http_status{}offset $__range ) by (code)", "hide": true, "refId": "B"}], "title": "Error <PERSON>", "type": "piechart"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 23, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"4(.*)|5(.*)\"}[5m])) by (code) > 0", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "Error", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 26}, "id": 34, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"4(.*)|5(.*)\"}[5m])) by (path, code) > 0", "legendFormat": "{{path}}:{{code}}", "range": true, "refId": "A"}], "title": "Error <PERSON>", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 34}, "id": 10, "panels": [], "title": "Latency", "type": "row"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 35}, "id": 16, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_request_latency_ms_sum{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m]))/ sum(rate(kong_custom_request_latency_ms_count{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m]))", "hide": false, "legendFormat": "Mean", "range": true, "refId": "A"}], "title": "Average Latency", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 35}, "id": 19, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.95, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (le))", "hide": false, "legendFormat": "P95", "range": true, "refId": "A"}, {"editorMode": "code", "expr": "histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (le))", "hide": false, "legendFormat": "P90", "range": true, "refId": "B"}, {"editorMode": "code", "expr": "histogram_quantile(0.99, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (le))", "hide": false, "legendFormat": "P99", "range": true, "refId": "C"}], "title": "Percentile Latency", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 43}, "id": 17, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "60000 > sum(rate(kong_custom_request_latency_ms_sum{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (path) / sum(rate(kong_custom_request_latency_ms_count{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (path) > 0", "hide": false, "legendFormat": "{{kube_service}}", "range": true, "refId": "A"}], "title": "Average Latency Per Path", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 51}, "id": 15, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 44}, "id": 13, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"$status_code\"}[5m])) by (kube_service) > 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "QPS Per Kubernetes Service", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 44}, "id": 18, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"4(.*)|5(.*)\"}[5m])) by (kube_service, code) > 0", "legendFormat": "{{kube_service}}:{{code}}", "range": true, "refId": "A"}], "title": "Error Per Ku<PERSON>netes Service", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 52}, "id": 12, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "60000 > sum(rate(kong_custom_request_latency_ms_sum{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (kube_service) / sum(rate(kong_custom_request_latency_ms_count{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (kube_service) > 0", "hide": false, "legendFormat": "{{kube_service}}", "range": true, "refId": "A"}], "title": "Average Latency Per Kubernetes Service", "type": "timeseries"}], "title": "Kubernetes Service View", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 52}, "id": 25, "panels": [{"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 5}, "id": 26, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"$status_code\"}[5m])) by (host) > 0", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "QPS Per Host", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 5}, "id": 27, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\", code=~\"4(.*)|5(.*)\"}[5m])) by (host, code) > 0", "legendFormat": "{{host}}:{{code}}", "range": true, "refId": "A"}], "title": "Error <PERSON>", "type": "timeseries"}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 13}, "id": 28, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "60000 > sum(rate(kong_custom_request_latency_ms_sum{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (host) / sum(rate(kong_custom_request_latency_ms_count{kube_service=~\"$kube_service\", host=~\"$host\", path=~\"$path\"}[1m])) by (host) > 0", "hide": false, "legendFormat": "{{kube_service}}", "range": true, "refId": "A"}], "title": "Average Latency Per Host", "type": "timeseries"}], "title": "Host View", "type": "row"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 53}, "id": 36, "panels": [], "title": "For OPS", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 54}, "hiddenSeries": false, "id": 40, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 0.5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"editorMode": "code", "exemplar": true, "expr": "sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{cluster=\"base/eks/us-west-2\", namespace=\"kong\"}) by (pod)/\nsum(kube_pod_container_resource_limits{cluster=\"base/eks/us-west-2\", namespace=\"kong\", resource=\"cpu\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Pod CPU Percentage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:79", "format": "percentunit", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:80", "format": "percentunit", "logBase": 1, "max": "1", "min": "0", "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 54}, "hiddenSeries": false, "id": 42, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"editorMode": "code", "exemplar": true, "expr": "sum(container_memory_working_set_bytes{cluster=\"base/eks/us-west-2\", namespace=\"kong\"})by(pod)/\nsum(kube_pod_container_resource_limits{cluster=\"base/eks/us-west-2\", namespace=\"kong\",resource=\"memory\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod Memory Percentage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:948", "format": "percentunit", "logBase": 1, "show": true}, {"$$hashKey": "object:949", "format": "percentunit", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 62}, "hiddenSeries": false, "id": 38, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.5", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_container_status_restarts_total{cluster=\"base/eks/us-west-2\", namespace=\"kong\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Pod <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:799", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:800", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 62}, "id": 6, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(kong_custom_http_status{host=~\"$host\", path=~\"/status(.*)\"}[5m])) by (code) > 0", "legendFormat": "{{code}}", "range": true, "refId": "A"}], "title": "Probe Status", "type": "timeseries"}], "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": [{"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_custom_http_status, kube_service)", "description": "业务线", "hide": 0, "includeAll": true, "multi": true, "name": "kube_service", "options": [], "query": {"query": "label_values(kong_custom_http_status, kube_service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_custom_http_status{kube_service=~\"$kube_service\"}, host)", "hide": 0, "includeAll": true, "multi": true, "name": "host", "options": [], "query": {"query": "label_values(kong_custom_http_status{kube_service=~\"$kube_service\"}, host)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\"}, path)", "hide": 0, "includeAll": true, "multi": true, "name": "path", "options": [], "query": {"query": "label_values(kong_custom_http_status{kube_service=~\"$kube_service\", host=~\"$host\"}, path)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": ".*", "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "definition": "label_values(kong_custom_http_status{kube_service=~\"$kube_service\"}, code)", "hide": 0, "includeAll": true, "multi": true, "name": "status_code", "options": [], "query": {"query": "label_values(kong_custom_http_status{kube_service=~\"$kube_service\"}, code)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"nowDelay": ""}, "timezone": "", "title": "API Gateway(Kong)", "uid": "6abxfwgVk", "version": 23, "weekStart": ""}