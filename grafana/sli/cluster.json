{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 1, "iteration": 1623147716880, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 2, "panels": [], "title": "SLI", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"unit": "percentunit"}, "overrides": []}, "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 1}, "hiddenSeries": false, "id": 4, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 0, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"exemplar": true, "expr": "    max (\n      (\n        increase(reporter_service_level_probe_cluster_counter{cluster_id=\"$cluster_id\", status=\"alive\"}[1m:1m])   # How many successful probes in the last 1 minute for each minute\n        /ignoring(status)\n        sum without (status)(increase(reporter_service_level_probe_cluster_counter{cluster_id=\"$cluster_id\"}[1m:1m]))   # How many total probes (include failure probes) in the last 1 minute for each minute\n      ) OR on() vector(0)  # If there is no data, treat it as 0%\n    )   # There are two reporters deployed. As long as one reporter can establish connection we treat it as available.\n    > bool 0   # As long as there is >0% availability in 1 minute, this minute is available and treat it as a 100% available minute.", "interval": "", "legendFormat": "SuccessRate", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Connection Availability", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 1}, "hiddenSeries": false, "id": 10, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 0, "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": true, "targets": [{"exemplar": true, "expr": "sum by (status) (increase(reporter_service_level_probe_cluster_counter{cluster_id=\"$cluster_id\", status!=\"alive\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{ status }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Failure Connection Probes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 1}, "hiddenSeries": false, "id": 12, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 0, "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": true, "targets": [{"exemplar": true, "expr": "sum by (status) (increase(reporter_service_level_probe_cluster_counter{cluster_id=\"$cluster_id\", status=\"alive\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{ status }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Success Connection Probes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 7}, "hiddenSeries": false, "id": 6, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 0, "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"exemplar": true, "expr": "1 - max((\n  (sum(rate(tidb_server_execute_error_total{kubernetes_namespace=\"tidb$cluster_id\", type=~\"[^:]*:(8021|8023|8024|8026|8027|8028|8039|8041|8042|8043|8044|8045|8046|8049|8050|8051|8052|8053|8054|8056|8057|8058|8059|8062|8106|8114|8117|8118|8120|8122|8201|8202|8203|8204|8205|8206|8207|8208|8209|8210|8211|8213|8214|8215|8217|8218|8219|8220|8221|8222|8223|8235|9001|9002|9003|9004|9005|9009|9010|9011|9012|9013)\"}[$__rate_interval])) or on() vector(0))\n  / sum(rate(tidb_server_query_total{kubernetes_namespace=\"tidb$cluster_id\", type=~\"Query|StmtExecute\", result=\"OK\"}[$__rate_interval]))\n) >= 0 or on() vector(0))", "hide": false, "interval": "", "legendFormat": "SuccessRate", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "SQL Success Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": null, "fill": 10, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 7}, "hiddenSeries": false, "id": 5, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 0, "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": true, "targets": [{"exemplar": true, "expr": "sum by (type) (increase(tidb_server_execute_error_total{kubernetes_namespace=\"tidb$cluster_id\", type=~\"[^:]*:(8021|8023|8024|8026|8027|8028|8039|8041|8042|8043|8044|8045|8046|8049|8050|8051|8052|8053|8054|8056|8057|8058|8059|8062|8106|8114|8117|8118|8120|8122|8201|8202|8203|8204|8205|8206|8207|8208|8209|8210|8211|8213|8214|8215|8217|8218|8219|8220|8221|8222|8223|8235|9001|9002|9003|9004|9005|9009|9010|9011|9012|9013)\"}[$__rate_interval]))", "hide": false, "instant": false, "interval": "", "legendFormat": "{{type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "SQL Fail Numbers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fill": 0, "fillGradient": 10, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 7}, "hiddenSeries": false, "id": 7, "interval": "1m", "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "maxDataPoints": null, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (result) (rate(tidb_server_query_total{kubernetes_namespace=\"tidb$cluster_id\", type=~\"Query|StmtExecute\"}[$__rate_interval]))", "hide": false, "instant": false, "interval": "", "legendFormat": "{{result}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "SQL Query Per Second", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "short", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 9, "panels": [], "title": "SLI Alternatives", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "decimals": null, "description": "TiDB statement statistics", "editable": true, "error": false, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "hiddenSeries": false, "id": 17, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_executor_statement_total{kubernetes_namespace=\"tidb$cluster_id\", type=~\"Select|Insert|Update|Delete\"}[$__rate_interval])) by (type)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:99", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:100", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "", "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "hiddenSeries": false, "id": 21, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.80, sum(rate(tidb_server_handle_query_duration_seconds_bucket{kubernetes_namespace=\"tidb$cluster_id\", sql_type=~\"Select|Insert|Update|Delete\"}[$__rate_interval])) by (le,sql_type))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{sql_type}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "80% Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:670", "format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"$$hashKey": "object:671", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 22}, "hiddenSeries": false, "id": 13, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "maxDataPoints": null, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_session_transaction_duration_seconds_count{kubernetes_namespace=\"tidb$cluster_id\"}[$__rate_interval])) by (type, txn_mode)", "interval": "", "intervalFactor": 1, "legendFormat": "{{type}}-{{txn_mode}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 22}, "hiddenSeries": false, "id": 14, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "maxDataPoints": null, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.80, sum(rate(tidb_session_transaction_duration_seconds_bucket{kubernetes_namespace=\"tidb$cluster_id\"}[1m])) by (le, txn_mode))", "interval": "", "intervalFactor": 1, "legendFormat": "{{txn_mode}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction 80% Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "description": "", "fill": 0, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 22}, "hiddenSeries": false, "id": 15, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "maxDataPoints": null, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.80, sum(rate(tidb_session_transaction_statement_num_bucket{kubernetes_namespace=\"tidb$cluster_id\"}[1m])) by (le, txn_mode))", "interval": "", "intervalFactor": 1, "legendFormat": "{{txn_mode}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Transaction 80% Statements", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:78", "format": "short", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"$$hashKey": "object:79", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 30}, "hiddenSeries": false, "id": 24, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_count{kubernetes_namespace=\"tidb$cluster_id\"}[$__rate_interval])) by (le, type)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KV Transaction OPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:906", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:907", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "tidb-cluster", "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 30}, "hiddenSeries": false, "id": 23, "interval": "1m", "legend": {"alignAsTable": true, "avg": true, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "sideWidth": 250, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "8.0.0-beta3", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile(0.80, sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_bucket{kubernetes_namespace=\"tidb$cluster_id\"}[$__rate_interval])) by (le, type))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{type}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "80% KV Transaction Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:906", "format": "s", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"$$hashKey": "object:907", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 27, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "CC_A1", "value": "CC_A1"}, "datasource": null, "definition": "query_result(sum by (tenant, name)(dbaas_tenant_info) and on(tenant) sum by (tenant)(dbaas_tidb_cluster_info))", "description": "The name of the organization (tenant)", "error": null, "hide": 0, "includeAll": false, "label": "Org", "multi": false, "name": "tenant_name", "options": [], "query": {"query": "query_result(sum by (tenant, name)(dbaas_tenant_info) and on(tenant) sum by (tenant)(dbaas_tidb_cluster_info))", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "/\"([^\"]+)\"/", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "1311265642040528896", "value": "1311265642040528896"}, "datasource": null, "definition": "label_values(dbaas_tenant_info{name=\"$tenant_name\"}, tenant)", "description": null, "error": null, "hide": 1, "includeAll": false, "label": null, "multi": false, "name": "tenant_id", "options": [], "query": {"query": "label_values(dbaas_tenant_info{name=\"$tenant_name\"}, tenant)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "tidbcluster-a1", "value": "tidbcluster-a1"}, "datasource": null, "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "description": "The name of the cluster", "error": null, "hide": 0, "includeAll": false, "label": "Cluster", "multi": false, "name": "cluster_name", "options": [], "query": {"query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\"},name)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "1311285575352848384", "value": "1311285575352848384"}, "datasource": null, "definition": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "description": null, "error": null, "hide": 1, "includeAll": false, "label": null, "multi": false, "name": "cluster_id", "options": [], "query": {"query": "label_values(dbaas_tidb_cluster_info{tenant=\"$tenant_id\",name=\"$cluster_name\"},cluster_id)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "SLI Per Cluster", "uid": "sli-cluster", "version": 1}