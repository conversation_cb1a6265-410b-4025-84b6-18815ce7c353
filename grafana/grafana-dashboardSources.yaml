apiVersion: v1
data:
  dashboards.yaml: |-
    {
        "apiVersion":1,
        "providers":[
           {
              "folder":"Kubernetes",
              "name":"0",
              "options":{
                 "path":"/grafana-dashboard-definitions/0"
              },
              "orgId":1,
              "type":"file"
           },
           {
              "folder":"DBaaS",
              "name":"1",
              "options":{
                 "path":"/grafana-dashboard-definitions/1"
              },
              "orgId":1,
              "type":"file"
           },
           {
              "folder":"TiDB",
              "name":"2",
              "options":{
                 "path":"/grafana-dashboard-definitions/2"
              },
              "orgId":1,
              "type":"file"
            },
            {
              "folder":"Capacity Planning",
              "name":"3",
              "options":{
                 "path":"/grafana-dashboard-definitions/3"
              },
              "orgId":1,
              "type":"file"
            },
            {
               "folder":"Ingress",
               "name":"4",
               "options":{
                  "path":"/grafana-dashboard-definitions/4"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"Alerts",
               "name":"5",
               "options":{
                  "path":"/grafana-dashboard-definitions/5"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"Logs",
               "name":"6",
               "options":{
                  "path":"/grafana-dashboard-definitions/6"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"Loki",
               "name":"7",
               "options":{
                  "path":"/grafana-dashboard-definitions/7"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"Thanos",
               "name":"8",
               "options":{
                  "path":"/grafana-dashboard-definitions/8"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"Fluentbit",
               "name":"9",
               "options":{
                  "path":"/grafana-dashboard-definitions/9"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"Cert Manager",
               "name":"10",
               "options":{
                  "path":"/grafana-dashboard-definitions/10"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"One Screen",
               "name":"11",
               "options":{
                  "path":"/grafana-dashboard-definitions/11"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"SLI",
               "name":"12",
               "options":{
                  "path":"/grafana-dashboard-definitions/12"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"User Alert",
               "name":"13",
               "options":{
                  "path":"/grafana-dashboard-definitions/13"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"Service Level Management",
               "name":"14",
               "options":{
                  "path":"/grafana-dashboard-definitions/14"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"GitOps",
               "name":"15",
               "options":{
                  "path":"/grafana-dashboard-definitions/15"
               },
               "orgId":1,
               "type":"file"
            },
            {
               "folder":"Ecosystem",
               "name":"16",
               "options":{
                  "path":"/grafana-dashboard-definitions/16"
               },
               "orgId":1,
               "type":"file"
            }
        ]
    }
kind: ConfigMap
metadata:
  name: grafana-dashboards-v2
  namespace: monitoring
