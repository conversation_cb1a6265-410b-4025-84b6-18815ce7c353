# Operation

*Note*: The following only applies to production environment


## Setup bastion machine

[Login to the bastion](https://pingcap.feishu.cn/wiki/wikcnz02ck11XVWNSFMPoETLh2e)

``` bash
 # Configure aws with credentials
aws configure

# List eks clusters in specified regions
aws eks list-clusters --region us-west-2
# Retrieve eks cluster kubeconfig
aws eks --region us-west-2 update-kubeconfig --name prod-base-us-west-2

# Configure gcloud
gcloud auth login

# List gke clusters in prod project
gcloud container clusters list --project dbaas-prod
# Retrieve gke kubeconfig
gcloud container clusters get-credentials prod-seed-us-west1-d5ae263 --region us-west1 --project dbaas-prod
```

## Accessing Seed/Shoot cluster

``` bash
kubectl config use-context arn:aws:eks:us-west-2:************:cluster/prod-base-us-west-2

# Run a debug pod, the pod will automatically retrieve shoot and seed kubeconfig
kubectl run -n prod ${my_debug_pod} --restart=Never -it --rm --image overriden --overrides '
{
  "spec": {
    "serviceAccountName": "central",
    "containers": [
      {
        "name": "cockpit",
        "stdin": true,
        "tty": true,
        "imagePullPolicy": "Always",
        "image": "gcr.io/pingcap-public/cockpit:latest"
      }
    ]
  }
}' --attach

# switch to the kubeconfig context
k ctx
```

Besides, you should configure the aws credentials via `aws configure`.

## Alert rules runbook

The alert rules runbook is managed via markdown style text under [docs folder](./docs), it will be synced to the [wiki repo](https://github.com/tidbcloud/runbooks/wiki) via github workflow.

## Grafana dashboards & Alert rules

For adding new alert rules, please first check out the how-to docs: https://pingcap.feishu.cn/wiki/wikcnPd8Xw2uyvCf0LwvU8wu24d

For Dev: you can exec `make all` format and lint all `grafana json`, `prometheus rules` and `pulumi code`.

> **Note**: the grafana dashboards and alert rules are applied to dev/stg/prod at the same time. So please make sure the alert rules are tested manually before merge to master

## Develop Pulumi Code

Setup local development environment

```bash
unset KUBECONFIG
export TEST_MODE=true
export PULUMI_CONFIG_PASSPHRASE=wearepingcap
pulumi login --local
pulumi stack select --create --secrets-provider=passphrase runbooks-dev
pulumi up
```

`make format`: will format all pulumi typescript code

`make check-pulumi`: will lint all pulumi typescript code

## Add New Alert Rules

`make base`: generate all yaml(PrometheusRules) file in base cluster

`make seed`: generate all yaml(PrometheusRules) file in seed cluster

`make shoot`: generate all yaml(ConfigMap) file in shoot cluster

All alert rules are in the `rules` directory. You can run `make check-rules` for linting.

**Note**: When adding new alert rules, please make sure adding a corresponding runbook in the [Wiki](https://github.com/tidbcloud/runbooks/wiki).

Best practices:
- make sure every alert rule has a `component` label to indicate which physical component it belongs to
- [write useful prometheus rules (Chinese)](https://docs.google.com/document/d/***************************-8Qll0lLPCQXnYTMQ/edit)
- [Awesome Prometheus alerts](https://awesome-prometheus-alerts.grep.to/)
- [rate-then-sum-never-sum-then-rate](https://www.robustperception.io/rate-then-sum-never-sum-then-rate/)

## Add New Grafana Dashboard

All grafana json are in the `grafana` directory. You should run `make format` to format it before sending PR.

Generally, you just need to put JSON files to a exist directory. If you want to create a new directory for your dashboards, refer to [this PR](https://github.com/tidbcloud/runbooks/pull/340/files).

After CD finish, it will take effect.
