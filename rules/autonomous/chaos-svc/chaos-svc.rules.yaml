groups:
  - name: chaos-svc.rules
    rules:
      - alert: ChaosSvcNoAvailableReplicas
        expr: kube_deployment_status_replicas_available{deployment="chaos-svc-server"}==0
        for: 5m
        labels:
          component: chaos-svc
          service: chaos-svc
          severity: critical
        annotations:
          description: There are no running chaos-svc pods.
      - alert: ChaosSvcOOMKilled
        expr: increase(kube_pod_container_status_restarts_total{container="chaos-svc-server"}[5m]) >= 1 AND ignoring(reason) kube_pod_container_status_last_terminated_reason{reason="OOMKilled",container="chaos-svc-server"} == 1
        for: 3m
        labels:
          component: chaos-svc
          service: chaos-svc
          severity: critical
        annotations:
          description: "ChaosSvc in {{ $labels.cluster }} has been OOMKilled {{ $value }} times in the last 5 minutes.\n LABELS = {{ $labels }}"
