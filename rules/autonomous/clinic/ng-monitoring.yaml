groups:
- name: ng-monitoring.rules
  rules:
  - alert: KubePersistentVolumeFillingUp
    annotations:
      message: The PersistentVolume claimed by {{ $labels.persistentvolumeclaim
        }} in Namespace {{ $labels.namespace }} is only {{ $value | humanizePercentage
        }} free.
      runbook_url: https://github.com/tidbcloud/runbooks/wiki/KubePersistentVolumeFillingUp
    expr: |
      kubelet_volume_stats_available_bytes{persistentvolumeclaim=~".*ng-monitoring.*"}
        /
      kubelet_volume_stats_capacity_bytes{persistentvolumeclaim=~".*ng-monitoring.*"}
        < 0.15
    for: 1m
    labels:
      component: ngmonitoring
      severity: critical