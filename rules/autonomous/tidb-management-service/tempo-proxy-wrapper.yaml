groups:
- name: mgmt.tempo-proxy-wrapper.allow-list
  rules:
    - alert: AllowListReloadRemoteFailed
      expr:  increase(allow_list_reload_failed_total{namespace="tidb-management-service"}[5m]) >= 1
      for: 5m
      labels:
        component: tidb-management-service
        service: allow-list
        severity: critical
      annotations:
        description: "Allow list reload remote failed in {{ $labels.container }} occurred {{ $value }} times in the last 5 minutes.\n LABELS = {{ $labels }}"

    - alert: AllowListWatchRemoteFailed
      expr:  increase(allow_list_watch_failed_total{namespace="tidb-management-service"}[5m]) >= 1
      for: 5m
      labels:
        component: tidb-management-service
        service: allow-list
        severity: critical
      annotations:
        description: "Allow list watch remote failed in {{ $labels.container }} occurred {{ $value }} times in the last 5 minutes.\n LABELS = {{ $labels }}"

- name: mgmt.tempo-proxy-wrapper.http
  rules:
    - alert: WrapperPortalHttp5xx
      expr: sum(rate(tidb_management_service_wrapper_http_responses_5xx{namespace="tidb-management-service", container="tempo-wrapper"}[1m])) by (code) > 0.2
      for: 1m
      annotations:
        description: Wrapper Portal has 5xx greater than 0.2 QPS for 1 minutes.
        expr: sum(rate(tidb_management_service_wrapper_http_responses_5xx{namespace="tidb-management-service", container="tempo-wrapper"}[1m])) by (code) > 0.2
        for: 1m
      labels:
        severity: critical
        component: tidb-management-service
        service: tempo-wrapper
