groups:
  - name: mgmt.workflow
    rules:
      - alert: WorkflowRunningTooLong
        annotations:
          message: Workflow cluster id {{ $labels.biz_id }} of {{ $labels.job }}/{{ $labels.workflow_dag }} on region {{ $labels.target_region }} is running too long.
        expr: |
          avg by (tenant_id,project_id,target_provider,target_region,biz_id,workflow_dag,job,dag_ins_id) (workflow_info{namespace=~"tidb-management-service",workflow_dag!~"dynamic_dag__(global_scale|console_api_scale).*"}) > 2400
        for: 1m
        labels:
          severity: critical
          component: tidb-management-service
      - alert: ScalingWorkflowRunningTooLong
        annotations:
          message: Workflow cluster id {{ $labels.biz_id }} of {{ $labels.job }}/{{ $labels.workflow_dag }} on region {{ $labels.target_region }} is running too long.
        expr: |
          avg by (tenant_id,project_id,target_provider,target_region,biz_id,workflow_dag,job,dag_ins_id) (workflow_info{namespace=~"tidb-management-service",workflow_dag=~"dynamic_dag__(global_scale|console_api_scale).*"}) > 10800
        for: 1m
        labels:
          severity: major
          component: tidb-management-service
      - alert: ScalingWorkflowRunningTooLong
        annotations:
          message: Workflow cluster id {{ $labels.biz_id }} of {{ $labels.job }}/{{ $labels.workflow_dag }} on region {{ $labels.target_region }} is running too long.
        expr: |
          avg by (tenant_id,project_id,target_provider,target_region,biz_id,workflow_dag,job,dag_ins_id) (workflow_info{namespace=~"tidb-management-service",workflow_dag=~"dynamic_dag__(global_scale|console_api_scale).*"}) > 86400
        for: 1m
        labels:
          severity: major
          component: tidb-management-service
      - alert: RetryWorkflowInstanceFailed
        annotations:
          message: Workflow cluster id {{ $labels.biz_id }} of {{ $labels.job }}/{{ $labels.workflow_dag }} on region {{ $labels.target_region }} failed to retry.
        expr: |
          increase(workflow_watchdog_retried_dag_instance_total{namespace=~"tidb-management-service",retry_command_error="true"}[1m]) > 10
        for: 1m
        labels:
          severity: critical
          component: tidb-management-service
