groups:
- name: mgmt.tidb-mgmt-service
  rules:
    - alert: SyncClusterInfoToJsmFailed
      expr: count by(cluster_id, cluster_name, event_type) (increase(tidb_management_service_consumer_sync_to_jsm_failed_total[1m:])) > 0
      annotations:
        description: |-
          cluster {{ $labels.cluster_id }}/{{ $labels.cluster_name }} {{ $labels.event_type }} sync to jsm failed
          VALUE = {{ $value }}
          LABELS = {{ $labels }}
        summary: cluster {{ $labels.cluster_id }}/{{ $labels.cluster_name }} {{ $labels.event_type }} sync to jsm failed
        expr: count by(cluster_id, cluster_name, event_type) (increase(tidb_management_service_consumer_sync_to_jsm_failed_total[1m:])) > 0
      labels:
        severity: critical
        component: tidb-management-service
        service: tidb-mgmt-service
    - alert: AutoResumePausedCluster
      expr: count by(cluster_id, cluster_name) (increase(tidb_management_service_auto_resume_cluster_count_total[1m:])) > 0
      annotations:
        description: |-
          cluster {{ $labels.cluster_id }}/{{ $labels.cluster_name }} is auto-resumed by TiDB Cloud
          project_id = {{ $labels.project_id }}
          org_id = {{ $labels.org_id }}
          VALUE = {{ $value }}
          LABELS = {{ $labels }}
        summary: cluster {{ $labels.cluster_id }}/{{ $labels.cluster_name }} is auto-resumed by TiDB Cloud
        expr: count by(cluster_id, cluster_name) (increase(tidb_management_service_auto_resume_cluster_count_total[1m:])) > 0
      labels:
        severity: major
        component: tidb-management-service
        service: tidb-mgmt-service

