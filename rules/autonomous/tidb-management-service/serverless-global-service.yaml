groups:
  - name: mgmt.serverless-global-service
    rules:
      - alert: RequestRegionalServerFailed
        expr: |
          sum(rate(global_serverless_control_regional_request_failed_total[1m])) by (path) > 1
        for: 1m
        labels:
          severity: critical
          component: tidb-management-service
        annotations:
          description: request regional server failed.
          diagnosis: "click here to see dashboard: https://clinic.pingcap.com/grafana/d/m25IUfjVz/serverless-global-service?orgId=1"

      - alert: TooManyRegionalRequests
        expr: |
          sum(rate(global_serverless_control_regional_request_total[1m])) by (path) 
            > 180
        for: 1m
        labels:
          severity: critical
          component: tidb-management-service
        annotations:
          diagnosis: "click here to see dashboard: https://clinic.pingcap.com/grafana/d/m25IUfjVz/serverless-global-service?orgId=1"

      - alert: TooSlowRegionalResponse
        expr: |
          histogram_quantile(0.99, sum(rate(global_serverless_control_regional_request_time_seconds_bucket{namespace=~"tidb-management-service"}[1m])) by (path, method, le))
            > 3
        for: 5m
        labels:
          severity: critical
          component: tidb-management-service
        annotations:
          diagnosis: "click here to see dashboard: https://clinic.pingcap.com/grafana/d/m25IUfjVz/serverless-global-service?orgId=1"

      - alert: TooManyClusterIDInRequest
        expr: |
          sum(rate(global_serverless_control_regional_request_too_many_clusterid[1m]))
          > 0
        for: 1s
        labels:
          severity: critical
          component: tidb-management-service
        annotations:
          diagnosis: "click here to see dashboard: https://clinic.pingcap.com/grafana/d/m25IUfjVz/serverless-global-service?orgId=1"

