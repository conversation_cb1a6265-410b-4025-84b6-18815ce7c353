groups:
  - name: mgmt.grpc
    rules:
      - alert: gRPCServerErrorRate > 5%
        expr: |
          (
            sum by (grpc_service, grpc_method, job) (
              rate(
                (
                  grpc_server_handled_total{grpc_code!="OK",namespace=~"tidb-management-service"}
                  unless 
                  grpc_server_handled_total{grpc_code="Code(49900004)",grpc_method="GetCluster",grpc_service="tidb_cloud_open_api.serverless.v1beta1.ServerlessService",job="tidb-mgmt-service"}
                  unless
                  grpc_server_handled_total{grpc_service="global.dedicated.v1.DataPlatformCompatService",job="dedicated-global-service"}
                )[3m:]
              )
            )
            /
            sum by (grpc_service, grpc_method, job) (
              rate(
                grpc_server_handled_total{namespace=~"tidb-management-service"}[3m]
              )
            )
          ) * 100
          > 5
        for: 3m
        labels:
          severity: critical
          component: tidb-management-service
        annotations:
          description: gRPC server failure rate is {{ printf "%.2f" $value }}%, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}, component={{ $labels.job }}.

#      - alert: gRPCServerQPSYoYByDay > 300%
#        expr: |
#          sum by (grpc_service, grpc_method, job) (
#            rate(grpc_server_handled_total{namespace=~"tidb-management-service"}[3m])
#          )
#          /
#          sum by (grpc_service, grpc_method, job) (
#            rate(grpc_server_handled_total{namespace=~"tidb-management-service"}[3m] offset 1d)
#          ) > 3
#        for: 5m
#        labels:
#          severity: major
#          component: tidb-management-service
#        annotations:
#          description: gRPC server QPS YoY by day is {{ printf "%.2f" $value }} times higher, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}, component={{ $labels.job }}.

#      - alert: gRPCServerP95DurationYoYByDay > 300%
#        expr: |
#          histogram_quantile(
#            0.95,
#            sum by (le, grpc_service, grpc_method, job) (
#              rate(grpc_server_handling_seconds_bucket{namespace=~"tidb-management-service"}[5m])
#            )
#          )
#          /
#          histogram_quantile(
#            0.95,
#            sum by (le, grpc_service, grpc_method, job) (
#              rate(grpc_server_handling_seconds_bucket{namespace=~"tidb-management-service"}[5m] offset 1d)
#            )
#          )
#          > 3
#        for: 15m
#        labels:
#          severity: major
#          component: tidb-management-service
#        annotations:
#          description: gRPC server response P95 duration YoY by day is {{ printf "%.2f" $value }} times slower, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}, component={{ $labels.job }}.
