groups:
  - name: mgmt.gorm
    rules:
      - alert: GormWaitForConnectionDurationIncrease
        annotations:
          message: Gorm db stats wait duration is not 0 for {{ $labels.job }}.
        expr: |
          sum(rate(gorm_dbstats_wait_duration{namespace=~"tidb-management-service"}[2m])) by (job)
          > 0
        for: 5m
        labels:
          severity: critical
          component: tidb-management-service
