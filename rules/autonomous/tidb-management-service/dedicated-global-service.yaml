groups:
  - name: mgmt.dedicated-global-service
    rules:
      - alert: ClusterMeterLagTooLarge
        annotations:
          message: the lag of cluster metering is {{ $value | humanizeDuration }} and more than 15 minutes. cluster_id is {{ $labels.cluster_id }}
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/Cittw3iTTiqJ8vksvHwcpOafnXf to get more info
        expr: |
          global_dedicated_service_metering_meter_lag_seconds{meter_type="cluster"} > 10800
        for: 0m
        labels:
          severity: major
          component: tidb-management-service
          service: dedicated-global-service

      - alert: ClusterMeterLagTooLarge
        annotations:
          message: the lag of cluster metering is {{ $value | humanizeDuration }} and more than 30 minutes. cluster_id is {{ $labels.cluster_id }}
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/Cittw3iTTiqJ8vksvHwcpOafnXf to get more info
        expr: |
          global_dedicated_service_metering_meter_lag_seconds{meter_type="cluster"} > 10800
        for: 0m
        labels:
          severity: critical
          component: tidb-management-service
          service: dedicated-global-service

      - alert: MeteringReportLagTooLarge
        annotations:
          message: the lag of metering report is {{ $value | humanizeDuration }} and more than 1 hour.
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/Cittw3iTTiqJ8vksvHwcpOafnXf to get more info
        expr: |
          global_dedicated_service_metering_report_lag_seconds{} > 3600
        for: 0m
        labels:
          severity: major
          component: tidb-management-service
          service: dedicated-global-service

      - alert: MeteringReportLagTooLarge
        annotations:
          message: the lag of metering report is {{ $value | humanizeDuration }} and more than 3 hours.
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/Cittw3iTTiqJ8vksvHwcpOafnXf to get more info
        expr: |
          global_dedicated_service_metering_report_lag_seconds{} > 10800
        for: 0m
        labels:
          severity: critical
          component: tidb-management-service
          service: dedicated-global-service

      - alert: ClusterMeterFailureRateTooHigh
        annotations:
          message: the failure rate of cluster metering is {{ $value | humanize }}. cluster_id is {{ $labels.cluster_id }}
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/Cittw3iTTiqJ8vksvHwcpOafnXf to get more info
        expr: |
          (100-(sum(rate(global_dedicated_service_metering_meter_total{cluster_id!="0", meter_type="cluster", success="true"}[10m])) BY (cluster_id) 
            / sum(rate(global_dedicated_service_metering_meter_total{cluster_id!="0", meter_type="cluster"}[10m])) BY (cluster_id) 
          ) * 100) > 40
        for: 5m
        labels:
          severity: major
          component: tidb-management-service
          service: dedicated-global-service

      - alert: ClusterMeterDurationP95TooLong
        annotations:
          message: the full cluster metering cost time is ${{ $value | humanizeDuration }} and more than 1 minute
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/Cittw3iTTiqJ8vksvHwcpOafnXf to get more info
        expr: |
          histogram_quantile(0.95,
            sum(rate(global_dedicated_service_metering_meter_duration_seconds_bucket{
              cluster_id="0", meter_type="cluster"
          }[5m])) by (le)) > 60
        for: 3m
        labels:
          severity: major
          component: tidb-management-service
          service: dedicated-global-service
