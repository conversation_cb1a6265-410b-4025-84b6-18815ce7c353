groups:
  - name: mgmt.eventbus
    rules:
      - alert: PublishingEventFailed
        expr: |
          (
            sum(rate(publish_time_seconds_count{namespace="tidb-management-service", success="false"}[3m])) by (event_type, job)
            /
            sum(rate(publish_time_seconds_count{namespace="tidb-management-service"}[3m])) by (event_type, job)
          )
          * 100          
          > 5
        for: 3m
        labels:
          severity: critical
          component: tidb-management-service
        annotations:
          message: Eventbus publishing failure rate is {{ printf "%.2f" $value }}%, event_type={{ $labels.event_type }}, component={{ $labels.job }}.

      - alert: ConsumingEventFailed
        expr: |
          (
            sum(rate(events_received_latency_seconds_count{namespace="tidb-management-service", success="false"}[3m])) by (event_type, group, job)
            /
            sum(rate(consume_time_seconds_count{namespace="tidb-management-service"}[3m])) by (event_type, group, job)
          )
          * 100          
          > 5
        for: 3m
        labels:
          severity: critical
          component: tidb-management-service
        annotations:
          message: Eventbus consuming failure rate is {{ printf "%.2f" $value }}%, event_type={{ $labels.event_type }}, group={{ $labels.group }}, component={{ $labels.job }}.
