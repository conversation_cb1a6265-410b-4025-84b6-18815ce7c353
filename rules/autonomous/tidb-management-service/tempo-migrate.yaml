#groups:
#  - name: mgmt.tempo-migrate
#    rules:
#      - alert: DiffTierWorkerStop
#        expr: sum(rate(tempo_migrate_worker_worker_diff_tier_failed_total{namespace="tidb-management-service"}[1m])) > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are no running tier diff worker.
#      - alert: DiffScaleWorkerStop
#        expr: sum(rate(tempo_migrate_worker_worker_scale_api_failed_total{namespace="tidb-management-service"}[1m])) > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are no running scale diff worker.
#      - alert: DiffListNodeProfileWorkerStop
#        expr: sum(rate(tempo_migrate_worker_worker_list_node_profile_failed_total{namespace="tidb-management-service"}[1m])) > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are no running list node profile worker.
#      - alert: ListNodeProfileApiDiff
#        expr: tempo_migrate_diff_diff_list_node_profiles4_tenant_diff_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are some different in list node profile api.
#      - alert: ScaleApiDiff
#        expr: tempo_migrate_diff_diff_scale_cluster_diff_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are some different in scale api.
#      - alert: TierTableDiff
#        expr: tempo_migrate_diff_diff_tiers_table_diff_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are some different in tier table.
#      - alert: TierUnitTableDiff
#        expr: tempo_migrate_diff_diff_tier_unit_table_diff_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are some different in tier unit table.
#      - alert: StorageTemplateTableDiff
#        expr: tempo_migrate_diff_diff_storage_template_table_diff_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are some different in storage template table.
#      - alert: InstanceTypeTableDiff
#        expr: tempo_migrate_diff_diff_instance_types_table_diff_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are some different in instance type table.
#      - alert: OnePD
#        expr: tempo_migrate_diff_diff_one_pd_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are only 1 pd.
#      - alert: PdAttachTiKVDiff
#        expr: tempo_migrate_diff_diff_pd_attach_tikv_diff_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are some different in tier unit conditions table.
#      - alert: Tier2TierUnitiff
#        expr: tempo_migrate_diff_diff_tier_2_tierunit_diff_total{namespace="tidb-management-service"} > 0
#        for: 0s
#        labels:
#          component: tidb-management-service
#          service: tempo-migrate-service
#          severity: critical
#        annotations:
#          description: There are some different in tier unit conditions table.
