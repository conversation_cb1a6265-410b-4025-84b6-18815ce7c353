groups:
- name: api.rules
  rules:
  - alert: CloudInfraCallO11yApiFailed
    annotations:
      message: "The error rate of requesting o11y infra API is over 50%, VALUE = {{ $value }}"
    expr: |
      sum(rate(infra_o11y_api_request_total{message!="success"}[5m]) / rate(infra_o11y_api_request_total[5m])) * 100 > 50
    for: 5m
    labels:
      component: o11y
      severity: critical
      tier: eks-provider-dedicated
  - alert: CloudInfraDeployO11yComponentFailed
    annotations:
      message: "{{ $labels.name }} in {{ $labels.region }}/{{ $labels.cluster }}/{{ $labels.namespace }} has been in a non-ready state for longer than 15 minutes."
    expr: |
      infra_o11y_component_health_status == -1
    for: 15m
    labels:
      component: o11y
      severity: critical
      tier: eks-provider-dedicated
