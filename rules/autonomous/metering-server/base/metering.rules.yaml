groups:
  - name: metering-server.rules
    rules:
      - alert: MeteringServerNoAvailableReplicas
        expr: kube_statefulset_status_replicas_available{statefulset="dedicated-metering-server",namespace="metering"}==0
        for: 5m
        labels:
          component: metering-server
          severity: critical
        annotations:
          description: There are no running metering-server pods.
      - alert: MeteringServerOOMKilled
        expr: increase(kube_pod_container_status_restarts_total{container="instance-metering-server"}[5m]) >= 1 AND ignoring(reason) kube_pod_container_status_last_terminated_reason{reason="OOMKilled",container="instance-metering-server"} == 1
        for: 3m
        labels:
          severity: critical
          component: metering-server
        annotations:
          description: "Metering Server in {{ $labels.cluster }} has been OOMKilled {{ $value }} times in the last 5 minutes.\n LABELS = {{ $labels }}"
      - alert: MeteringServerTooManyAPIRequestsFailed
        expr: increase(metering_common_meter_request_api_total{api_result="failed"}[5m]) >= 5 
        for: 5m
        labels:
          severity: major
          component: metering-server
        annotations:
          description: "Metering Server in {{ $labels.cluster }} has too many api reuqest failed in the last 5 minutes.\n LABELS = {{ $labels }}"
      - alert: MeteringServerMeteringFailed
        expr: increase(metering_instance_meter_failed_total{}[5m]) >= 5 
        for: 5m
        labels:
          severity: major
          component: metering-server
        annotations:
          description: "Metering Server in {{ $labels.cluster }} has failed in the last 5 minutes.\n LABELS = {{ $labels }}"
