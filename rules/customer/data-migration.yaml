groups:
- name: data-migration
  rules:
  - alert: DMMetErrorOnExport
    annotations:
      message: |
        Data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName meet some error during data export. Please check the error and see <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
      description: Data migration job met error during data export
    expr: |
      changes(dm_mydumper_exit_with_error_count{resumable_err="false"}[1m]) > 0 or on(project_id, job_id, source_id) increase(dm_mydumper_exit_with_error_count{resumable_err="true"}[2m]) > 3
    labels:
      visibility: external
      severity: major
      component: data-migration
      ruleID: "5"
      status : active
      description: Data migration job met error during data export
      message: |
        Data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName meet some error during data export. Please check the error and see <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
  - alert: DMMetErrorOnImport
    annotations:
      message: |
        Data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName meet some error during data import. Please check the error and see <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
      description: Data migration job met error during data import
    expr: |
      changes(dm_loader_exit_with_error_count{resumable_err="false"}[1m]) > 0 or on(project_id, job_id, source_id) increase(dm_loader_exit_with_error_count{resumable_err="true"}[2m]) > 3
    labels:
      visibility: external
      severity: major
      component: data-migration
      ruleID: "10"
      status : active
      description: Data migration job met error during data import
      message: |
        Data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName meet some error during data import. Please check the error and see <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
  - alert: DMMetErrorOnSync
    annotations:
      message: |
        Data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName meet some error during incremental data migration. Please check the error and see <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
      description: Data migration job met error during incremental data migration
    expr: |
      rate(dm_syncer_exit_with_error_count{resumable_err="false"}[1m]) > 0 or on(project_id, job_id, source_id) increase(dm_syncer_exit_with_error_count{resumable_err="true"}[2m]) > 3
    labels:
      visibility: external
      severity: major
      component: data-migration
      ruleID: "15"
      status : active
      description: Data migration job met error during incremental data migration
      message: |
        Data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName meet some error during incremental data migration. Please check the error and see <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
  - alert: DMSyncPauseTooLong
    annotations:
      message: |
        Data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName has been paused for more than 6 hours during incremental migration, binlog in upstream database might be purged(depends on your database binlog purge strategy) and may cause incremental migration fail. See <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
      description: Data migration job has been paused for more than 6 hours during incremental migration
    expr: |
      dm_worker_task_state{} == 3
    for: 6h
    labels:
      visibility: external
      severity: major
      component: data-migration
      ruleID: "20"
      status : active
      message: |
        Data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName has been paused for more than 6 hours during incremental migration, binlog in upstream database might be purged(depends on your database binlog purge strategy) and may cause incremental migration fail. See <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
      description: Data migration job has been paused for more than 6 hours during incremental migration
  - alert: DMReplicationLagTooLarge
    annotations:
      message: |
        Replication lag is larger than {threshold} seconds and stilling increasing for more than {triggerInterval} minutes for data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName. See <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
      description: Replication lag is larger than {threshold} seconds and stilling increasing for more than {triggerInterval} minutes
    expr: |
      delta(dm_syncer_replication_lag_gauge[1m]) > 0 and on(project_id, job_id, source_id) dm_syncer_replication_lag_gauge{} > 600
    for: 20m
    labels:
      visibility: external
      severity: major
      component: data-migration
      ruleID: "25"
      status : active
      canEdit : all
      monitorTarget: "Replication lag "
      operator: " is larger than {threshold} seconds"
      message: |
        Replication lag is larger than {threshold} seconds and stilling increasing for more than {triggerInterval} minutes for data migration job <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/data-migration/{{ $labels.job_id }}">{{ $labels.job_id }}</a> of TiDB cluster TiDBClusterName. See <a href="https://docs.pingcap.com/tidbcloud/tidb-cloud-dm-precheck-and-troubleshooting#migration-errors-and-solutions">Troubleshoot DM</a> for help.
      description: Replication lag is larger than {threshold} seconds and stilling increasing for more than {triggerInterval} minutes