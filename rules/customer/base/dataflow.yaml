groups:
- name: dataflow-customer
  rules:
  # only expose this alarm to specific customer: CTW(1372813089190911285), Wetech(1372813089209061633)
  - alert: AutoBackupScheduledAbnormal
    annotations:
      description: Auto backup is not scheduled
      message: > 
        The auto backup of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.cluster_id }}"> cluster </a>  is not successfully scheduled.
        There are reasons TiDB Cloud can skip the auto backup, such as ongoing Import tasks, another backup is running or cluster status is not Available. 
        If not these cases, you can contact TiDB Cloud Support for further assistance.
    expr: |
      sum(increase(dataflow_worker_backup_scheduler_abnormal{type="auto_backup", tenant=~"1372813089190911285|1372813089209061633"}[90m])) by (cluster_id) > 0
    labels:
      severity: major
      component: br
      visibility: external
  - alert: TiDBDedicatedClusterBackupFailed
    annotations:
      description: Backup failed
      message: >
        The backup of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.cluster_id }}"> cluster </a> failed.
        It may be caused by unknown bugs, you can contact TiDB Cloud Support for further assistance.
    expr: |
      max(increase(dataflow_backup_execution_status_duration_seconds{status="failed", tenant=~"1372813089190911285|1372813089209061633"}[90m])) by (cluster_id, backup_id) > 0
    labels:
      severity: major
      component: br
      visibility: external
  - alert: TiDBClusterBackupStatusPending
    annotations:
      description: Backup pending
      message: |
        The backup of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.cluster_id }}"> cluster </a> is pending for more than 1 hour.
    expr: |
      (dataflow_backup_execution_status_duration_seconds{status="pending", tenant=~"1372813089209061633"} and on (cluster_id) dbaas_tidb_cluster_info{status="normal"}) > 60 * 60
    labels:
      severity: major
      component: br
      visibility: external
