groups:
- name: serverless
  rules:
  - alert: ServerlessRowStorageCapacity(75%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 75% of the row storage capacity this month."
      description: "Row storage capacity reached 75%"
    expr: |
      sum(avg(tikv_store_size_bytes{type="used"}) by(region_id)) / (5120 * 1024 * 1024) >= 0.75
    for: 5m
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "5"
      status: active
      description: "Row storage capacity reached 75%"
  - alert: ServerlessRowStorageCapacity(90%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 90% of the row storage capacity this month."
      description: "Row storage capacity reached 90%"
    expr: |
      sum(avg(tikv_store_size_bytes{type="used"}) by(region_id)) / (5120 * 1024 * 1024) >= 0.9
    for: 5m
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "10"
      status: active
      description: "Row storage capacity reached 90%"
  - alert: ServerlessRowStorageCapacity(100%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 100% of the row storage capacity this month."
      description: "Row storage capacity reached 100%"
    expr: |
      sum(avg(tikv_store_size_bytes{type="used"}) by(region_id)) / (5120 * 1024 * 1024) >= 1.0
    for: 5m
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "15"
      status: active
      description: "Row storage capacity reached 100%"
    
  - alert: ServerlessColumnStorageCapacity(75%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 75% of the column storage capacity this month."
      description: "Column storage capacity reached 75%"
    expr: |
      sum(tiflash_system_current_metric_StoreSizeUsed{type="used"}) / (5120 * 1024 * 1024) >= 0.75
    for: 5m
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "20"
      status: active
      description: "Column storage capacity reached 75%"
  - alert: ServerlessColumnStorageCapacity(90%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 90% of the column storage capacity this month."
      description: "Column storage capacity reached 90%"
    expr: |
      sum(tiflash_system_current_metric_StoreSizeUsed{type="used"}) / (5120 * 1024 * 1024) >= 0.9
    for: 5m
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "25"
      status: active
      description: "Column storage capacity reached 90%"
  - alert: ServerlessColumnStorageCapacity(100%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 100% of the column storage capacity this month."
      description: "Column storage capacity reached 100%"
    expr: |
      sum(tiflash_system_current_metric_StoreSizeUsed{type="used"}) / (5120 * 1024 * 1024) >= 1.0
    for: 5m
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "30"
      status: active
      description: "Column storage capacity reached 100%"
  
  - alert: ServerlessFreeRequestUnitsCapacity(75%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 75% of the free requests unit this month."
      description: "Free request units capacity reached 75%"
    expr: |
      (sum(increase(gateway_connection_flow_bytes_sum{direction="out",service=~"gateway-metrics|gateway-metrics-privatelink"}[1m])) / 512 +
       sum(increase(resource_manager_resource_unit_read_request_unit_sum[1m]))  +
       sum(increase(resource_manager_resource_unit_write_request_unit_sum[1m])) + 
       sum(increase(resource_manager_resource_unit_sql_layer_request_unit[1m])) +
       sum(increase(tiflash_compute_request_unit[1m]))
       ) / 50000000 >= 0.75
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "35"
      status: active
      description: "Free request units capacity reached 75%"
  - alert: ServerlessFreeRequestUnitsCapacity(90%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 90% of the free requests unit this month."
      description: "Free request units capacity reached 90%"
    expr: |
      (sum(increase(gateway_connection_flow_bytes_sum{direction="out",service=~"gateway-metrics|gateway-metrics-privatelink"}[1m])) / 512 +
       sum(increase(resource_manager_resource_unit_read_request_unit_sum[1m]))  +
       sum(increase(resource_manager_resource_unit_write_request_unit_sum[1m])) + 
       sum(increase(resource_manager_resource_unit_sql_layer_request_unit[1m])) +
       sum(increase(tiflash_compute_request_unit[1m]))
       ) / 50000000 >= 0.9
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "40"
      status: active
      description: "Free request units capacity reached 90%"
  - alert: ServerlessFreeRequestUnitsCapacity(100%)
    annotations:
      summary: "The serverless cluster TiDBClusterName in project TiDBProjectName has reached 100% of the free requests unit this month."
      description: "Free request units capacity reached 100%"
    expr: |
      (sum(increase(gateway_connection_flow_bytes_sum{direction="out",service=~"gateway-metrics|gateway-metrics-privatelink"}[1m])) / 512 +
       sum(increase(resource_manager_resource_unit_read_request_unit_sum[1m]))  +
       sum(increase(resource_manager_resource_unit_write_request_unit_sum[1m])) + 
       sum(increase(resource_manager_resource_unit_sql_layer_request_unit[1m])) +
       sum(increase(tiflash_compute_request_unit[1m]))
       ) / 50000000 >= 1.0
    labels:
      visibility: external
      severity: major
      component: serverless
      ruleID: "45"
      status: active
      description: "Free request units capacity reached 100%"
