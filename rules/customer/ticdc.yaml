groups:
- name: ticdc
  rules:
  # comment for now as it's using control-plane metrics
  # - alert: ChangefeedCreateFailed
  #   annotations:
  #     message: |
  #       The Changefeed {{ $labels.changefeed_id }} of cluster( {{ $labels.cluster_id }}, {{ $labels.cluster_name }}) create failed, {{ $labels.message }}, You can read error message to adjust changefeed create parameters, You can fire a ticket if needed.
  #     description: Changefeed was failed to create
  #   expr: |
  #     increase(dataflow_ticdc_changefeed_create_result{result="failed"}[5m])>0
  #   labels:
  #     visibility: external
  #     severity: major
  #     component: ticdc
  #     ruleID: "5"
  #     status : active
  #     description: Changefeed was failed to create
  - alert: TiCDCCheckpointHighDelay
    expr: ticdc_owner_checkpoint_ts_lag{instance=~"^db.*"} > 600
    for: 1m
    labels:
      visibility: external
      severity: major
      component: ticdc
      ruleID: "10"
      status : active
      canEdit : all
      monitorTarget: "Changefeed latency"
      operator: "is larger than {threshold} seconds"
      description: Changefeed latency is more than {threshold} seconds
      message: |
        The latency of Changefeed {{ $labels.changefeed }} of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/changefeed"> cluster TiDBClusterName </a> in project TiDBProjectName is more than {threshold} seconds.
        Check the changefeed status in the Changefeed Page and Changefeed Detail Page of the TiDB Cloud console, where you can find some error messages to help diagnose this issue.
        Possible reasons that can trigger this alert include: 
        1. The overall traffic in the upstream has increased, causing the existing changefeed specification to be insufficient to handle it. 
           If the traffic increase is temporary, the changefeed latency will automatically recover after the traffic returns to normal. 
           If the traffic increase is continuous, you need to scale up the changefeed.
        2. The downstream or network is abnormal. In this case, resolve this abnormality first.
        3. Tables lack indexes if the downstream is RDS, which might cause low write performance and high latency. In this case, you need to add the necessary indexes to the upstream or downstream.
        If the problem cannot be fixed from your side, you can contact TiDB Cloud Support for further assistance.
    annotations:
      message: |
        The latency of Changefeed {{ $labels.changefeed }} of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/changefeed"> cluster TiDBClusterName </a> in project TiDBProjectName is more than {threshold} seconds.
        Check the changefeed status in the Changefeed Page and Changefeed Detail Page of the TiDB Cloud console, where you can find some error messages to help diagnose this issue.
        Possible reasons that can trigger this alert include: 
        1. The overall traffic in the upstream has increased, causing the existing changefeed specification to be insufficient to handle it. 
           If the traffic increase is temporary, the changefeed latency will automatically recover after the traffic returns to normal. 
           If the traffic increase is continuous, you need to scale up the changefeed.
        2. The downstream or network is abnormal. In this case, resolve this abnormality first.
        3. Tables lack indexes if the downstream is RDS, which might cause low write performance and high latency. In this case, you need to add the necessary indexes to the upstream or downstream.
        If the problem cannot be fixed from your side, you can contact TiDB Cloud Support for further assistance.
      description: Changefeed latency is more than {threshold} seconds
  - alert: ChangefeedIsFailed
    expr: ticdc_owner_status{instance=~"^db.*"} == 2
    labels:
      visibility: external
      severity: critical
      component: ticdc
      ruleID: "15"
      status: active
      canEdit: status
      monitorTarget: "Changefeed status"
      operator: "is failed"
      description: Changefeed status is failed
      message: >
        The status of Changefeed {{ $labels.changefeed }} of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/changefeed"> cluster TiDBClusterName </a> in project TiDBProjectName is failed.
        Check the changefeed status in the Changefeed Page and Changefeed Detail Page of the TiDB Cloud console, where you can find some error messages to help diagnose this issue.
        If the problem cannot be fixed from your side, you can contact TiDB Cloud Support for further assistance.
    annotations:
      message: >
        The status of Changefeed {{ $labels.changefeed }} of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/changefeed"> cluster TiDBClusterName </a> in project TiDBProjectName is failed.
        Check the changefeed status in the Changefeed Page and Changefeed Detail Page of the TiDB Cloud console, where you can find some error messages to help diagnose this issue.
        If the problem cannot be fixed from your side, you can contact TiDB Cloud Support for further assistance.
      description: Changefeed status is failed
  - alert: ChangefeedIsWarning
    expr: ticdc_owner_status{instance=~"^db.*"} == 6
    for: 5m
    labels:
      visibility: external
      severity: major
      component: ticdc
      ruleID: "20"
      status: active
      canEdit: status
      monitorTarget: "Changefeed status"
      operator: "is warning"
      description: Changefeed status is warning
      message: >
        The status of Changefeed {{ $labels.changefeed }} of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/changefeed"> cluster TiDBClusterName </a> in project TiDBProjectName is warning.
        Check the changefeed status in the Changefeed Page and Changefeed Detail Page of the TiDB Cloud console, where you can find some error messages to help diagnose this issue.
        If the problem cannot be fixed from your side, you can contact TiDB Cloud Support for further assistance.
    annotations:
      message: >
        The status of Changefeed {{ $labels.changefeed }} of <a href="https://TiDBCloudDomainName/console/clusters/{{ $labels.tidb_cluster_id }}/changefeed"> cluster TiDBClusterName </a> in project TiDBProjectName is warning.
        Check the changefeed status in the Changefeed Page and Changefeed Detail Page of the TiDB Cloud console, where you can find some error messages to help diagnose this issue.
        If the problem cannot be fixed from your side, you can contact TiDB Cloud Support for further assistance.
      description: Changefeed status is warning

