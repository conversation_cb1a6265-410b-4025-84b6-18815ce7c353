groups:
  - name: tikv-worker.rules
    rules:
      - alert: TiKVWorkerIsDown
        annotations:
          message: |
            {{ $labels.region }} Serverless tikv-worker is down.
          summary: "TiKV worker is down."
          region: '{{ $labels.region }}'
        expr: |
          up{component="tikv-worker"} == 0
        for: 1m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: tikv
      - alert: TiKVWorkerFrequentlyRestart
        expr: sum(changes(process_start_time_seconds{component="tikv-worker"}[30m])) by (instance, region) >=4
        for: 5m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: critical
          expr: sum(changes(process_start_time_seconds{component="tikv-worker"}[1h])) by (instance, region) >=4
          component: tikv
        annotations:
          message: 'region: {{ $labels.region }}, instance: {{ $labels.instance }}, values: {{ $value }}'
          value: '{{ $value }}'
          summary: TiKV worker is down for at least 4 times in half an hour