groups:

  - name: tidb-worker/scaler
    rules:
      # CRITICAL
      - alert: TiDBWorkerScalerNoAvailableReplicas
        expr: |
          kube_deployment_status_replicas_available{deployment="scaler",namespace="tidb-worker"} == 0
          or
          absent(kube_deployment_status_replicas_available{deployment="scaler",namespace="tidb-worker"})
        for: 6h
        annotations:
          message: 'no available tidb worker scaler for more than 6 hour'
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: tidb-worker
          expr: kube_deployment_status_replicas_available{deployment="scaler",namespace="tidb-worker"} == 0 or absent(kube_deployment_status_replicas_available{deployment="scaler",namespace="tidb-worker"})

      # WARNING
      - alert: TiDBWorkerScalerMissing
        expr: |
          kube_deployment_status_replicas_available{deployment="scaler",namespace="tidb-worker"} == 0
          or
          absent(kube_deployment_status_replicas_available{deployment="scaler",namespace="tidb-worker"})
        for: 5m
        annotations:
          message: 'no available tidb worker scaler for more than 5 min'
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: tidb-worker
          expr: kube_deployment_status_replicas_available{deployment="scaler",namespace="tidb-worker"} == 0 or absent(kube_deployment_status_replicas_available{deployment="scaler",namespace="tidb-worker"})

      - alert: TiDBWorkerTooManyPendingTasks
        expr: sum by (pod, type) (tidb_worker_scaler_current_task_counter{namespace="tidb-worker", state="pending"}) > 1000
        for: 15m
        annotations:
          description: Too many pending tidb-worker tasks
          message: 'too many pending {{ $labels.type }} tasks in tidb-worker, currently {{ $value }}'
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tidb-worker
          expr: sum by (pod, type) (tidb_worker_scaler_current_task_counter{namespace="tidb-worker", state="pending"}) > 1000

      - alert: TiDBWorkerTooManyPods
        expr: count(kube_pod_info{namespace="tidb-worker"}) > 100
        for: 5m
        annotations:
          description: Too many pods in tidb-worker namespace
          message: 'too many pods running in tidb-worker namespace, currently {{ $value }}'
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tidb-worker
          expr: count(kube_pod_info{namespace="tidb-worker"}) > 100

      - alert: TiDBWorkerTooManyGCWorkers
        expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-gc.*"}) > 50
        for: 5m
        annotations:
          description: Too many gc/gcv2 workers in tidb-worker namespace
          message: 'too many gc/gcv2 workers in tidb-worker namespace, currently {{ $value }}'
        labels:
            tier: serverless
            severity: warning
            provider_type: aws-free-tier
            component: tidb-worker
            expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-gc.*"}) > 50

      - alert: TiDBWorkerTooManyDDLWorkers
        expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-ddl-.*"}) > 40
        for: 5m
        annotations:
          description: Too many ddl workers in tidb-worker namespace
          message: 'too many ddl workers in tidb-worker namespace, currently {{ $value }}'
        labels:
            tier: serverless
            severity: warning
            provider_type: aws-free-tier
            component: tidb-worker
            expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-ddl-.*"}) > 40

      - alert: TiDBWorkerTooManyBatchWorkers
        expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-batch-.*"}) > 20
        for: 5m
        annotations:
          description: Too many batch workers in tidb-worker namespace
          message: 'too many batch workers in tidb-worker namespace, currently {{ $value }}'
        labels:
            tier: serverless
            severity: warning
            provider_type: aws-free-tier
            component: tidb-worker
            expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-batch-.*"}) > 20

      - alert: TiDBWorkerTooManyTTLWorkers
        expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-ttl-.*"}) > 100
        for: 5m
        annotations:
          description: Too many ttl workers in tidb-worker namespace
          message: 'too many ttl workers in tidb-worker namespace, currently {{ $value }}'
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tidb-worker
          expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-ttl-.*"}) > 50

      - alert: TiDBWorkerTooManyAutoAnalyzeWorkers
        expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-auto-analyze-.*"}) > 20
        for: 5m
        annotations:
          description: Too many auto analyze workers in tidb-worker namespace
          message: 'too many ttl workers in tidb-worker namespace, currently {{ $value }}'
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tidb-worker
          expr: count(kube_pod_info{namespace="tidb-worker",pod=~"tidb-worker-auto-analyze-.*"}) > 20

      - alert: TiDBWorkerGCWorkerRunningTooLong
        expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-gc.*"} > 3600
        for: 5m
        annotations:
          description: GC worker has been running for more than 1 hour.
          message: 'gc/gcv2 worker {{ $labels.pod }} has been running for more than 1 hour'
        labels:
            tier: serverless
            severity: warning
            provider_type: aws-free-tier
            component: tidb-worker
            expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-gc.*"} > 3600

      - alert: TiDBWorkerDDLWorkerRunningTooLong
        expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-ddl-.*"} > 86400
        for: 5m
        annotations:
          description: DDL worker has been running for more than 1 day.
          message: 'ddl worker {{ $labels.pod }} has been running for more than 1 day'
        labels:
            tier: serverless
            severity: warning
            provider_type: aws-free-tier
            component: tidb-worker
            expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-ddl-.*"} > 86400

      - alert: TiDBWorkerBatchWorkerRunningTooLong
        expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-batch-.*"} > 86400
        for: 5m
        annotations:
          description: Batch worker has been running for more than 1 day.
          message: 'batch worker {{ $labels.pod }} has been running for more than 1 day'
        labels:
            tier: serverless
            severity: warning
            provider_type: aws-free-tier
            component: tidb-worker
            expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-batch-.*"} > 86400

      - alert: TiDBWorkerTTLWorkerRunningTooLong
        expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-ttl-.*"} > 3600
        for: 5m
        annotations:
          description: TTL worker has been running for more than 1 hour.
          message: 'ttl worker {{ $labels.pod }} has been running for more than 1 hour'
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tidb-worker
          expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-ttl-.*"} > 3600

      - alert: TiDBWorkerAutoAnalyzeWorkerRunningTooLong
        expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-auto-analyze-.*"} > 3600
        for: 5m
        annotations:
          description: Auto Analyze worker has been running for more than 1 hour.
          message: 'auto analyze worker {{ $labels.pod }} has been running for more than 1 hour'
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tidb-worker
          expr: time() - kube_pod_start_time{namespace="tidb-worker",pod=~"tidb-worker-auto-analyze-.*"} > 3600
