groups:
  - name: serverless.rules
    interval: 10s
    rules:
      # important!!! used for serverless metering start.
      - record: cluster:tikv_store_size_bytes:row_based_storage
        expr: |
          quantile_over_time(0.5, sum by(keyspace_id) (avg by(keyspace_id, region_id) (tikv_store_size_bytes{namespace="tidb-serverless",component="tikv",type="used"}))[5m:1m]) > 0
      - record: cluster:tikv_store_size_bytes:columnar_storage
        expr: |
          quantile_over_time(0.5, sum by(keyspace_id) (avg by(keyspace_id, region_id) (tikv_store_size_bytes{namespace="tidb-serverless",component="tikv",type="tiflash_used"}))[5m:1m]) > 0
      # important!!! used for serverless metering end.
