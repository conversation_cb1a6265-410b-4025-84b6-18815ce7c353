groups:
- name: tidb-operator.rules
  rules:
  - alert: TiDBControllerManagerNoAvailableReplicas
    expr: kube_deployment_status_replicas_available{deployment="tidb-operator-controller-manager", namespace="tidb-serverless"}==0
    for: 15m
    labels:
      component: controller-manager
      service: tidb-operator-controller-manager
      severity: critical
      tier: serverless
      provider_type: aws-free-tier
      visibility: operator
    annotations:
      message: There are no running tidb operator controller manager pods.
      region: '{{ $labels.region }}'
