groups:
- name: pod.rules
  rules:
  - alert: <PERSON><PERSON><PERSON>od<PERSON>rashLooping
    expr: increase(kube_pod_container_status_restarts_total[1h]) > 5
    for: 15m
    labels:
      severity: warning
      provider_type: aws-free-tier
      tier: serverless
      component: k8s-pod
    annotations:
      description: There are not running pods.
      message: Pod {{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container}}) is restarting {{ printf "%.2f" $value }} times in 1 hours.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepodcrashlooping
  - alert: KubePodCrashLooping
    expr: increase(kube_pod_container_status_restarts_total[1h]) > 5
    for: 2h
    labels:
      severity: Major
      provider_type: aws-free-tier
      tier: serverless
      component: k8s-pod
    annotations:
      description: There are not running pods.
      message: Pod {{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container}}) is restarting {{ printf "%.2f" $value }} times in 1 hours.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepodcrashlooping
  - alert: KubePodNotReady
    annotations:
      message: Pod {{ $labels.namespace }}/{{ $labels.pod }} has been in a non-ready state for longer than 15 minutes.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepodnotready
    expr: |
      sum by (namespace, pod) (max by(namespace, pod) (kube_pod_status_phase{phase=~"Pending|Unknown"}) * on(namespace, pod) group_left(owner_kind) max by(namespace, pod, owner_kind) (kube_pod_owner{owner_kind!="Job"})) > 0
    for: 15m
    labels:
      severity: warning
      provider_type: aws-free-tier
      tier: serverless
      component: k8s-pod
  - alert: KubePodPhaseFailed
    annotations:
      message: ns {{ $labels.namespace }} has {{ $value }} number of {{ $labels.phase }} pod for longer than 1 hour.
    expr: |
      sum(kube_pod_status_phase{phase!~"Succeeded|Running",provider_type=~"alicloud-serverless|aws-free-tier",namespace!="local-path-storage"}) by (namespace,phase) > 0
    for: 1h
    labels:
      severity: warning
      provider_type: aws-free-tier
      tier: serverless
      component: k8s-pod
  - alert: LocalPathStoragePodPhaseFailed
    annotations:
      message: The Local Path Storage Provisioner has not been running for longer than one hour.
    expr: |
      sum(kube_pod_status_phase{phase!~"Succeeded|Running",provider_type=~"alicloud-serverless|aws-free-tier",namespace!="local-path-storage",pod=~"local-path-provisioner-.*"}) by (phase) > 0
    for: 1h
    labels:
      severity: warning
      provider_type: aws-free-tier
      tier: serverless
      component: k8s-pod
