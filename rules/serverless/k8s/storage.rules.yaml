# copy from rules/common/k8s/storage.rules.yam
groups:
- name: kubernetes-storage
  rules:
  - alert: KubePersistentVolumeFillingUp
    annotations:
      message: The PersistentVolume claimed by {{ $labels.persistentvolumeclaim
        }} in Namespace {{ $labels.namespace }} is only {{ $value | humanizePercentage
        }} free.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepersistentvolumefillingup
    expr: |
      (
        kubelet_volume_stats_available_bytes{job="kubelet",persistentvolumeclaim!~".*victoria-metrics-agent.*|.*vmagent.*|.*ng-monitoring.*|.*index-acceleration",provider_type=~"aws-free-tier|alicloud-serverless"}
          /
         kubelet_volume_stats_capacity_bytes{job="kubelet",persistentvolumeclaim!~".*victoria-metrics-agent.*|.*vmagent.*|.*ng-monitoring.*|.*index-acceleration",provider_type=~"aws-free-tier|alicloud-serverless"}
      ) < 0.15
      and
      kubelet_volume_stats_available_bytes{job="kubelet",persistentvolumeclaim!~".*victoria-metrics-agent.*|.*vmagent.*|.*ng-monitoring.*|.*index-acceleration",provider_type=~"aws-free-tier|alicloud-serverless"} < 50*1024*1024*1024
    for: 1m
    labels:
      component: resilience
      severity: critical
  - alert: KubePersistentVolumeFillingUp
    annotations:
      message: Based on recent sampling, the PersistentVolume claimed by {{ $labels.persistentvolumeclaim
        }} in Namespace {{ $labels.namespace }} is expected to fill up within four
        days. Currently {{ $value | humanizePercentage }} is available.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepersistentvolumefillingup
    expr: |
      (
        kubelet_volume_stats_available_bytes{job="kubelet",persistentvolumeclaim!~".*victoria-metrics-agent.*|.*vmagent.*|.*ng-monitoring.*",provider_type=~"aws-free-tier|alicloud-serverless"}
          /
        kubelet_volume_stats_capacity_bytes{job="kubelet",persistentvolumeclaim!~".*victoria-metrics-agent.*|.*vmagent.*|.*ng-monitoring.*",provider_type=~"aws-free-tier|alicloud-serverless"}
      ) < 0.2
      and
      kubelet_volume_stats_available_bytes{job="kubelet",persistentvolumeclaim!~".*victoria-metrics-agent.*|.*vmagent.*|.*ng-monitoring.*|.*index-acceleration",provider_type=~"aws-free-tier|alicloud-serverless"} < 50*1024*1024*1024
      and
      predict_linear(kubelet_volume_stats_available_bytes{job="kubelet",persistentvolumeclaim!="o11y-vmagent-victoria-metrics-agent",provider_type=~"aws-free-tier|alicloud-serverless"}[6h], 4 * 24 * 3600) < 0
    for: 1h
    labels:
      component: resilience
      severity: major
