groups:
- name: qdrant-api
  rules:
  - alert: AiGatewayTooMuchModelErrors
    expr: sum(increase(qdrant_api_errors_total{category="model", level="error"}[1m])) > 10
    for: 2m
    labels:
      severity: warning
      expr:  sum(increase(qdrant_api_errors_total{category="model", level="error"}[1m])) > 10
      component: ai-gateway
    annotations:
      message: 'ai-gateway too much error from TiDB: {{ $value }}'
      value: '{{ $value }}'
      summary: AI Gateway errors from TiDB
  - alert: AiGatewayTooMuchHttpErrors
    expr: sum(increase(qdrant_api_errors_total{category="http"}[1m])) > 20
    for: 2m
    labels:
      severity: warning
      expr: sum(increase(qdrant_api_errors_total{category="http"}[1m])) > 20
      component: ai-gateway
    annotations:
      message: 'ai-gateway too much http error: {{ $value }}'
      value: '{{ $value }}'
      summary: AI Gateway http code metrics abnormal
  - alert: AiGatewayTooMuchInternalError
    expr: sum(increase(qdrant_api_errors_total{category="http", level="500"}[1m])) > 5
    for: 1m
    labels:
      severity: critical
      expr: sum(increase(qdrant_api_errors_total{category="http", level="500"}[1m])) > 5
      component: ai-gateway
    annotations:
      message: 'ai-gateway got internal error: {{ $value }}'
      value: '{{ $value }}'
      summary: AI Gateway internal error
  - alert: AiGatewayMetricsAbnormal
    expr: sum(increase(qdrant_api_collection_actions_total{region="us-east-1"}[1m])) < 10 or absent(sum(increase(qdrant_api_collection_actions_total{region="us-east-1"} [1m]))) > 0
    for: 2m
    labels:
      severity: warning
      expr:  sum(increase(qdrant_api_collection_actions_total{region="us-east-1"}[1m])) < 10 or absent(sum(increase(qdrant_api_collection_actions_total{region="us-east-1"} [1m]))) > 0
      component: ai-gateway
    annotations:
      message: 'ai-gateway metrics too low in us-east-1: {{ $value }}'
      value: '{{ $value }}'
      summary: AI Gateway metrics abnormal