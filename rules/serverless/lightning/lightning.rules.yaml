groups:
  - name: lightning.rules
    rules:
      - alert: RemoteClientTooManyRetries
        annotations:
          message: |
            {{ $labels.region }} lightning {{ $labels.pod }} remote client {{ $labels.task_id }} too many retries
          summary: "{{ $labels.region }} lightning {{ $labels.pod }} remote client {{ $labels.task_id }} too many retries."
          region: '{{ $labels.region }}'
        expr: |
          lightning_remote_client_retry > 1200
        for: 5m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: lightning
