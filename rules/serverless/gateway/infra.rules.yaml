groups:
  - name: infra.rules
    rules:
      - alert: TiDBGatewayFailToGet
        expr: increase(infra_client_get_duration_seconds_count{job=~"gateway-metrics.*",rlt="fail"}[1m]) > 1
        for: 10m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: gateway
        annotations:
          description: infra Get from infra {{ $labels.name }} is failing.
          kubernetes: '{{ $labels.cluster }}'
      - alert: TiDBGatewayFailToGetIndex
        expr: increase(infra_client_get_index_duration_seconds_count{job=~"gateway-metrics.*",rlt="fail"}[1m]) > 1
        for: 10m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: gateway
        annotations:
          description: infra GetIndex from infra {{ $labels.name }} is failing.
          kubernetes: '{{ $labels.cluster }}'
      - alert: TiDBGatewayRedisXReadTooMany
        expr: increase(infra_consumer_redis_xread_total{job=~"gateway-metrics.*",rlt="fail"}[1m]) > 10
        for: 5m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: gateway
        annotations:
          description: infra redis stream {{ $labels.name }} call XRead too many.
          kubernetes: '{{ $labels.cluster }}'
      - alert: TiDBGatewayRedisResetLastIDTooMany
        expr: increase(infra_consumer_redis_reset_last_id_total{job=~"gateway-metrics.*"}[10m]) > 5
        for: 30m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: gateway
        annotations:
          description: infra redis stream {{ $labels.name }} reset lastID too many.
          kubernetes: '{{ $labels.cluster }}'
