groups:
  - name: gateway.rules
    rules:
      - alert: TiDBGatewayIsDown
        annotations:
          message: region {{ $labels.region }}, serverless gateway {{ $labels.job }} is down.
        expr: |
          up{provider_type=~"aws-free-tier|alicloud-serverless", job=~"gateway-metrics(.*)|privatelink-metrics", pod!~"haproxy(.*)|gateway-controller(.*)"} == 0
        for: 5m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: gateway
      - alert: TiDBGatewayConnectManagerFailed
        annotations:
          message: "gataway {{ $labels.job }} connect manager failed, total count: {{ $value }}."
          region: '{{ $labels.region }}'
        expr: |
          sum(increase(gateway_connection_total{stage="fetch_manager_failed"}[5m])) > 10
        for: 10m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: gateway
      - alert: TiDBGatewayUserEstablishConnectionTooLong
        annotations:
          message: "user connect {{ $labels.job }} tidb pod with {{ $value }} seconds, more then 5s."
          region: '{{ $labels.region }}'
        expr: |
          histogram_quantile(0.95, sum(rate(gateway_connection_seconds_bucket{stage="establish_total", product="serverless",job!="gateway-metrics"}[10m])) by (le, product, region, stage)) > 5
        for: 10m
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: gateway
      - alert: TiDBGatewayP95HandshakeTooLong
        annotations:
          message: "{{ $labels.region }}/{{ $labels.job }} P95 time to connect to TiDB server for {{ $value }} seconds, more then 600ms."
        expr: |
          histogram_quantile(0.95, sum(rate(gateway_connection_seconds_bucket{stage="connect_backend",job!="gateway-metrics"}[5m])) by (region, job, le)) > 0.6
        for: 5m
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessPaidPoolIsBreakingSLA
        annotations:
          message: region {{ $labels.region }}, tidb_pool {{$labels.tidb_pool}}, gateway {{ $labels.pod }} SLA is {{ $value }}, lower than 0.999. [SLA Board](https://clinic.pingcap.com/grafana/d/8fh_Hs-4k/serverless-sla-board?orgId=1&var-control_plane_info=us-west-2-f01%2Faws-us-west-2%2Fdevtier-infra&var-mode=All&var-instance=All&var-cluster_id=All&from=now-1h&to=now)
        expr: |
          (1 - (sum(increase(gateway_connection_sla_total{tidb_pool=~".*vip.*",cluster_id!="",status=~"inner_error|tidb_error|cancel"}[1m])) by (tidb_pool, pod, region) /
          sum(increase(gateway_connection_sla_total{tidb_pool=~".*vip.*",cluster_id!="",status!="missmatch"}[1m])) by (tidb_pool, pod, region))) < 0.999
        for: 10m
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessPaidPoolIsCriticallyBreakingSLA
        annotations:
          message: region {{ $labels.region }}, tidb_pool {{$labels.tidb_pool}}, gateway {{ $labels.pod }} SLA is {{ $value }}, lower than 0.90. [SLA Board](https://clinic.pingcap.com/grafana/d/8fh_Hs-4k/serverless-sla-board?orgId=1&var-control_plane_info=us-west-2-f01%2Faws-us-west-2%2Fdevtier-infra&var-mode=All&var-instance=All&var-cluster_id=All&from=now-1h&to=now)
        expr: |
          (1 - (sum(increase(gateway_connection_sla_total{tidb_pool=~".*vip.*",cluster_id!="",status=~"inner_error|tidb_error|cancel"}[1m])) by (tidb_pool, pod, region) /
          sum(increase(gateway_connection_sla_total{tidb_pool=~".*vip.*",cluster_id!="",status!="missmatch"}[1m])) by (tidb_pool, pod, region))) < 0.9
        for: 10m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessFreePoolIsBreakingSLA
        annotations:
          message: region {{ $labels.region }}, tidb_pool {{$labels.tidb_pool}}, gateway {{ $labels.pod }} SLA is {{ $value }}, lower than 0.5. [SLA Board](https://clinic.pingcap.com/grafana/d/8fh_Hs-4k/serverless-sla-board?orgId=1&var-control_plane_info=us-west-2-f01%2Faws-us-west-2%2Fdevtier-infra&var-mode=All&var-instance=All&var-cluster_id=All&from=now-1h&to=now)
        expr: |
          (1 - (sum(increase(gateway_connection_sla_total{tidb_pool!~".*vip.*",cluster_id!="",status=~"inner_error|tidb_error|cancel"}[10m])) by (tidb_pool, pod, region) /
          sum(increase(gateway_connection_sla_total{tidb_pool!~".*vip.*",cluster_id!="",status!="missmatch"}[10m])) by (tidb_pool, pod, region))) < 0.5
        for: 10m
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessFreeClusterIsBreakingSLA
        annotations:
          message: region {{ $labels.region }}, tidb_pool {{$labels.tidb_pool}}, gateway {{ $labels.pod }}, cluster {{ $labels.cluster_id }} SLA is {{ $value }}, lower than 0.5. [SLA Board](https://clinic.pingcap.com/grafana/d/8fh_Hs-4k/serverless-sla-board?orgId=1&var-control_plane_info=us-west-2-f01%2Faws-us-west-2%2Fdevtier-infra&var-mode=All&var-instance=All&var-cluster_id=All&from=now-1h&to=now)
        expr: |
          (1 - (sum(increase(gateway_connection_sla_total{tidb_pool!~".*vip.*",cluster_id!="",status=~"inner_error|tidb_error|cancel"}[10m])) by (tidb_pool, pod, region, cluster_id) /
          sum(increase(gateway_connection_sla_total{tidb_pool!~".*vip.*",cluster_id!="",status!="missmatch"}[10m])) by (tidb_pool, pod, region, cluster_id))) < 0.5
        for: 10m
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessPaidClusterIsBreakingSLA
        annotations:
          message: region {{ $labels.region }}, tidb_pool {{$labels.tidb_pool}}, gateway {{ $labels.pod }}, cluster {{ $labels.cluster_id }} SLA is {{ $value }}, lower than 0.99. [SLA Board](https://clinic.pingcap.com/grafana/d/8fh_Hs-4k/serverless-sla-board?orgId=1&var-control_plane_info=us-west-2-f01%2Faws-us-west-2%2Fdevtier-infra&var-mode=All&var-instance=All&var-cluster_id=All&from=now-1h&to=now)
        expr: |
          (1 - (sum(increase(gateway_connection_sla_total{tidb_pool=~".*vip.*",cluster_id!="",status=~"inner_error|tidb_error|cancel"}[1m])) by (tidb_pool, pod, region, cluster_id) /
          sum(increase(gateway_connection_sla_total{tidb_pool=~".*vip.*",cluster_id!="",status!="missmatch"}[1m])) by (tidb_pool, pod, region, cluster_id))) < 0.99
        for: 5m
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessPaidClusterIsCriticallyBreakingSLA
        annotations:
          message: region {{ $labels.region }}, tidb_pool {{$labels.tidb_pool}}, gateway {{ $labels.pod }}, cluster {{ $labels.cluster_id }} SLA is {{ $value }}, lower than 0.5. [SLA Board](https://clinic.pingcap.com/grafana/d/8fh_Hs-4k/serverless-sla-board?orgId=1&var-control_plane_info=us-west-2-f01%2Faws-us-west-2%2Fdevtier-infra&var-mode=All&var-instance=All&var-cluster_id=All&from=now-1h&to=now)
        expr: |
          (1 - (sum(increase(gateway_connection_sla_total{tidb_pool=~".*vip.*",cluster_id!="",status=~"inner_error|tidb_error|cancel"}[1m])) by (tidb_pool, pod, region, cluster_id) /
          sum(increase(gateway_connection_sla_total{tidb_pool=~".*vip.*",cluster_id!="",status!="missmatch"}[1m])) by (tidb_pool, pod, region, cluster_id))) < 0.9
        for: 5m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessSvipClusterIsCriticallyBreakingSLA
        annotations:
          message: region {{ $labels.region }}, tidb_pool {{$labels.tidb_pool}}, gateway {{ $labels.pod }}, cluster {{ $labels.cluster_id }} SLA is {{ $value }}, lower than 0.99. [SLA Board](https://clinic.pingcap.com/grafana/d/8fh_Hs-4k/serverless-sla-board?orgId=1&var-control_plane_info=us-west-2-f01%2Faws-us-west-2%2Fdevtier-infra&var-mode=All&var-instance=All&var-cluster_id=All&from=now-1h&to=now)
        expr: |
          (1 - (sum(increase(gateway_connection_sla_total{tidb_pool="super-vip-tidb-pool",cluster_id!="",status=~"inner_error|tidb_error|cancel"}[1m])) by (tidb_pool, pod, region, cluster_id) /
          sum(increase(gateway_connection_sla_total{tidb_pool="super-vip-tidb-pool",cluster_id!="",status!="missmatch"}[1m])) by (tidb_pool, pod, region, cluster_id))) < 0.99
        for: 1m
        labels:
          tier: serverless
          severity: Major
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessPocAction
        annotations:
          message: cluster {{ $labels.region }} / {{ $labels.service }} / {{ $labels.tidb_pool }} / {{ $labels.cluster_id }} has {{ $value }} concurrent connections, may be doing a PoC, please pay attention.
        expr: |
          sum(increase(gateway_connection_sla_total{cluster_id!=""}[1m])) by (cluster_id, region, tidb_pool, service) > 1000
        for: 10m
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: gateway
      - alert: ServerlessIsBreakingSLAorMetricMissing
        annotations:
          message: serverless SLA is {{ $value }}, lower than 0.5. [SLA Board](https://clinic.pingcap.com/grafana/d/8fh_Hs-4k/serverless-sla-board?orgId=1&var-control_plane_info=us-west-2-f01%2Faws-us-west-2%2Fdevtier-infra&var-mode=All&var-instance=All&var-cluster_id=All&from=now-1h&to=now)
        expr: |
           (1 - ((sum(increase(gateway_connection_sla_total{cluster_id!="",status=~"inner_error|tidb_error|cancel"}[1m])) or vector(1)) / (sum(increase(gateway_connection_sla_total{cluster_id!="",status!="missmatch"}[1m])) or vector(1)))) < 0.50
        for: 10m
        labels:
          severity: Major
          provider_type: aws-free-tier
          component: gateway
