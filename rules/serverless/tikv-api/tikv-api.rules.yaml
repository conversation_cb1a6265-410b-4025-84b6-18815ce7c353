groups:
  - name: tikv-api.rules
    rules:
      - alert: TiKVAPIIsDown
        annotations:
          message: |
            {{ $labels.region }} Serverless tikv-api is down.
          summary: "{{ $labels.region }} Serverless tikv-api is down."
          region: '{{ $labels.region }}'
        expr: |
          up{job=~"tikv-api-service.*"} == 0
        for: 5m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: tikv
      - alert: TiKVNativeRestoreFailure
        annotations:
          message: |
            {{ $labels.region }} Serverless tikv-api restore keyspace failed.
          summary: "{{ $labels.region }} Serverless tikv-api restore keyspace failed."
          region: '{{ $labels.region }}'
        expr: |
          changes(tikv_worker_native_br_counter{type="restore_keyspace_fail"}[10m]) > 0
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tikv
      - alert: TiKVAPIQueryLoadDataWorkerFailure
        annotations:
          message: |
            {{ $labels.region }} Serverless tikv-api query load-data-worker failed.
          summary: "{{ $labels.region }} Serverless tikv-api query load-data-worker failed."
          region: '{{ $labels.region }}'
        expr: |
          changes(tikv_worker_worker_scaler_query_failures_counter[5m]) > 0
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tikv
      - alert: TiKVAPINoSuccessBackupFor30m
        annotations:
          message: |
            Serverless tikv-api no successful backup for 30 minutes.
          region: '{{ $labels.region }}'
        expr: |
          changes(native_br_backup_success[30m]) == 0
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tikv
