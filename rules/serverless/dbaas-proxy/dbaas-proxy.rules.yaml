groups:
- name: dbaas-proxy.rules
  rules:
  - alert: AwsServerlessTierDBaaSProxyNoAvailableReplicas
    expr: kube_deployment_status_replicas_available{deployment="dbaas-proxy", cluster=~"freetier.*"}==0
    for: 5m
    labels:
      severity: critical
      component: dbaas-proxy
      provider_type: aws-free-tier
      tier: serverless
    annotations:
      message: There are no running dbaas proxy pods.
      region: '{{ $labels.region }}'
