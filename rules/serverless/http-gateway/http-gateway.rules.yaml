groups:
  - name: http.gateway.rules
    rules:
      - alert: HTTPGatewayIsDown
        annotations:
          message: Http gataway region {{ $labels.region }} pod {{ $labels.pod }} is down.
        expr: |
          up{job=~"tidb-http-gateway-metrics.*"} == 0
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          provider_type: aws-free-tier
          component: ecosystem-server
      - alert: HTTPGatewayHighCPU
        annotations:
          message: HTTP gataway region {{ $externalLabels.o11y_region }} pod {{ $labels.pod }} cpu usage / limits(4) is higher than 80%, current value is {{ $value }}).
        expr: |
          (sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{ namespace="tidb-admin", pod=~"tidb-http-gateway.*"}) by (pod) / sum(kube_pod_container_resource_limits{ namespace="tidb-admin", pod=~"tidb-http-gateway.*",resource="cpu"}) by (pod) ) > 0.8
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          provider_type: aws-free-tier
          component: ecosystem-server
      - alert: HTTPGatewayHighMemory
        annotations:
          message: HTTP gataway region {{ $labels.region }} pod {{ $labels.pod }} memory usage / limits(8Gi) is higher than 80%, current value is {{ $value }}).
        expr: |
          (sum(container_memory_working_set_bytes{ namespace="tidb-admin", pod=~"tidb-http-gateway.*", container!="", image!=""}) by (region,pod) / sum(kube_pod_container_resource_limits{namespace="tidb-admin", pod=~"tidb-http-gateway.*",resource="memory"}) by (region,pod)) > 0.8
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          provider_type: aws-free-tier
          component: ecosystem-server
      - alert: HTTPGatewayTooMany5xx
        annotations:
          message: HTTP gataway region {{ $labels.region }} too many 5xx, times ({{ printf "%.2f" $value }}%).
        expr: |
          (sum(increase(http_gateway_connection_seconds_count{service=~"tidb-http-gateway-metrics",status=~"5.*"}[5m])) by (region)
            / sum(increase(http_gateway_connection_seconds_count{service=~"tidb-http-gateway-metrics"}[5m])) by (region)
          ) > 0.1
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
      - alert: HTTPGatewayTooMany5xxTotal
        annotations:
          message: HTTP gataway region {{ $labels.region }} too many 5xx, {{ $value }} times in 5 minutes.
        expr: |
          sum(increase(http_gateway_connection_seconds_count{service=~"tidb-http-gateway-metrics",status=~"5.*"}[5m])) by (region) > 100
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
      
      
