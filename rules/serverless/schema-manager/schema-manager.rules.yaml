groups:
  - name: schema-manager.rules
    rules:
      - alert: SchemaManagerIsDown
        annotations:
          message: |
            {{ $labels.region }} Serverless schema-manager is down.
          summary: "Schema manager is down."
          region: '{{ $labels.region }}'
        expr: |
          up{component="schema-manager"} == 0
        for: 1m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: tikv
      - alert: SchemaManagerFrequentlyRestart
        expr: sum(changes(process_start_time_seconds{component="schema-manager"}[30m])) by (instance, region) >=4
        for: 5m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: critical
          expr: sum(changes(process_start_time_seconds{component="schema-manager"}[30m])) by (instance, region) >=4
          component: tikv
        annotations:
          message: 'region: {{ $labels.region }}, instance: {{ $labels.instance }}, values: {{ $value }}'
          value: '{{ $value }}'
          summary: "Schema manager is down for at least 4 times in half an hour"