groups:
  - name: load-data-worker.rules
    rules:
      - alert: LoadDataWorkerIsDown
        annotations:
          message: |
            {{ $labels.region }} Serverless load-data-worker is down.
          summary: "Load data worker is down."
          region: '{{ $labels.region }}'
        expr: |
          up{component="load-data-worker"} == 0
        for: 5m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: tikv
      - alert: LoadDataWorkerFrequentlyRestart
        expr: sum(changes(process_start_time_seconds{component="load-data-worker"}[30m])) by (instance, region) >=4
        for: 5m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: critical
          expr: sum(changes(process_start_time_seconds{component="load-data-worker"}[30m])) by (instance, region) >=4
          component: tikv
        annotations:
          message: 'region: {{ $labels.region }}, instance: {{ $labels.instance }}, values: {{ $value }}'
          value: '{{ $value }}'
          summary: Load data worker is down for at least 4 times in half an hour
      - alert: LoadDataWorkerSplitRegionTooManyRetires
        annotations:
          message: |
            {{ $labels.region }} Serverless load data worker {{ $labels.pod }} split region too many retries
          summary: "{{ $labels.region }} Serverless load data worker {{ $labels.pod }} split region too many retries."
          region: '{{ $labels.region }}'
        expr: |
          tikv_worker_load_data_split_region_failures_counter > 10
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tikv
      - alert: LoadDataWorkerIngestRangeGroupTooManyRetires
        annotations:
          message: |
            {{ $labels.region }} Serverless load data worker {{ $labels.pod }} ingest range group too many retries
          summary: "{{ $labels.region }} Serverless load data worker {{ $labels.pod }} ingest range group too many retries."
          region: '{{ $labels.region }}'
        expr: |
          tikv_worker_load_data_ingest_range_group_failures_counter > 10
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tikv
      - alert: LoadDataWorkerGetShardMetaTooManyRetires
        annotations:
          message: |
            {{ $labels.region }} Serverless load data worker {{ $labels.pod }} get shard meta  too many retries
          summary: "{{ $labels.region }} Serverless load data worker {{ $labels.pod }} get shard meta too many retries."
          region: '{{ $labels.region }}'
        expr: |
          tikv_worker_load_data_get_shard_meta_failures_counter > 10
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: tikv

