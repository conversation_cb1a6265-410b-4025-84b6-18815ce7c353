groups:
  - name: serverless.cert-manager.rules
    rules:
      - alert: TiDBServerlessCertsRenewed
        annotations:
          description: |
            Some certificate in the namespace tidb-serverless has been renewed.
            It might require the restart the corresponding service to reload the latest certificate before the old certificate is expired (about 15 days later).
          message: The cert {{ $labels.exported_namespace }}/{{ $labels.name }} renewed.
        expr: |
          certmanager_certificate_expiration_timestamp_seconds{exported_namespace = "tidb-serverless"} - time() < (15 * 24 * 3600 - 3600) # 14d23h
        for: 10m
        labels:
          severity: warning
          tier: serverless
          provider_type: aws-free-tier
          component: cert-manager
      - alert: TiFlashAutoScaleCertsRenewed
        annotations:
          description: |
            Some certificate in the namespace tiflash-autoscale has been renewed.
            It might require the restart the corresponding service to reload the latest certificate before the old certificate is expired (about 15 days later).
          message: The cert {{ $labels.exported_namespace }}/{{ $labels.name }} renewed.
        expr: |
          certmanager_certificate_expiration_timestamp_seconds{exported_namespace = "tiflash-autoscale"} - time() < (15 * 24 * 3600 - 3600) # 14d23h
        for: 10m
        labels:
          severity: warning
          tier: serverless
          provider_type: aws-free-tier
          component: cert-manager
