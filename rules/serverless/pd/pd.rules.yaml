groups:
- name: pd/pd
  rules:
  - alert: PDServerIsDown
    expr: up{component="pd"} == 0
    for: 10m
    labels:
      severity: critical
      expr: up{component="pd"} == 0
      tier: serverless
      component: pd
      provider_type: aws-free-tier
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.f5wwrkav7k5f
      value: '{{ $value }}'
      summary: PD server is down
  - alert: PDDiscoverOfflineStore
    expr: (sum ( pd_cluster_status{type="store_down_count"} ) by (cluster_id, instance, tenant, provider_type) > 0) and (sum(etcd_server_is_leader) by (cluster_id, instance, tenant, provider_type) > 0)
    for: 5m
    labels:
      severity: warning
      tier: serverless
      provider_type: aws-free-tier
      expr:  (sum ( pd_cluster_status{type="store_down_count"} ) by (cluster_id, instance, tenant, provider_type) > 0) and (sum(etcd_server_is_leader) by (cluster_id, instance, tenant, provider_type) > 0)
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.zigyfcz6fd17
      value: '{{ $value }}'
      summary: "PD found store is down"
  - alert: PDDiscoverLowSpaceStore
    expr: (sum ( pd_cluster_status{type="store_low_space_count"} ) by (cluster_id, instance, tenant, provider_type) > 0) and (sum(etcd_server_is_leader) by (cluster_id, instance, tenant, provider_type) > 0)
    for: 5m
    labels:
      severity: warning
      tier: serverless
      provider_type: aws-free-tier
      expr:  (sum ( pd_cluster_status{type="store_low_space_count"} ) by (cluster_id, instance, tenant, provider_type) > 0) and (sum(etcd_server_is_leader) by (cluster_id, instance, tenant, provider_type) > 0)
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.t3cn3udx76m7
      value: '{{ $value }}'
      summary: "PD found some store space is too low"
  - alert: PDRegionDownPeerTooLong
    expr: histogram_quantile(0.99, sum(rate(pd_regions_abnormal_peer_duration_seconds_bucket{type="down-peer"}[5m])) by (le, type, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
    for: 5m
    labels:
      severity: warning
      tier: serverless
      provider_type: aws-free-tier
      expr: histogram_quantile(0.99, sum(rate(pd_regions_abnormal_peer_duration_seconds_bucket{type="down-peer"}[5m])) by (le, type, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.2boq1zqje9s
      value: '{{ $value }}'
      summary: "PD found some region has down peer too long duration"
  - alert: PDRegionMissPeerTooLong
    expr: histogram_quantile(0.99, sum(rate(pd_regions_abnormal_peer_duration_seconds_bucket{type="miss-voter-peer"}[5m])) by (le, type, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
    for: 5m
    labels:
      severity: warning
      tier: serverless
      provider_type: aws-free-tier
      expr: histogram_quantile(0.99, sum(rate(pd_regions_abnormal_peer_duration_seconds_bucket{type="miss-voter-peer"}[5m])) by (le, type, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.my2gfezc47ow
      value: '{{ $value }}'
      summary: "PD found some region has miss votor peer too long duration"
  - alert: TiKVRegionPendingPeerTooLong
    expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_peer_pending_duration_seconds_bucket[1m])) BY (le, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
    for: 5m
    labels:
      severity: warning
      tier: serverless
      provider_type: aws-free-tier
      expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_peer_pending_duration_seconds_bucket[1m])) BY (le, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.lizm9t5f8fpu
      value: '{{ $value }}'
      summary: "TiKV found some region has pending peer too long duration"
  - alert: GCSafePointNotAdvancing
    expr: (max(pd_gc_gc_safepoint) - max((pd_gc_gc_safepoint offset 24h))) == 0
    for: 5m
    labels:
      severity: warning
      tier: serverless
      provider_type: aws-free-tier
      expr: (max(pd_gc_gc_safepoint) - max((pd_gc_gc_safepoint offset 24h))) == 0
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url:
      value: '{{ $value }}'
      summary: "GC safepoint has not advanced for more than 3 hours"
  - alert: GCSafePointAbsent
    expr:  absent(pd_gc_gc_safepoint) > 0
    for: 30m
    labels:
      severity: warning
      tier: serverless
      provider_type: aws-free-tier
      expr: absent(pd_gc_gc_safepoint) > 0
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url:
      value: '{{ $value }}'
      summary: "GC safepoint metrics absent for 30 minutes"
  - alert: OperatorTimeout
    expr: sum(delta(pd_schedule_operators_count{event="timeout"}[1m])) > 0
    for: 10m
    labels:
      severity: warning
      tier: serverless
      provider_type: aws-free-tier
      expr: sum(delta(pd_schedule_operators_count{event="timeout"}[1m])) > 0
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url:
      value: '{{ $value }}'
      summary: "PD operator timeout"
  - alert: RUTokenUseRate
    expr: resource_manager_resource_unit_request_unit_consume_rate > 0.8
    for: 5m
    labels:
      severity: warning
      tier: serverless
      expr: resource_manager_resource_unit_request_unit_consume_rate > 0.8
      component: pd
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, resource_group: {{ $labels.resource_group }}, value: {{ $value }}'
      runbook_url:
      value: '{{ $value }}'
      summary: "RU Token use too fast"
