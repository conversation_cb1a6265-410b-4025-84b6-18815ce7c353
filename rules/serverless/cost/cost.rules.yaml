groups:

- name: cost.rules
  rules:
  - alert: DailyOperationCostIncreaseTooQuick
    expr: |
      (max(serverless_billing_daily_operation_cost{offset="2"}) by (exported_provider, exported_region) - max(serverless_billing_daily_operation_cost{offset="2"} offset 1d) by (exported_provider, exported_region)) > 50 > (0.2*max(serverless_billing_daily_operation_cost{offset="2"} offset 1d) by (exported_provider, exported_region))
      or
      (max(serverless_billing_daily_operation_cost{offset="2"}) by (exported_provider, exported_region) - max(serverless_billing_daily_operation_cost{offset="2"} offset 7d) by (exported_provider, exported_region)) > 50 > (0.2*max(serverless_billing_daily_operation_cost{offset="2"} offset 7d) by (exported_provider, exported_region))
    for: 25h
    labels:
      tier: serverless
      provider_type: aws-free-tier
      severity: warning
      component: operating-cost
    annotations:
      message: 'The regional daily operating cost has increased by ${{ $value }}, please pay attention to this. [monitoring board](https://clinic.pingcap.com/grafana/d/KmTtYdBNk/serverless-resource-usage?orgId=1&viewPanel=25&from=now-30d&to=now)'
