groups:
  - name: o11y.rules
    rules:
      - alert: VmagentIsDown
        annotations:
          message: |
           "TiDB Serverless vmagent {{ $labels.job }} is down."
          region: '{{ $labels.region }}'
        expr: |
          up{job=~".*vmagent.*"} == 0
        for: 5m
        labels:
          tier: serverless
          severity: warning
          provider_type: aws-free-tier
          component: vmagent
      - alert: RowStorageRecordingRuleMissing
        expr: (count(cluster:tikv_store_size_bytes:row_based_storage) or vector (-1)) < 0
        for: 5m
        labels:
          severity: warning
          component: vmagent
        annotations:
          summary: "Row-based Storage recording rule missing"
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless/serverless.recordingrules.yaml
      - alert: ColumnarStorageRecordingRuleMissing
        expr: (count(cluster:tikv_store_size_bytes:columnar_storage) or vector (-1)) < 0
        for: 5m
        labels:
          severity: warning
          component: vmagent
        annotations:
          summary: "Columnar Storage recording rule missing"
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless/serverless.recordingrules.yaml
