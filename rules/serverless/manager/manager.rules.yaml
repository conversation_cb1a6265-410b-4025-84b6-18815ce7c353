groups:
  - name: manager.rules
    rules:
      - alert: ManagerOutOfMemory
        annotations:
          message: |
            At least one Manager node in cluster TiDBCluster<PERSON>ame in project TiDBProject<PERSON>ame ran out of memory.
          description: At least one Manager node in the cluster has run out of memory
          region: '{{ $labels.region }}'
        expr: |
          sum(label_replace(increase(kube_pod_container_status_restarts_total{cluster=~"freetier.*",job="manager"}[5m])>=1
          AND ignoring(reason) kube_pod_container_status_last_terminated_reason{cluster=~"freetier.*",reason='OOMKilled',job="manager"}==1,"cluster_id","$1","pod","db(.*)-(.*)-(.*)"))by(cluster_id)
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: manager
      - alert: ManagerIsDown
        annotations:
          message: |
            region {{ $labels.region }}, manager of serverless is down.
        expr: |
          up{cluster=~"freetier.*", job="manager"} == 0
        for: 10m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: manager
      - alert: ManagerAssignTidbFailedTooMuch
        annotations:
          message: |
            region {{ $labels.region }}, manager assign tidb failed too much, affects {{ $value }} users.
          region: '{{ $labels.region }}'
        expr: |
          count(sum(increase(tidbmanager_assign_tidb_seconds_count{stage="total_fail"}[10m])) by (token,region) > 0) by (region) > 10
        for: 10m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerAssignTidbFailedByToken
        annotations:
          message: |
            region {{ $labels.region }}, manager assign tidb failed for user token: {{ $labels.token }}, count: {{ $value }}.
          region: '{{ $labels.region }}'
        expr: |
          sum(increase(tidbmanager_assign_tidb_seconds_count{stage="total_fail"}[1m])) by (token,region) > 2
        for: 10m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerAssignTidbFailedByToken
        annotations:
          message: |
            region {{ $labels.region }}, manager assign tidb failed for user token: {{ $labels.token }}, count: {{ $value }}.
          region: '{{ $labels.region }}'
        expr: |
          sum(increase(tidbmanager_assign_tidb_seconds_count{stage="total_fail",provider_type="alicloud-serverless"}[1m])) by (token,region) > 2
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: critical
          component: manager
      - alert: ManagerZRUnevictablePVC
        annotations:
          message: |
            Manager ZR found unevictable PVC, region {{ $labels.region }}, pod {{ $labels.podname }}.
          region: '{{ $labels.region }}'
        expr: |
          increase(zr_evicter_unevictable_pvc_total{job="manager"}[1m]) > 0
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: critical
          component: '{{ $labels.component }}'
      - alert: ManagerTidbFreePodMoreThan200
        annotations:
          message: |
            Manager tidb free pod more than 200, current number {{ $value }}, pool {{ $labels.pool_ns }}, region {{ $labels.region }}.
        expr: |
          sum(tidbmanager_cache_size{type="free",region!="us-east-1"}) by (pool_ns, region) > 200
        for: 10m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerTidbFreePodMoreThan300
        annotations:
          message: |
            Manager tidb free pod more than 300, current number {{ $value }}, pool {{ $labels.pool_ns }}, region {{ $labels.region }}.
        expr: |
          sum(tidbmanager_cache_size{type="free",region="us-east-1"}) by (pool_ns, region) > 300
        for: 10m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerNoFreeTidbPod
        annotations:
          message: |
            Manager has no free tidb pods, current number {{ $value }}, pool {{ $labels.pool_ns }}, region {{ $labels.region }}.
        expr: |
          sum(tidbmanager_cache_size{type="free",pool_ns=~"vip-tidb-pool|tidb-pool"}) by (pool_ns, region) == 0
        for: 1m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerNoFreeTidbPod
        annotations:
          message: |
            Manager has no free tidb pods, current number {{ $value }}, pool {{ $labels.pool_ns }}, region {{ $labels.region }}.
        expr: |
          sum(tidbmanager_cache_size{type="free",pool_ns!~"export(.*)",provider_type="alicloud-serverless"}) by (pool_ns, region) <= 0
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: critical
          component: manager
      - alert: ManagerClusterCreationTakesTooLong
        annotations:
          message: |
            Cluster {{ $labels.cluster_id }} creation takes {{ $value }} seconds longer than 300, region {{ $labels.region }}.
        expr: |
          (sum(cluster_creation_seconds_sum{cluster_type!="branch",is_restore!="true"}) by (cluster_id,region) / sum(cluster_creation_seconds_count{cluster_type!="branch",is_restore!="true"}) by (cluster_id,region)) > 300
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerRestoredClusterCreationTakesTooLong
        annotations:
          message: |
            Restored Cluster {{ $labels.cluster_id }} creation takes {{ $value }} seconds longer than 300, region {{ $labels.region }}.
        expr: |
          (sum(cluster_creation_seconds_sum{cluster_type!="branch",is_restore="true"}) by (cluster_id,region) / sum(cluster_creation_seconds_count{cluster_type!="branch",is_restore="true"}) by (cluster_id,region)) > 300
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerRemovePodQueueTooLong
        annotations:
          message: |
            The tidb pod remove queue too long, current length {{ $value }}, region {{ $labels.region }}.
        expr: |
          count(tidbmanager_remove_pod_queue_len > 0)
        for: 10m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerAvailableKeyspaceNotEnough
        annotations:
          message: |
            The available keyspace not enough in keyspace-pool, current value {{ $value }}, region {{ $labels.region }}.
        expr: |
          keyspace_available{pool_ns="keyspace-pool"} < 8
        for: 10m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: ManagerEncryptAvailableKeyspaceNotEnough
        annotations:
          message: |
            The available keyspace not enough in encrypt-keyspace-pool, current value {{ $value }}, region {{ $labels.region }}.
        expr: |
          keyspace_available{pool_ns="encrypt-keyspace-pool"} < 2
        for: 10m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: UnmanagedTiDBPodsTooMany
        annotations:
          message: |
            The ns {{ $labels.pool_ns }} has {{ $value }} unmanaged pods for 1h. [Clinic Dashboard](https://clinic.pingcap.com/grafana/d/KmTtYdBNk/serverless-resource-usage?orgId=1&var-control_plane_info=ap-northeast-1-a01%2Falicloud-ap-northeast-1%2Fdevtier-infra)
        expr: |
          sum(label_replace(sum(kube_pod_status_phase{phase="Running",namespace=~".*tidb-pool.*"}) by (namespace), "pool_ns", "$1", "namespace", "(.*)")) by (pool_ns)
          -
          sum(max(tidbmanager_cache_size{type!="assign_token"}) by (pool_ns, type)) by (pool_ns) > 5
        for: 1h
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
      - alert: TiDBPodReplicasTooMany
        annotations:
          message: |
            The cluster {{ $labels.cluster_id }}/{{ $labels.token }}/{{ $labels.keyspace_id }} has {{ $value }} tidb pod replicas for 24h. [Clinic Dashboard](https://clinic.pingcap.com/grafana/d/KmTtYdBNk/serverless-resource-usage?orgId=1&var-control_plane_info=ap-northeast-1-a01%2Falicloud-ap-northeast-1%2Fdevtier-infra)
        expr: |
          count(sum(rate(tidb_executor_statement_total[1m])) by (instance,keyspace_id)) by (keyspace_id) > 5
          or
          max(gateway_tidb_replicas) by (cluster_id, token) > 5
        for: 24h
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: warning
          component: manager
