groups:

- name: tidb/tidb
  rules:
  - alert: TiDBDiscoveredTimeJumpBack
    expr: increase(tidb_monitor_time_jump_back_total[10m])  > 0
    for: 5m
    labels:
      tier: serverless
      provider_type: aws-free-tier
      severity: warning
      expr:  increase(tidb_monitor_time_jump_back_total[10m])  > 0
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.region }}/{{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ph7vof1otr6g
      value: '{{ $value }}'
      summary: TiDB monitor found time jump back error
  - alert: TiDBHighTokenUsage
    expr: sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type) > 0 and (sum(tidb_server_tokens{}) by (cluster_id, instance, tenant,provider_type) / sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type)) > 0.8
    for: 5m
    labels:
      tier: serverless
      provider_type: aws-free-tier
      severity: warning
      expr: sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type) > 0 and (sum(tidb_server_tokens{}) by (cluster_id, instance, tenant,provider_type) / sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type)) > 0.8
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }} token usage is values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.es82zlkd14z
      value: '{{ $value }}'
      summary: TiDB token usage too high
  - alert: TiDBQueryUnexpectedlyFailed
    expr: increase(tidb_server_execute_error_total{type=~".*8027|.*8120|.*8021|.*8024|.*9001|.*9002|.*9003|.*9011|.*9012|.*9013"}[5m]) > 1
    for: 5m
    labels:
      tier: serverless
      provider_type: aws-free-tier
      severity: warning
      expr: increase(tidb_server_execute_error_total{type=~".*8027|.*8120|.*8021|.*8024|.*9001|.*9002|.*9003|.*9011|.*9012|.*9013"}[5m]) > 1
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.region }}/{{ $labels.cluster_id }}, instance: {{ $labels.instance }}, error type: {{ $labels.type }}, count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.vr9n074q793p
      value: '{{ $value }}'
      summary: TiDB query got unexpected error
        #- alert: TiDBStatsHealthy
    #expr: avg(tidb_statistics_stats_healthy{type="[0,50)"}) by (cluster_id, instance, tenant, provider_type) / avg(tidb_statistics_stats_healthy{type="[0,100]"}) by (cluster_id, instance, tenant, provider_type) > 0.5
      #for: 1d
      #labels:
      #tier: serverless
        #provider_type: aws-free-tier
        #severity: warning
        #expr: avg(tidb_statistics_stats_healthy{type="[0,50)"}) by (cluster_id, instance, tenant, provider_type) / avg(tidb_statistics_stats_healthy{type="[0,100]"}) by (cluster_id, instance, tenant, provider_type) > 0.5
        #component: tidb
        #annotations:
      #message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
        #runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.k4nn0mporild
        #value: '{{ $value }}'
        #      summary: More than half of tables's statisitics TiDB server are in bad healthy for one day
  - alert: TidbPodNotReadyTooMuch
    annotations:
      message: tidb-pool namespace {{ $value }} pods has been in a non-ready state for longer than 10 minutes.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepodnotready
    expr: |
      count(sum by (namespace, pod) (max by(namespace, pod) (kube_pod_status_phase{namespace=~"tidb-pool", phase=~"Pending|Unknown"}) * on(namespace, pod) group_left(owner_kind) max by(namespace, pod, owner_kind) (kube_pod_owner{owner_kind!="Job"})) > 0) > 20
    for: 10m
    labels:
      severity: critical
      provider_type: aws-free-tier
      tier: serverless
      component: tidb
  - alert: TiDBGoGCTooFrequently
    annotations:
      message: 'region: {{ $labels.region }}, ns: {{ $labels.namespace }}, instance: {{ $labels.instance }}, values: {{ $value }}'
    expr: |
      max(increase(go_gc_cycles_total_gc_cycles_total{container="tidb"}[1m])) by (namespace,region,instance) * max(go_gc_duration_seconds{container="tidb"}) by (namespace,region,instance) > 10
    for: 10m
    labels:
      severity: warning
      provider_type: aws-free-tier
      tier: serverless
      component: tidb
  - alert: TiDBVpaReportMemoryFail
    annotations:
      message: 'region: {{ $labels.region }}, ns: {{ $labels.namespace }}, instance: {{ $labels.instance }}, values: {{ $value }}'
    expr: |
      sum(increase(tidb_vpa_vpa_report_counter{type="failed"}[5m])) > 1
    for: 10m
    labels:
      severity: critical
      provider_type: aws-free-tier
      tier: serverless
      component: tidb
  - alert: TiDBNoAvailableTiFlashComputeNode
    annotations:
      message: 'region: {{ $labels.region }}, ns: {{ $labels.namespace }}, instance: {{ $labels.instance }}, values: {{ $value }}'
    expr: |
      increase(tidb_server_no_available_tiflash_compute_node[1m]) > 0
    for: 10m
    labels:
      severity: critical
      provider_type: aws-free-tier
      tier: serverless
      component: tidb
