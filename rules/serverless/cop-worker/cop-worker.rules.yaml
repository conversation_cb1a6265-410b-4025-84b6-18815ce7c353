groups:
  - name: cop-worker.rules
    rules:
      - alert: CopWorkerIsDown
        annotations:
          message: |
            {{ $labels.region }} Serverless cop-worker is down.
          summary: "Cop worker is down."
          region: '{{ $labels.region }}'
        expr: |
          up{component="cop-worker"} == 0
        for: 5m
        labels:
          tier: serverless
          severity: critical
          provider_type: aws-free-tier
          component: tikv
      - alert: CopWorkerFrequentlyRestart
        expr: sum(changes(process_start_time_seconds{component="cop-worker"}[30m])) by (instance, region) >=4
        for: 5m
        labels:
          tier: serverless
          provider_type: aws-free-tier
          severity: critical
          expr: sum(changes(process_start_time_seconds{component="cop-worker"}[30m])) by (instance, region) >=4
          component: tikv
        annotations:
          message: 'region: {{ $labels.region }}, instance: {{ $labels.instance }}, values: {{ $value }}'
          value: '{{ $value }}'
          summary: Cop worker is down for at least 4 times in half an hour