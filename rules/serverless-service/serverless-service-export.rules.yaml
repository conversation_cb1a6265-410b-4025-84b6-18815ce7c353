groups:
  - name: serverless-service-export
    rules:
      - alert: ExportFailed
        expr: sum(increase(serverless_export_failed_number{namespace=~".*-ms"}[5m])) by (cluster_id,export_id,reason) > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless cluster {{ $labels.cluster_id }} export {{ $labels.export_id }} failed with reason {{ $labels.reason }}.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-export.rules.yaml

      - alert: ExportRunningTooLong
        expr: |
          serverless_export_running_duration_seconds{namespace=~".*-ms"} > 3600
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless cluster {{ $labels.cluster_id }} export {{ $labels.export_id }} running more than 1h.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-export.rules.yaml

      - alert: ExportMeterFailed
        expr: |
          increase(serverless_export_meter_process_statistics_total{status="failed"}[5m]) > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless export {{ $labels.export }} meter failed with reason {{ $labels.reason }}.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-export.rules.yaml

      - alert: ExportHandleEventFailed
        expr: |
          increase(serverless_export_event_handle_total{status!="success"}[5m]) > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless export handle cluster {{ $labels.cluster_id }} {{ $labels.event_type }} failed. The event id is {{ $labels.event_id }}. The consumption of eventbus will retry for 5 times, please confirm the final result.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-export.rules.yaml

      - alert: ExportLargeData
        expr: |
          serverless_export_size{} > 102400
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Please pay attention to the Serverless export {{ $labels.export_id }} as it will export large data size {{ $value }} MB.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-export.rules.yaml


