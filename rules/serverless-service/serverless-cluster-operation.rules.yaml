groups:
- name: serverless-cluster-operations.rules
  rules:
  - alert: AwsServerlessTierInfraClusterCreationDurationTooLong
    annotations:
      message: The serverless cluster {{ $labels.tenant_name }}/{{ $labels.name }} creation time exceeds 10 minutes
    expr: |
      dbaas_tidb_cluster_wait_ready_duration_seconds{provider_type=~"aws-free-tier|alicloud-serverless"} > 10 * 60
    labels:
      tier: serverless
      provider_type: aws-free-tier
      severity: warning
      component: tidb-cluster
