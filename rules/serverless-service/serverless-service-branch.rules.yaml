groups:
  - name: serverless-service-branch
    rules:
      - alert: BranchTooManyFailedRequests
        expr: |
          (sum(increase(serverless_branch_requested_total{namespace=~".*-ms", code=~"5.*"}[3m])) by (type,method)
            / sum(increase(serverless_branch_requested_total{namespace=~".*-ms"}[3m])) by (type,method)
          ) > 0.1
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless Branch {{ $labels.type }} {{ $labels.method }} has too may failed requests.

      - alert: BranchTooManySlowRequests
        expr: |
          histogram_quantile(0.99,
            sum(rate(serverless_branch_handling_seconds_bucket{namespace=~".*-ms"}[3m])) by (type, method, le)
          ) > 5
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: branch response latency P99 is {{ $value }}s, method={{ $labels.type }}/{{ $labels.method }}.

      # adjust the threshold after test
      - alert: ServerlessBranchReconcileError
        expr: |
          sum(increase(serverless_branch_reconcile_err_total{namespace=~".*-ms"}[1m])) by (method, resource_id) > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: serverless-svc-worker method {{ $labels.method }} error, branch {{ $labels.resource_id }}.

      # Limiter runs every 30 seconds
      - alert: ServerlessBranchLimiterError
        expr: |
          increase(serverless_branch_limit_process_statistics_total{namespace=~".*-ms", status="failed"}[5m]) > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          # provider and region conflicts with server-side labels, see prometheus honor_labels config
          message: "Serverless Branch limiter {{ $labels.exported_provider }}/{{ $labels.exported_region }} error: {{ $labels.reason }}."

      - alert: BranchWaitReadyTimeout
        expr: |
          sum(serverless_branch_wait_ready_duration_seconds{namespace=~".*-ms"}) by (branch_id) > 600
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless Branch {{ $labels.branch_id }} wait ready time out.

      - alert: BranchHandleClusterEventError
        expr: |
          sum(increase(serverless_branch_sync_process_statistics_total{namespace=~".*-ms", status="failed"}[5m])) by (event_id) >= 5
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless Branch handle cluster event {{ $labels.event_id }} error.

      - alert: BranchSvcManyFailedHttpRequests
        expr: |
          (sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-http-metrics",grpc_service!~"(serverless.ServerlessBillingService)",grpc_code!~"(OK|NotFound|InvalidArgument|AlreadyExists|Aborted|PermissionDenied|Unauthenticated)"}[5m])) by (grpc_service, grpc_method)
            / sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-http-metrics",grpc_service!~"(serverless.ServerlessBillingService)"}[5m])) by (grpc_service, grpc_method)
          ) > 0.05
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: HTTP requests failed many times({{ printf "%.2f" $value }}%), method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
      - alert: BranchSvcTooManyFailedHttpRequests
        # Exclude method ListDatabases because there are some unhandled but recoverable errors (e.g., accessDenied), and since the API does not raise critical warnings, the risk is relatively low.
        expr: |
          (sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-http-metrics",grpc_method!~"(ListDatabases)",grpc_service!~"(serverless.ServerlessBillingService)",grpc_code!~"(OK|NotFound|InvalidArgument|AlreadyExists|Aborted|PermissionDenied|Unauthenticated)"}[5m])) by (grpc_service, grpc_method)
            / sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-http-metrics",grpc_service!~"(serverless.ServerlessBillingService)"}[5m])) by (grpc_service, grpc_method)
          ) > 0.1
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: critical
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: HTTP requests failed too many times({{ printf "%.2f" $value }}%), method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
      - alert: BranchSvcManyFailedRpcRequests
        expr: |
          (sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_service!~"(serverless.ServerlessBillingService)",grpc_code!~"(OK|NotFound|InvalidArgument|AlreadyExists|Aborted|PermissionDenied|Unauthenticated)"}[5m])) by (grpc_service, grpc_method)
            / sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_service!~"(serverless.ServerlessBillingService)"}[5m])) by (grpc_service, grpc_method)
          ) > 0.05
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: RPC requests failed too many times({{ printf "%.2f" $value }}%), method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
      - alert: BranchSvcTooManyFailedRpcRequests
        expr: |
          (sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_service!~"(serverless.ServerlessBillingService)",grpc_code!~"(OK|NotFound|InvalidArgument|AlreadyExists|Aborted|PermissionDenied|Unauthenticated)"}[5m])) by (grpc_service, grpc_method)
            / sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_service!~"(serverless.ServerlessBillingService)"}[5m])) by (grpc_service, grpc_method)
          ) > 0.1
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: critical
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: RPC requests failed too many times({{ printf "%.2f" $value }}%), method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
