groups:
  - name: serverless-service-import
    rules:
      - alert: ImportStillSyncingAfterLongTime
        expr: |
          serverless_import_syncing_import_since_created_seconds > 3600 * 2 # 2 hours
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Import {{ $labels.import_id }} for cluster {{ $labels.cluster_id }} still syncing after 2 hours.

      - alert: ImportFail
        # 10278121821273448857 is the cluster_id of the e2e import aws cluster
        # 10033797696310464420 is the cluster_id of the e2e import alicloud cluster
        expr: |
          serverless_import_fail{msg!~"(?s)(?:AccessDenied|BucketRegionError|PreCheckFailed|SyntaxError|SchemaNotExists|UnknownColumns|FileNotExists|ClusterDeleted|CastValueError|SchemaDataMismatch).*", cluster_id!="10278121821273448857", cluster_id!="10748314250805687889"}
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message:  "Import {{ $labels.import_id }} for cluster {{ $labels.cluster_id }} failed: {{ $labels.msg }}."

      - alert: LargeImport
        expr: |
          serverless_import_size_with_index_bytes > 1024 * 1024 * 1024 * 100
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message:  "Large import detected: {{ $labels.import_id }} on cluster {{ $labels.cluster_id }} exceeds 100 GiB."
