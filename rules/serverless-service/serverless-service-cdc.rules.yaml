groups:
  - name: serverless-service-cdc
    rules:
      - alert: ChangefeedStateFailed
        expr: increase(serverless_cdc_state{state=~"running_failed|create_failed"}[5m]) > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless cluster {{ $labels.cluster_id }} changefeed {{ $labels.changefeed_id }} is in {{$labels.state}} state.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-cdc.rules.yaml
      
      - alert: ChangefeedStateWarning
        expr: serverless_cdc_state{state='warning'} > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless cluster {{ $labels.cluster_id }} changefeed {{ $labels.changefeed_id }} is in {{$labels.state}} state.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-cdc.rules.yaml

      - alert: ChangefeedStateMismatch
        expr: increase(serverless_cdc_state_mismatch_total{}[5m]) > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless cluster {{ $labels.cluster_id }} changefeed {{ $labels.changefeed_id }} state change mismatch, original state is {{$labels.db_state}}, target state is {{$labels.changefeed_state}}.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-cdc.rules.yaml

      - alert: ChangefeedCheckpointDelay
        expr: serverless_cdc_checkpoint_delay_seconds{} > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless cluster {{ $labels.cluster_id }} changefeed {{ $labels.changefeed_id }} checkpoint delay {{ $value }} seconds.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-cdc.rules.yaml

      - alert: ChangefeedCreatingTooLong
        expr: serverless_cdc_creating_duration_seconds{} > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: Serverless cluster {{ $labels.cluster_id }} changefeed {{ $labels.changefeed_id }} creating more than {{ $value }} seconds.
          runbook_url: https://github.com/tidbcloud/runbooks/blob/master/rules/serverless-service/serverless-service-cdc.rules.yaml

