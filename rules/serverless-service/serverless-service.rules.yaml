groups:
  - name: serverless-service
    rules:
      - alert: ServerlessSvcNoAvailablePods
        expr: sum(kube_deployment_status_replicas_available{namespace=~".*-ms",deployment=~"serverless-svc-.*"}) by(deployment) == 0
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: critical
          component: serverless-svc
          provider_type: aws-free-tier
        annotations:
          message: There are no running pods of deployment {{ $labels.deployment }}.
      - alert: ServerlessSvcPodRestart
        expr: sum(increase(kube_pod_container_status_restarts_total{namespace=~".*-ms",pod=~"serverless-svc-.*"}[1m])) by(pod) > 0
        labels:
          tier: TiDB Serverless
          severity: warning
          component: serverless-svc
          provider_type: aws-free-tier
        annotations:
          message: The pod {{ $labels.pod }} has restarted.
      - alert: ServerlessSvcManyFailedHttpRequests
        expr: |
          (sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-http-metrics",grpc_service=~"(serverless.ServerlessBillingService)",grpc_code!~"(OK|NotFound|InvalidArgument)"}[5m])) by (grpc_service, grpc_method)
            / sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-http-metrics",grpc_service=~"(serverless.ServerlessBillingService)"}[5m])) by (grpc_service, grpc_method)
          ) > 0.1
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: serverless-svc
          provider_type: aws-free-tier
        annotations:
          message: HTTP requests failed many times({{ printf "%.2f" $value }}), method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
      - alert: ServerlessSvcTooManyFailedHttpRequests
        expr: |
          (sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-http-metrics",grpc_service=~"(serverless.ServerlessBillingService)",grpc_code!~"(OK|NotFound|InvalidArgument)"}[5m])) by (grpc_service, grpc_method)
            / sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-http-metrics",grpc_service=~"(serverless.ServerlessBillingService)"}[5m])) by (grpc_service, grpc_method)
          ) > 0.5
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: critical
          component: serverless-svc
          provider_type: aws-free-tier
        annotations:
          message: HTTP requests failed too many times({{ printf "%.2f" $value }}), method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
      - alert: ServerlessSvcManyFailedRpcRequests
        expr: |
          (sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_service=~"(serverless.ServerlessBillingService)",grpc_code!~"(OK|NotFound|InvalidArgument)"}[5m])) by (grpc_service, grpc_method)
            / sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_service=~"(serverless.ServerlessBillingService)"}[5m])) by (grpc_service, grpc_method)
          ) > 0.1
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: serverless-svc
          provider_type: aws-free-tier
        annotations:
          message: RPC requests failed too many times({{ printf "%.2f" $value }}), method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
      - alert: ServerlessSvcTooManyFailedRpcRequests
        expr: |
          (sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_service=~"(serverless.ServerlessBillingService)",grpc_code!~"(OK|NotFound|InvalidArgument)"}[5m])) by (grpc_service, grpc_method)
            / sum(increase(grpc_server_handled_total{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_service=~"(serverless.ServerlessBillingService)"}[5m])) by (grpc_service, grpc_method)
          ) > 0.5
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: serverless-svc
          provider_type: aws-free-tier
        annotations:
          message: RPC requests failed too many times({{ printf "%.2f" $value }}), method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
      - alert: ServerlessSvcTooManySlowRpcRequests
        expr: |
          histogram_quantile(0.99,
            sum(rate(grpc_server_handling_seconds_bucket{namespace=~".*-ms",service=~"serverless-svc-grpc-metrics",grpc_type="unary"}[5m])) by (grpc_service, grpc_method, le)
          ) > 5
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: serverless-svc
          provider_type: aws-free-tier
        annotations:
          message: RPC response latency P99 is {{ $value }}s, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
      - alert: ServerlessSvcDBQueryFailed
        expr: |
          sum(increase(serverless_gorm_errors_total{namespace=~".*-ms",error!="record not found"}[1m])) by (table, error) > 0
        for: 5m
        labels:
          tier: TiDB Serverless
          severity: warning
          component: serverless-svc
          provider_type: aws-free-tier
        annotations:
          message: DB query failed, table={{ $labels.table }}, error={{ $labels.error }}.
