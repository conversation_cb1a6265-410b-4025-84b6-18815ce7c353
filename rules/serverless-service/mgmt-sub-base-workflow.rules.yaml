groups:
  - name: subscribe.mgmt.workflow
    rules:
      - alert: WorkflowRunningTooLong
        annotations:
          message: tidb-management-service, workflow dag instance {{ $labels.dag_ins_id }} of {{ $labels.job }}/{{ $labels.workflow_dag }} for project {{ $labels.project_id }} on region {{ $labels.target_region }} is running too long.
        expr: |
          avg by (tenant_id,project_id,target_provider,target_region,biz_id,workflow_dag,job,dag_ins_id) (workflow_info{namespace=~"tidb-management-service",workflow_dag="mgmt_serverless_create_cluster"}) > 2400
        for: 1m
        labels:
          severity: major
          component: serverless-svc
          server: tidb-management-service
          provider_type: aws-free-tier
      - alert: ScalingWorkflowRunningTooLong
        annotations:
          message: Workflow dag instance {{ $labels.dag_ins_id }} of {{ $labels.job }}/{{ $labels.workflow_dag }} for project {{ $labels.project_id }} on region {{ $labels.target_region }} is running too long.
        expr: |
          avg by (tenant_id,project_id,target_provider,target_region,biz_id,workflow_dag,job, dag_ins_id) (workflow_info{namespace=~"tidb-management-service",job="serverless-global-service",workflow_dag=~"dynamic_dag__global_scale.*"}) > 10800
        for: 1m
        labels:
          severity: major
          component: serverless-svc
          provider_type: aws-free-tier
          server: tidb-management-service
      - alert: RetryWorkflowInstanceFailed
        annotations:
          message: Workflow dag instance {{ $labels.dag_ins_id }} of {{ $labels.job }}/{{ $labels.workflow_dag }} for project {{ $labels.project_id }} on region {{ $labels.target_region }} failed to retry.
        expr: |
          increase(workflow_watchdog_retried_dag_instance_total{namespace=~"tidb-management-service",job="serverless-global-service",retry_command_error="true"}[1m]) > 10
        for: 1m
        labels:
          severity: major
          component: serverless-svc
          provider_type: aws-free-tier
          server: tidb-management-service
