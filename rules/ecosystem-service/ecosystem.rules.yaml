groups:
  - name: ecosystem
    rules:
      # ecosystem server alert
      - alert: ecosystem_serverDown
        annotations:
          message: "ecosystem server has been down for more than 5 minutes.\n LABELS = {{ $labels }}."
        expr: |
          up{job=~"ecosystem.*",namespace=~".*-ms"} == 0
        for: 5m
        labels:
          severity: critical
          component: ecosystem-server
          provider_type: aws-free-tier

      #  error_code_5xx_alert warning
      - alert: ecosystem_errorCode_5xx
        annotations:
          message: "ecosystem server has 5** code in 2 minutes.\n method= {{ $labels.url }} {{ $labels.method }}."
        expr: |
          sum(increase (ecosystem_service_http_request_total{code=~"5.*",namespace=~".*-ms"}[2m])) by (url,method) > 0
        for: 2m
        labels:
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier

      - alert: ecosystem_handle_event_error
        expr: |
          sum(increase(ecosystem_service_event_handler_process_statistics_total{namespace=~".*-ms", status="failed"}[5m])) by (event_id) >= 5
        labels:
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          message: ecosystem service handle event {{ $labels.event_id }} error.
