groups:
- name: SLOsForDataApps
  rules:
  - alert: SLOSqlEditorHttpApiAvailabilityBreak
    annotations:
      message: SLO 'SQL Editor HTTP API Availability' Break
    expr: |
      sum(rate(kong_custom_http_status{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*", code!~"5.*"}[10m]))
      /
      sum(rate(kong_custom_http_status{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m]))
      < 0.99
    for: 5m
    labels:
      severity: major
      component: dataservice
  - alert: SLOSqlEditorHttpApiAvailabilityBreakRisk
    annotations:
      message: SLO 'SQL Editor HTTP API Availability' Break Risk
    expr: |
      sum(rate(kong_custom_http_status{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*", code!~"5.*"}[10m]))
      /
      sum(rate(kong_custom_http_status{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m]))
      < 0.995
    for: 5m
    labels:
      severity: major
      component: dataservice
  - alert: SLOSqlEditorHttpApiLatencyP99Break
    annotations:
      message: SLO 'SQL Editor HTTP API Latency P99' Break
    expr: |
      (histogram_quantile(0.99, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m])) by (le)) > 30000)
        and
      (sum(increase(kong_custom_request_latency_ms_count{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m])) > 10)
    for: 5m
    labels:
      severity: major
      component: dataservice
  - alert: SLOSqlEditorHttpApiLatencyP99BreakRisk
    annotations:
      message: SLO 'SQL Editor HTTP API Latency P99' Break Risk
    expr: |
      (histogram_quantile(0.99, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m])) by (le)) > 20000)
        and
      (sum(increase(kong_custom_request_latency_ms_count{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m])) > 10)
    for: 5m
    labels:
      severity: major
      component: dataservice
  - alert: SLOSqlEditorHttpApiLatencyP90Break
    annotations:
      message: SLO 'SQL Editor HTTP API Latency P90' Break
    expr: |
      (histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m])) by (le)) > 5000)
        and
      (sum(increase(kong_custom_request_latency_ms_count{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m])) > 15)
    for: 5m
    labels:
      severity: major
      component: dataservice
  - alert: SLOSqlEditorHttpApiLatencyP90BreakRisk
    annotations:
      message: SLO 'SQL Editor HTTP API Latency P90' Break Risk
    expr: |
      (histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m])) by (le)) > 3000)
        and
      (sum(increase(kong_custom_request_latency_ms_count{kube_service=~"(nightly-ms|staging-ms|prod-ms).sqleditor-http.*"}[10m])) > 15)
    for: 5m
    labels:
      severity: major
      component: dataservice
