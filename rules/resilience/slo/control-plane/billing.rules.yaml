groups:
- name: SLOsForBilling
  rules:
  - alert: SLOBillingHttpApiAvailabilityBreakRisk
    annotations:
      message: SLO 'Billing HTTP API Availability' Break Risk
    expr: |
      sum(rate(billing2_requests_total{service="billing2",code!~"5.*"}[10m]))
      /
      sum(rate(billing2_requests_total{service="billing2"}[10m]))
      < 0.999
    for: 5m
    labels:
      severity: major
      component: billing-server
  - alert: SLOBillingHttpApiLatencyP99BreakRisk
    annotations:
      message: |
        SLO 'Billing HTTP API Latency P99' Break Risk, Runbook: https://clinic.pingcap.com/grafana/d/QIw-n_l4k/slo-global-control-plane?orgId=1
    expr: |
      (
        1-(sum(rate(billing2_request_duration_seconds_bucket{le="1"}[1h]))
        /
        sum(rate(billing2_request_duration_seconds_count{}[1h]))) 
        > 16.8*0.01
      ) and (
        1-(sum(rate(billing2_request_duration_seconds_bucket{le="1"}[5m]))
        /
        sum(rate(billing2_request_duration_seconds_count{}[5m])))
        > 16.8*0.01
      )
    labels:
      severity: major
      component: billing-server
  - alert: SLOBillingHttpApiLatencyP90BreakRisk
    annotations:
      message: |
        SLO 'Billing HTTP API Latency P90' Break Risk, Runbook: https://clinic.pingcap.com/grafana/d/QIw-n_l4k/slo-global-control-plane?orgId=1
    expr: |
      (
        1-(sum(rate(billing2_request_duration_seconds_bucket{le="0.5"}[1h]))
        /
        sum(rate(billing2_request_duration_seconds_count{}[1h]))) 
        > 2.8*0.1
      ) and (
        1-(sum(rate(billing2_request_duration_seconds_bucket{le="0.5"}[5m]))
        /
        sum(rate(billing2_request_duration_seconds_count{}[5m])))
        > 2.8*0.1
      )
    labels:
      severity: major
      component: billing-server
  - alert: SLOBillingOfficialWebsiteUserExperienceApiAvailabilityBreakRisk
    annotations:
      message: SLO 'Billing OfficialWebsiteUserExperience API Availability' Break Risk
    expr: |
      sum(rate(billing2_requests_total{service="billing2",code!~"5.*",url=~"(.*)month||(.*)export||(.*)dryrun||(.*)payment_methods||(.*)invoices||(.*)setup_intent"}[10m]))
      /
      sum(rate(billing2_requests_total{service="billing2",url=~"(.*)month||(.*)export||(.*)dryrun||(.*)payment_methods||(.*)invoices||(.*)setup_intent"}[10m]))
      < 0.99995
    for: 5m
    labels:
      severity: major
      component: billing-server