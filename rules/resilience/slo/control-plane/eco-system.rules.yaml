groups:
- name: SLOsForEcoSystem
  rules:
  - alert: SLOEcoSystemHttpApiAvailabilityBreak
    annotations:
      message: SLO 'EcoSystem HTTP API Availability' Break
    expr: |
      sum(rate(kong_custom_http_status{kube_service=~"(nightly-ms|staging-ms|prod-ms).ecosystem-service-http.*", code!~"5.*"}[10m])) 
      /
      sum(rate(kong_custom_http_status{kube_service=~"(nightly-ms|staging-ms|prod-ms).ecosystem-service-http.*"}[10m]))
      < 0.999
    for: 5m
    labels:
      severity: major
      component: ecosystem-server
  - alert: SLOEcoSystemHttpApiAvailabilityBreakRisk
    annotations:
      message: SLO 'EcoSystem HTTP API Availability' Break Risk
    expr: |
      sum(rate(kong_custom_http_status{kube_service=~"(nightly-ms|staging-ms|prod-ms).ecosystem-service-http.*", code!~"5.*"}[10m]))
      /
      sum(rate(kong_custom_http_status{kube_service=~"(nightly-ms|staging-ms|prod-ms).ecosystem-service-http.*"}[10m]))
      < 0.9995
    for: 5m
    labels:
      severity: major
      component: ecosystem-server
  - alert: SLOEcoSystemHttpApiLatencyP99Break
    annotations:
      message: SLO 'EcoSystem HTTP API Latency P99' Break
    expr: |
      histogram_quantile(0.99, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~"(nightly-ms|staging-ms|prod-ms).ecosystem-service-http.*"}[10m])) by (le)) > 60000
    for: 5m
    labels:
      severity: major
      component: ecosystem-server
  - alert: SLOEcoSystemHttpApiLatencyP99BreakRisk
    annotations:
      message: SLO 'EcoSystem HTTP API Latency P99' Break Risk
    expr: |
      histogram_quantile(0.99, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~"(nightly-ms|staging-ms|prod-ms).ecosystem-service-http.*"}[10m])) by (le)) > 54000
    for: 5m
    labels:
      severity: major
      component: ecosystem-server
  - alert: SLOEcoSystemHttpApiLatencyP90Break
    annotations:
      message: SLO 'EcoSystem HTTP API Latency P90' Break
    expr: |
      histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~"(nightly-ms|staging-ms|prod-ms).ecosystem-service-http.*"}[10m])) by (le)) > 30000
    for: 5m
    labels:
      severity: major
      component: ecosystem-server
  - alert: SLOEcoSystemHttpApiLatencyP90BreakRisk
    annotations:
      message: SLO 'EcoSystem HTTP API Latency P90' Break Risk
    expr: |
      histogram_quantile(0.90, sum(rate(kong_custom_request_latency_ms_bucket{kube_service=~"(nightly-ms|staging-ms|prod-ms).ecosystem-service-http.*"}[10m])) by (le)) > 27000
    for: 5m
    labels:
      severity: major
      component: ecosystem-server