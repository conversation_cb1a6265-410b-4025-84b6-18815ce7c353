groups: 
  - name: clinic.importer
    rules:
      - alert: ImporterJobFailed
        expr: kube_job_status_failed{namespace="clinic", job_name=~"importer-.*"} > 0
        for: 5m
        annotations:
          summary: importer job failed (job {{ $labels.job_name }})
        labels:
          severity: critical
          component: clinic
      - alert: ImporterPodNotHealthy
        expr: min_over_time(sum by (pod) (kube_pod_status_phase{namespace="clinic", pod=~"importer-.*", phase=~"Pending|Unknown|Failed"})[3m:1m]) > 0
        for: 5m
        annotations:
          summary: importer job's pod not healthy (pod {{ $labels.pod }})
        labels:
          severity: critical
          component: clinic