groups: 
  - name: clinic.victoriametrics
    rules:
      # - alert: VmScrapeJobsMissing
      #   expr: sum (up{job=~"vminsert|vmselect|vmstorage"}) < 3
      #   for: 5m
      #   annotations:
      #     description: |-
      #       vm scrape jobs has disappeared
      #       VALUE = {{ $value }}
      #       LABELS = {{ $labels }}
      #     summary: some vm scrape jobs are missing (instance {{ $labels.instance }})
      #     expr: sum (up{job=~"vminsert|vmselect|vmstorage"}) < 3
      #     for: 5m
      #   labels:
      #     severity: major
      #     component: clinic
      # - alert: VmStorageDiskSpaceLow
      #   expr: min(vm_free_disk_space_bytes{job="vmstorage"}) / (1024*1024*1024) < 5
      #   for: 5m
      #   annotations:
      #     description: |-
      #       vmstoarge instance free disk space less than 10GB
      #   labels:
      #     severity: critical
      #     component: clinic
      # - alert: VmClusterTooManyErrorRequests
      #   expr: sum(rate(vm_http_request_errors_total{job=~"vminsert|vmselect|vmstorage"}[1m])) by (path) > 0
      #   for: 5m
      #   annotations:
      #     description: |-
      #       vm cluster has too many error requests
      #       VALUE = {{ $value }}
      #       LABELS = {{ $labels }}
      #     summary: vm cluster has too many error requests (path {{ $labels.path }})
      #     expr: sum(rate(vm_http_request_errors_total{job=~"vminsert|vmselect|vmstorage"}[1m])) by (path) > 0
      #     for: 5m
      #   labels:
      #     severity: critical
      #     component: clinic
      - alert: VmInsertHasSlowInsert
        expr: sum(rate(vm_slow_row_inserts_total{job="vmstorage"}[1m])) / sum(rate(vm_rows_inserted_total{job="vminsert"}[1m])) > 0.1
        for: 5m
        annotations:
          summary: vminsert has slow inserts
        labels:
          severity: major
          component: clinic
      # - alert: VmSelectSlowQuery
      #   expr: sum(rate(vm_slow_queries_total{job="vmselect"}[1m])) > 5
      #   for: 5m
      #   annotations:
      #     description: |-
      #       vmselect has slow queries
      #       VALUE = {{ $value }}
      #       LABELS = {{ $labels }}
      #     summary: vmselect has slow queries
      #     expr: sum(rate(vm_slow_queries_total{job="vmselect"}[1m])) > 5
      #     for: 3m
      #   labels:
      #     severity: critical
      #     component: clinic