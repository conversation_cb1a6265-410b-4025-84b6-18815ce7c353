groups:
- name: TiDBCloudIncident
  rules:
  - alert: IncidentChangefeedHighDelay
    expr: ticdc_owner_checkpoint_ts_lag{} > 8*60*60
    for: 1m
    labels:
      severity: major
      component: resilience
    annotations:
      message: |
        The Changefeed {{ $labels.changefeed }} of cluster {{ $labels.cluster_id }} checkpoint delay is more than 8 hours.
  - alert: PerformanceDegradeRisk
    expr: |
      (
        avg_over_time(sum by (cluster_id) (rate(tidb_executor_statement_total{tenant!~".*1372813089209061633"}[1m]))[5m:])
        < bool on (cluster_id) 0.2 * avg_over_time(
            sum by (cluster_id) (rate(tidb_executor_statement_total{tenant!~".*1372813089209061633"}[1m]))[1d:]
        )
      ) + on (cluster_id) (
        avg_over_time(histogram_quantile(0.99, sum by (cluster_id, le) (rate(tidb_server_handle_query_duration_seconds_bucket{tenant!~".*1372813089209061633"}[1m])))[5m:])
          > bool on (cluster_id) 100 * 
              histogram_quantile(0.99, sum by (cluster_id, le) (rate(tidb_server_handle_query_duration_seconds_bucket{tenant!~".*1372813089209061633"}[1d])))
      ) + on (cluster_id) (
        (
          avg_over_time(sum by (cluster_id) (increase(tidb_server_execute_error_total{type=~".*8027|.*8120|.*8021|.*8024|.*9001|.*9002|.*9003|.*9011|.*9012|.*9013",tenant!~".*1372813089209061633"}[1m]))[5m:]) 
          > bool 1
        ) or vector(0)
      ) >= 2
    labels:
      severity: major
      component: resilience
    annotations:
      message: |
         The TiDB cluster {{ $labels.cluster_id }} has performance degrade risk. This indicates that TiDB's QPS has dropped by at least 80%, and the p99 latency has increased by at least 100 times.
  - alert: IncidentClusterOfflineStoreProgressPending
    annotations:
      message: The TiDB Cluster {{ $labels.cluster_id }} offline store progress pending for more than 1 hour
    expr: |
      increase(pd_cluster_progress{action="removing"}[2m]) == 0
    for: 1h
    labels:
      severity: major
      component: resilience
  - alert: CertikTikvDiskReadIopsHigh
    expr: |
      (
        rate(node_disk_reads_completed_total[2m]) 
        and on(instance) 
        sum by(instance) (label_replace(kube_pod_info{namespace=~".*1379661944636804070", pod=~"db-tikv.*"}, "instance", "$1", "node", "(.+)"))
      ) > 3500
    for: 5m
    labels:
      severity: major
      component: resilience
    annotations:
      message: |
        The TiKV on {{ $labels.instance }} disk Read IOPS is too high.
  - alert: CertikTikvDiskWriteIopsHigh
    expr: |
      (
        rate(node_disk_writes_completed_total[2m]) 
        and on(instance) 
        sum by(instance) (label_replace(kube_pod_info{namespace=~".*1379661944636804070", pod=~"db-tikv.*"}, "instance", "$1", "node", "(.+)"))
      ) > 3500
    for: 5m
    labels:
      severity: major
      component: resilience
    annotations:
      message: |
        The TiKV on {{ $labels.instance }} disk Write IOPS is too high.
  - alert: TiflashMaxSnapshotLifetimeReachingLong
    expr: tiflash_system_asynchronous_metric_MaxDTDeltaOldestSnapshotLifetime > 18000
    for: 4h
    labels:
      severity: major
      tier: dedicated
      component: resilience
    annotations:
      description: The corresponding SQL query involves fetching a large volume of data with time spent on 'send response time'. Doubt to be a potential issue with client-side response handling the result.
      summary: "Long Running Tiflash Job"
  - alert: PerformanceDegradeRiskForWetech
    expr: |
      (
        sum by (cluster_id) (rate(tidb_executor_statement_total{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[2m]))
        < bool on (cluster_id) 0.4 * avg_over_time(
          sum by (cluster_id) (rate(tidb_executor_statement_total{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[2m]))[1h:]
        )
      ) + on (cluster_id) (
        histogram_quantile(0.99, sum by (cluster_id, le) (rate(tidb_server_handle_query_duration_seconds_bucket{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[2m])))
        > bool on (cluster_id) 5 * 
          histogram_quantile(0.99, sum by (cluster_id, le) (rate(tidb_server_handle_query_duration_seconds_bucket{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[1h])))
      ) >= 2
    labels:
      severity: major
      component: resilience
    annotations:
      message: |
        The TiDB cluster {{ $labels.cluster_id }} has performance degrade risk. This indicates that TiDB's QPS has dropped by at least 50%, and the p99 latency has increased by at least 5 times.
  - alert: PerformanceJitterRiskForWetech
    expr: |
      (
        sum by (cluster_id) (rate(tidb_server_tokens{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[2m]))
        > bool on (cluster_id) 3 * avg_over_time(
          sum by (cluster_id) (rate(tidb_server_tokens{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[2m]))[1h:]
        )
      ) + on (cluster_id) (
        histogram_quantile(0.999, sum by (cluster_id, le) (rate(tidb_server_handle_query_duration_seconds_bucket{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[2m])))
        > bool on (cluster_id) 5 * 
          histogram_quantile(0.999, sum by (cluster_id, le) (rate(tidb_server_handle_query_duration_seconds_bucket{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[1h])))
      ) + on(cluster_id) (
        max_over_time(sum by(cluster_id) (tidb_server_tokens{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"})[2m:]) > bool 5*60
      ) >= 3
    labels:
      severity: major
      component: resilience
    annotations:
      message: |
        The TiDB cluster {{ $labels.cluster_id }} has performance jitter risk. This indicates that the TiDB's database time has increased by at least 3 times, and the p999 latency has increased by at least 5 times. It could be caused by fluctuations in the underlying EBS. For more details, please refer to `Performance Overview` Dashboard.
  - alert: TiFlashPerformanceJitterRiskForWetech
    expr: |
      (
        histogram_quantile(0.999, sum by (cluster_id, le) (rate(tiflash_coprocessor_request_duration_seconds_bucket{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[2m])))
        > bool on (cluster_id) 5 * 
          histogram_quantile(0.999, sum by (cluster_id, le) (rate(tiflash_coprocessor_request_duration_seconds_bucket{tenant=~".*1372813089209061633",cluster_id!~".*1379661944646413137|.*1379661944646413136|.*1379661944646413872|.*1379661944646414020|.*1379661944646414023|.*1379661944646414008|.*1379661944646414005|.*1379661944646414021|.*1379661944646414022|.*1379661944646414018|.*1379661944646414004"}[1h])))
      ) >= 1
    labels:
      severity: warning
      component: resilience
    annotations:
      message: |
        The TiDB cluster {{ $labels.cluster_id }} has TiFlash performance jitter risk. This indicates that the p999 request duration of TiFlash has increased by at least 5 times. For more details, please refer to `Performance Overview` Dashboard.
  - alert: NetworkLagIncreaseForWetech
    expr: |
      sum(rate(tidb_tikvclient_rpc_net_latency_seconds_sum{tenant="1372813089209061633", scope="false"}[2m])) by (tenant, cluster_id, le, store) 
      / sum(rate(tidb_tikvclient_rpc_net_latency_seconds_count{tenant="1372813089209061633", scope="false"}[2m])) by (tenant, cluster_id, le, store)
      > 0.005
    for: 30s
    labels:
      severity: major
      component: resilience
    annotations:
      message: |
        The network duration of store-{{ $labels.store }} in TiDB cluster {{ $labels.cluster_id }} is over 5ms.

#  alert: WetechQPSRaised move to rules/dedicated/swat/data-plane/kernel-tcocp.yaml

