groups:
- name: ingress
  rules:
  - alert: IngressDown
    annotations:
      message: Nginx ingress controller has disappeared from Prometheus target discovery.
    expr: |
      up{job=~"nginx-ingress.*"} == 0
    for: 10m
    labels:
      component: nginx-ingress
      severity: critical
  - alert: IngressSuccessRateIsEqualToZero
    annotations:
      message: Ingress success rate is equal to 0. Maybe backend service down.
    expr: |
      sum(rate(nginx_ingress_controller_requests{status=~"5.*"}[2m])) by (ingress) / sum(rate(nginx_ingress_controller_requests{}[2m])) by (ingress)==1
    for: 10m
    labels:
      component: nginx-ingress
      severity: critical
