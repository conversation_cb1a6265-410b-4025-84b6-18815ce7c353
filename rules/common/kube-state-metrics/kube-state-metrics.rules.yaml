groups:
- name: kube-state-metrics
  rules:
  - alert: KubeStateMetricsListErrors
    annotations:
      message: kube-state-metrics is experiencing errors at an elevated rate in
        list operations. This is likely causing it to not be able to expose metrics
        about Kubernetes objects correctly or at all.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubestatemetricslisterrors
    expr: |
      (sum(rate(kube_state_metrics_list_total{job="kube-state-metrics",result="error"}[5m]))
        /
      sum(rate(kube_state_metrics_list_total{job="kube-state-metrics"}[5m])))
      > 0.01
    for: 15m
    labels:
      component: kubernetes
      severity: major
  - alert: KubeStateMetricsWatchErrors
    annotations:
      message: kube-state-metrics is experiencing errors at an elevated rate in
        watch operations. This is likely causing it to not be able to expose metrics
        about Kubernetes objects correctly or at all.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubestatemetricswatcherrors
    expr: |
      (sum(rate(kube_state_metrics_watch_total{job="kube-state-metrics",result="error"}[5m]))
        /
      sum(rate(kube_state_metrics_watch_total{job="kube-state-metrics"}[5m])))
      > 0.01
    for: 15m
    labels:
      component: kubernetes
      severity: major
  - alert: KubeStateMetricsDown
    expr: up{job="kube-state-metrics"} == 0
    for: 15m
    labels:
      component: kubernetes
      service: kube-state-metrics
      severity: critical
    annotations:
      description: kube-state-metrics is down
