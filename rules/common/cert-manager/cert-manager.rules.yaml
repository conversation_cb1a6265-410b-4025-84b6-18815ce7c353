groups:
- name: cert-manager
  rules:
    - alert: CertManagerDown
      annotations:
        description: New certificates will not be able to be minted, and existing ones can't
          be renewed until cert-manager is back.
        runbook_url: https://gitlab.com/uneeq-oss/cert-manager-mixin/-/blob/master/RUNBOOK.md#certmanagerabsent
        summary: There are no running cert-manager pods for the past last 10 minutes.
      expr: sum(up{job="cert-manager"} )by(cluster) == 0
      for: 10m
      labels:
        severity: critical
        component: cert-manager
    - alert: CertManagerCertExpirySoon
      annotations:
        description: The domain that this cert covers will be unavailable after {{ $value
          | humanizeDuration }}. Clients using endpoints that this cert protects will start
          to fail in {{ $value | humanizeDuration }}.
        runbook_url: https://gitlab.com/uneeq-oss/cert-manager-mixin/-/blob/master/RUNBOOK.md#certmanagercertexpirysoon
        message: The cert {{ $labels.exported_namespace }}/{{ $labels.name }} is {{ $value | humanizeDuration }} from expiry.
      expr: |
          certmanager_certificate_expiration_timestamp_seconds{exported_namespace!~"mimir-.*"} - time() < (15 * 24 * 3600) # 15 days
      for: 10m
      labels:
        severity: warning
        component: cert-manager
    - alert: CertManagerCertExpirySoon
      annotations:
        description: The domain that this cert covers will be unavailable after {{ $value
          | humanizeDuration }}. Clients using endpoints that this cert protects will start
          to fail in {{ $value | humanizeDuration }}.
        runbook_url: https://gitlab.com/uneeq-oss/cert-manager-mixin/-/blob/master/RUNBOOK.md#certmanagercertexpirysoon
        message: The cert {{ $labels.exported_namespace }}/{{ $labels.name }} is {{ $value | humanizeDuration }} from expiry.
      expr: |
          certmanager_certificate_expiration_timestamp_seconds{exported_namespace!~"mimir-.*"} - time() < (7 * 24 * 3600) # 7 days
      for: 10m
      labels:
        severity: critical
        component: cert-manager
    - alert: CertManagerCertNotReady
      annotations:
        description: This certificate has not been ready to serve traffic for at least 10m.
          If the cert is being renewed or there is another valid cert, the ingress controller
          _may_ be able to serve that instead.
        runbook_url: https://gitlab.com/uneeq-oss/cert-manager-mixin/-/blob/master/RUNBOOK.md#certmanagercertnotready
        summary: The cert {{ $labels.name }} is not ready to serve traffic.
      expr: |
          certmanager_certificate_ready_status{condition!="True"} == 1
      for: 10m
      labels:
        severity: critical
        component: cert-manager
    - alert: CertManagerHittingRateLimits
      annotations:
        description: Depending on the rate limit, cert-manager may be unable to generate
          certificates for up to a week.
        runbook_url: https://gitlab.com/uneeq-oss/cert-manager-mixin/-/blob/master/RUNBOOK.md#certmanagerhittingratelimits
        summary: Cert manager hitting LetsEncrypt rate limits.
      expr: |
        sum by (host) (
          rate(certmanager_http_acme_client_request_count{status="429"}[2m])
        ) > 0
      for: 5m
      labels:
        severity: critical
        component: cert-manager
