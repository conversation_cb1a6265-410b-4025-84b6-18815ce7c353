groups:
- name: calico.rules
  rules:
  - alert: CalicoTyphaNoAvailableReplicas
    expr: kube_deployment_status_replicas_available{deployment="calico-typha-deploy"}==0
    for: 20m
    labels:
      service: calico-typha
      severity: critical
      type: shoot
      component: network
    annotations:
      description: There are no running calico-typha pods.
  - alert: CalicoKubeControllersNoAvailableReplicas
    expr: kube_deployment_status_replicas_available{deployment="calico-kube-controllers"}==0
    for: 20m
    labels:
      service: calico-kube-controllers
      severity:  critical
      type: shoot
      component: network
    annotations:
      description: There are no running calico-kube-controllers pods.
