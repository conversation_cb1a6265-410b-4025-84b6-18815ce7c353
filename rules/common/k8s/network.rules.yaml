groups:
- name: node-network
  rules:
  - alert: NodeNetworkInterfaceFlapping
    annotations:
      message: Network interface "{{ $labels.device }}" changing it's up status
        often on node-exporter {{ $labels.namespace }}/{{ $labels.pod }}"
    expr: |
      changes(node_network_up{job="node-exporter",device!~"veth.+"}[2m]) > 2
    for: 2m
    labels:
      component: kubernetes
      severity: warning
