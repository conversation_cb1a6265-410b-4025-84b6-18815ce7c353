groups:
- name: workload.rules
  rules:
  - expr: |
      sum(rate(container_cpu_usage_seconds_total{ image!="", container!="POD"}[5m])) by (namespace)
    record: namespace:container_cpu_usage_seconds_total:sum_rate
  - expr: |
      sum by (cluster, namespace, pod, container) (
        rate(container_cpu_usage_seconds_total{ image!="", container!="POD"}[5m])
      ) * on (cluster, namespace, pod) group_left(node) topk by (cluster, namespace, pod) (
        1, max by(cluster, namespace, pod, node) (kube_pod_info)
      )
    record: node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate
  - expr: |
      container_memory_working_set_bytes{ image!=""}
      * on (namespace, pod) group_left(node) topk by(namespace, pod) (1,
        max by(namespace, pod, node) (kube_pod_info)
      )
    record: node_namespace_pod_container:container_memory_working_set_bytes
  - expr: |
      container_memory_rss{ image!=""}
      * on (namespace, pod) group_left(node) topk by(namespace, pod) (1,
        max by(namespace, pod, node) (kube_pod_info)
      )
    record: node_namespace_pod_container:container_memory_rss
  - expr: |
      container_memory_cache{ image!=""}
      * on (namespace, pod) group_left(node) topk by(namespace, pod) (1,
        max by(namespace, pod, node) (kube_pod_info)
      )
    record: node_namespace_pod_container:container_memory_cache
  - expr: |
      container_memory_swap{ image!=""}
      * on (namespace, pod) group_left(node) topk by(namespace, pod) (1,
        max by(namespace, pod, node) (kube_pod_info)
      )
    record: node_namespace_pod_container:container_memory_swap
  - expr: |
      sum(container_memory_usage_bytes{ image!="", container!="POD"}) by (namespace)
    record: namespace:container_memory_usage_bytes:sum
  - expr: |
      sum by (namespace, resource) (
          sum by (namespace, pod, resource) (
              max by (namespace, pod, container, resource) (
                  kube_pod_container_resource_requests{job="kube-state-metrics"}
              ) * on(namespace, pod) group_left() max by (namespace, pod) (
                kube_pod_status_phase{phase=~"Pending|Running"} == 1
              )
          )
      )
    record: namespace:kube_pod_container_resource_requests:sum
  - expr: |
      max by (cluster, namespace, workload, pod) (
        label_replace(
          label_replace(
            kube_pod_owner{job="kube-state-metrics", owner_kind="ReplicaSet"},
            "replicaset", "$1", "owner_name", "(.*)"
          ) * on(replicaset, namespace) group_left(owner_name) topk by(replicaset, namespace) (
            1, max by (replicaset, namespace, owner_name) (
              kube_replicaset_owner{job="kube-state-metrics"}
            )
          ),
          "workload", "$1", "owner_name", "(.*)"
        )
      )
    labels:
      workload_type: deployment
    record: mixin_pod_workload
  - expr: |
      max by (cluster, namespace, workload, pod) (
        label_replace(
          kube_pod_owner{job="kube-state-metrics", owner_kind="DaemonSet"},
          "workload", "$1", "owner_name", "(.*)"
        )
      )
    labels:
      workload_type: daemonset
    record: mixin_pod_workload
  - expr: |
      max by (cluster, namespace, workload, pod) (
        label_replace(
          kube_pod_owner{job="kube-state-metrics", owner_kind="StatefulSet"},
          "workload", "$1", "owner_name", "(.*)"
        )
      )
    labels:
      workload_type: statefulset
    record: mixin_pod_workload
