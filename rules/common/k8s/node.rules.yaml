groups:
- name: node.rules
  rules:
  - expr: |
      sum(min(kube_pod_info) by (cluster, node))
    record: ':kube_pod_info_node_count:'
  - expr: |
      topk by(cluster, namespace, pod) (1,
        max by (cluster, node, namespace, pod) (
          label_replace(kube_pod_info{job="kube-state-metrics"}, "pod", "$1", "pod", "(.*)")
      ))
    record: 'node_namespace_pod:kube_pod_info:'
  - expr: |
      count by (cluster, node) (sum by (cluster, node, cpu) (
        node_cpu_seconds_total{job="node-exporter"}
      * on (namespace, pod) group_left(node)
        node_namespace_pod:kube_pod_info:
      ))
    record: node:node_num_cpu:sum
  - expr: |
      sum(
        node_memory_MemAvailable_bytes{job="node-exporter"} or
        (
          node_memory_Buffers_bytes{job="node-exporter"} +
          node_memory_Cached_bytes{job="node-exporter"} +
          node_memory_MemFree_bytes{job="node-exporter"} +
          node_memory_Slab_bytes{job="node-exporter"}
        )
      ) by (cluster)
    record: :node_memory_MemAvailable_bytes:sum
  - expr: |
      label_replace(
        node_memory_MemTotal_bytes{}, "node","$1","instance", "(.+)")
      * on (cluster, node) group_right() kube_node_labels
    record: :node_memory_MemTotal_bytes:kube_node_labels
  - expr: |
      label_replace(
        node_memory_MemAvailable_bytes{}, "node","$1","instance", "(.+)")
      * on (cluster, node) group_right() kube_node_labels
    record: :node_memory_MemAvailable_bytes:kube_node_labels
  - expr: |
      label_replace(
        avg by(cluster,instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])), "node","$1","instance", "(.+)")
      * on (cluster, node) group_right() kube_node_labels
    record: :node_cpu_idle:kube_node_labels