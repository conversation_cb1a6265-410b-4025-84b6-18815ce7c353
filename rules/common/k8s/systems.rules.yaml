groups:
- name: kubernetes-system
  rules:
  - alert: KubeClientErrors
    annotations:
      message: Kubernetes API server client '{{ $labels.job }}/{{ $labels.instance
        }}' is experiencing {{ $value | humanizePercentage }} errors.'
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubeclienterrors
    expr: |
      (sum(rate(rest_client_requests_total{code=~"5.."}[5m])) by (cluster,instance, job)
        /
      sum(rate(rest_client_requests_total[5m])) by (cluster,instance, job))
      > 0.01
    for: 15m
    labels:
      component: kubernetes
      severity: warning
