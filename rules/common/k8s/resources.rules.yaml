groups:
- name: kubernetes-resources
  rules:
  - alert: KubeCPUQuotaOvercommit
    annotations:
      message: Cluster has overcommitted CPU resource requests for Namespaces.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubecpuquotaovercommit
    expr: |
      sum(min without(resource) (kube_resourcequota{job="kube-state-metrics", type="hard", resource=~"(cpu|requests.cpu)"})) by (cluster)
        /
      sum(kube_node_status_allocatable{resource="cpu", job="kube-state-metrics"}) by (cluster)
        > 1.5
    for: 5m
    labels:
      component: kubernetes
      severity: warning
  # Deprecated in kube-state-metrics v2
  - alert: KubeMemoryQuotaOvercommit
    annotations:
      message: Cluster has overcommitted memory resource requests for Namespaces.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubememoryquotaovercommit
    expr: |
      sum(min without(resource) (kube_resourcequota{job="kube-state-metrics", type="hard", resource=~"(memory|requests.memory)"})) by (cluster)
        /
      sum(kube_node_status_allocatable{resource="memory", job="kube-state-metrics"}) by (cluster)
        > 1.5
    for: 5m
    labels:
      component: kubernetes
      severity: warning
  # - alert: KubeResourceQuotaOvercommit
  #   annotations:
  #     message: Cluster has overcommitted resource {{ $labels.resource }} requests for Namespaces.
  #     runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubememoryquotaovercommit
  #   expr: |
  #     sum(kube_resourcequota{job="kube-state-metrics", type="hard"}) by (resource)
  #       /
  #     sum(kube_node_status_allocatable) by (resource)
  #       > 1.5
  #   for: 5m
  #   labels:
  #     component: kubernetes
  #     severity: warning
  - alert: KubeQuotaExceeded
    annotations:
      message: Namespace {{ $labels.namespace }} is using {{ $value | humanizePercentage
        }} of its {{ $labels.resource }} quota.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubequotaexceeded
    expr: |
      kube_resourcequota{job="kube-state-metrics", type="used"}
        / ignoring(instance, job, type)
      (kube_resourcequota{job="kube-state-metrics", type="hard"} > 0)
        > 0.90
    for: 15m
    labels:
      component: kubernetes
      severity: warning
  # - alert: CPUThrottlingHigh
  #   annotations:
  #     message: '{{ $value | humanizePercentage }} throttling of CPU in namespace
  #       {{ $labels.namespace }} for container {{ $labels.container }} in pod {{
  #       $labels.pod }}.'
  #     runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-cputhrottlinghigh
  #   expr: |
  #     sum(increase(container_cpu_cfs_throttled_periods_total{container!~"grafana|node-exporter|auto-create-objects|fluent-bit|gardener-seed-admission-controller",container!="", }[5m])) by (container, pod, namespace)
  #       /
  #     sum(increase(container_cpu_cfs_periods_total{}[5m])) by (container, pod, namespace)
  #       > ( 25 / 100 )
  #   for: 15m
  #   labels:
  #     severity: warning
