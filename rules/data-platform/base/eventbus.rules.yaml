groups:
  - name: dataflow-eventbus
    rules:
      - alert: DataflowEventbusDLQ
        annotations:
          message: Event {{ $labels.event_type }} enter DLQ at Dataflow Eventbus.
        expr: |
          sum(increase(dataflow_eventbus_events_published_total{target="dlq" }[1m])) > 0
        for: 0m
        labels:
          severity: major
          component: dataflow-service
      - alert: DataflowEventbusPublishFailed
        annotations:
          message: Eventbus {{ $labels.event_type }} Publishing failed.
        expr: |
          sum(increase(dataflow_eventbus_events_published_total{success="false"}[1m])) > 0
        for: 0m
        labels:
          severity: major
          component: dataflow-service
