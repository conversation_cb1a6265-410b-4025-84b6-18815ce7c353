groups:
  - name: br-workflow
    rules:
      - alert: BRWorkflowDagInstanceRunFailed
        annotations:
          message: The Workflow {{ $labels.dag_ins_id }} is Failed. business_type is {{ $labels.business_type }}, business_action is {{ $labels.business_action }}, business_id is {{ $labels.business_id }}
        expr: |
          fastflow_executor_dag_ins_failed{worker_key=~"dataflow-workflow.*",business_type=~"bucket|backup_setting|snapshot_backup|pit_backup|log_backup|restore|backup_export"} > 0
        for: 0m
        labels:
          severity: major
          component: br
