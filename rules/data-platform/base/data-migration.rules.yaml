groups:
- name: data-migration
  rules:
  - alert: DMJobInCreatingPhaseForTooLong
    annotations:
      message: "DM job {{ $labels.job_id }} for cluster {{ $labels.cluster_id }} being in creating phase for too long."
    expr: |
      dataflow_dm_job_duration_in_phase{phase="creating"} > 15*60 # 15 minutes. in most case the job will be created in less than 5 minutes
    labels:
      severity: critical
      component: data-migration
  - alert: DMJobInDeletingPhaseForTooLong
    annotations:
      message: "DM job {{ $labels.job_id }} for cluster {{ $labels.cluster_id }} being in deleting phase for too long."
    expr: |
      dataflow_dm_job_duration_in_phase{phase="deleting"} > 30*60 # 30 minutes.
    labels:
      severity: critical
      component: data-migration
  - alert: DMJobInModifyingPhaseForTooLong
    annotations:
      message: "DM job {{ $labels.job_id }} for cluster {{ $labels.cluster_id }} being in modifying phase for too long."
    expr: |
      dataflow_dm_job_duration_in_phase{phase="modifying"} > 15*60 # 15 minutes.
    labels:
      severity: critical
      component: data-migration
