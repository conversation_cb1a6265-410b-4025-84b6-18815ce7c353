groups:
- name: ticdc
  rules:
# mute this alert since most failure can return to user
#  - alert: FailedToCreateChangefeed
#    annotations:
#      message: >
#        Failed to create changefeed {{ $labels.changefeed_id }} for tidb cluster {{ $labels.cluster_id }}.
#        Error message: {{ $labels.message }}.
#        Reported by service: {{ $labels.service }}, pod: {{ $labels.pod }}
#    expr: |
#      increase(dataflow_ticdc_changefeed_create_result{result="failed"}[5m])>0
#    labels:
#      severity: major
#      component: ticdc
  - alert: TiDBClusterChangefeedCreatingDurationTooLong
    annotations:
      message: The TiDB Cluster {{ $labels.cluster_id }}/{{ $labels.changefeed_id }} changefeed in creating status more than 30m.
    expr: |
      avg by (cluster_id, changefeed_id)(dataflow_changefeed_status) == 0
    for: 30m
    labels:
      severity: major
      component: ticdc
  - alert: TiDBClusterChangefeedScalingDurationTooLong
    annotations:
      message: The TiDB Cluster {{ $labels.cluster_id }}/{{ $labels.changefeed_id }} changefeed in scaling status more than 30m.
    expr: |
      avg by (cluster_id, changefeed_id)(dataflow_changefeed_status) == 9
    for: 30m
    labels:
      severity: major
      component: ticdc
