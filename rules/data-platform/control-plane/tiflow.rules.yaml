groups:
  - name: tiflow-agent
    rules:
      - alert: UnhealthyPod
        annotations:
          message: Pod {{ $labels.namespace }}/{{ $labels.pod }} in {{ $labels.cluster }} has been in a non-ready state for longer than 10 minutes.
        expr: kube_pod_status_phase{phase=~"Pending|Unknown|Failed", pod=~"tiflow.*"} == 1
        for: 10m
        labels:
          severity: critical
          component: data-migration
