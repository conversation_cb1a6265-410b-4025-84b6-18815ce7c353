groups:
- name: lightning
  rules:
  - alert: LighningPodNotReady
    annotations:
      summary: 'Lightning Pod is not ready'
      message: Lightning of Tenant {{ $labels.tenant}}, Cluster {{ $labels.namespace }} has been in a non-ready state for longer than 15 minutes.
    expr: |
      sum(kube_pod_status_phase{job="kube-state-metrics", phase=~"Pending|Unknown", pod=~"import.*"}) by (namespace, tenant) > 0
    for: 15m
    labels:
      component: lightning
      severity: critical
