groups:
- name: ticdc
  rules:
  - alert: BizChangefeedCheckpointHighDelay1h
    # filter out wetech(tenant: 1372813089209061633) since it have special rule
    # filter out cluster: eagle-insight(mk-jp-tidb-gcp, it's a dev cluster and its kafka always down)
    # filter out cluster: stg-tidb (ELESTYLE, it's a dev env)
    # filter out cluster: TIDBUAT (BingoPlus, it's a dev env)
    # filter out cluster: us-trans-prod (wetech, it sometimes has too high traffic, and the customer can tolerate the lag of changefeeed)
    # filter out cluster: production (Fujitsu Japan, burst traffic, flush configurations are not efficient for replication, but cost-effective)
    expr: ticdc_owner_checkpoint_ts_lag{instance=~"^db.*", tenant!~"1372813089209061633", tidb_cluster_id!~"1379661944646413949|1379661944635814075|10293089908087286529|1379661944646416107|1379661944646225237"} > 3600
    for: 10m # reduce noise for new created changefeed
    labels:
      severity: critical
      expr: ticdc_owner_checkpoint_ts_lag{instance=~"^db.*"} > 1h
      component: ticdc
    annotations:
      message: >
        Checkpoint of {{ $labels.changefeed }} delay more than 1 hour.
        TiDB Cluster: {{ $labels.tidb_cluster_id }}
        Reported by {{ $labels.instance }}
        Region: {{ $labels.seed_region }}
        Infra Arch: {{ $labels.seed_provider }}
  - alert: BizChangefeedCheckpointHighDelay3h
    # for cluster: us-trans-prod (wetech, it sometimes has too high traffic, and the customer can tolerate the lag of changefeeed)
    # for cluster: production (Fujitsu Japan, burst traffic, flush configurations are not efficient for replication, but cost-effective)
    expr: ticdc_owner_checkpoint_ts_lag{instance=~"^db.*", tidb_cluster_id=~"1379661944646416107|1379661944646225237"} > 10800
    for: 10m # reduce noise for new created changefeed
    labels:
      severity: major
      expr: ticdc_owner_checkpoint_ts_lag{instance=~"^db.*"} > 3h
      component: ticdc
    annotations:
      message: >
        Checkpoint of {{ $labels.changefeed }} delay more than 3 hours.
        TiDB Cluster: {{ $labels.tidb_cluster_id }}
        Reported by {{ $labels.instance }}
        Region: {{ $labels.seed_region }}
        Infra Arch: {{ $labels.seed_provider }}
  - alert: BizChangefeedIsFailedByKernelStatus
    # filter out wetech(tenant: 1372813089209061633) since it have special rule
    # filter out cluster: eagle-insight(mk-jp-tidb-gcp, it's a dev cluster and its kafka always down)
    # filter out cluster: stg-tidb (ELESTYLE, it's a dev env)
    # filter out cluster: TIDBUAT (BingoPlus, it's a dev env)
    expr: ticdc_owner_status{instance=~"^db.*", tenant!~"1372813089209061633", tidb_cluster_id!~"1379661944646413949|1379661944635814075|10293089908087286529"} == 2
    labels:
      severity: critical
      expr: ticdc_owner_status{instance=~"^db.*"} == 2
      component: ticdc
    annotations:
      message: >
        Changefeed: {{ $labels.changefeed }}
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
  - alert: BizChangefeedIsWarningByKernelStatus
    # filter out wetech(tenant: 1372813089209061633) since it have special rule
    # filter out cluster: eagle-insight(mk-jp-tidb-gcp, it's a dev cluster and its kafka always down)
    # filter out cluster: stg-tidb (ELESTYLE, it's a dev env)
    # filter out cluster: TIDBUAT (BingoPlus, it's a dev env)
    expr: ticdc_owner_status{instance=~"^db.*", tenant!~"1372813089209061633", tidb_cluster_id!~"1379661944646413949|1379661944635814075|10293089908087286529"} == 6
    for: 30m
    labels:
      severity: major
      expr: ticdc_owner_status{instance=~"^db.*"} == 6
      component: ticdc
    annotations:
      message: >
        Changefeed: {{ $labels.changefeed }}
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
  - alert: RGChangefeedCheckpointHighDelay10m
    expr: ticdc_owner_checkpoint_ts_lag{instance=~"^rg.*"}  > 600
    for: 15m # reduce noise for traffic spikes and temporary errors
    labels:
      severity: critical
      expr: ticdc_owner_checkpoint_ts_lag{instance=~"^rg.*"} > 10m
      component: ticdc
    annotations:
      message: >
        Checkpoint of {{ $labels.changefeed }} delay more than 10 minutes.
        TiDB Cluster: {{ $labels.tidb_cluster_id }}
        Reported by {{ $labels.instance }}
        Region: {{ $labels.seed_region }}
        Infra Arch: {{ $labels.seed_provider }}
  - alert: RGChangefeedTiCDCNodeFrequentlyUnavailable
    expr: sum(changes(floor(process_start_time_seconds{instance=~"^rg.*", component="ticdc"})[1h:15s])) by (cluster_id, instance) >= 4
    for: 1m
    labels:
      severity: critical
      expr: sum(changes(floor(process_start_time_seconds{instance=~"^rg.*", component="ticdc"})[1h:15s])) by (cluster_id, instance) >= 4
      component: ticdc
    annotations:
      message: >
        CDC pod restart frequently.
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
        Tenant: {{ $labels.tenant }}
  - alert: RGChangefeedIsFailedByKernelStatus
    expr: ticdc_owner_status{instance=~"^rg.*"} == 2
    labels:
      severity: critical
      expr: ticdc_owner_status{instance=~"^rg.*"} == 2
      component: ticdc
    annotations:
      message: >
        Changefeed: {{ $labels.changefeed }}
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
  - alert: RGChangefeedIsWarningByKernelStatus
    expr: ticdc_owner_status{instance=~"^rg.*"} == 6
    for: 2m
    labels:
      severity: major
      expr: ticdc_owner_status{instance=~"^rg.*"} == 6
      component: ticdc
    annotations:
      message: >
        Changefeed: {{ $labels.changefeed }}
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
  - alert: TiKVSideCDCSinkMemoryHighUsage
    expr: sum(tikv_cdc_sink_memory_bytes{instance=~".*-tikv.*"}) by (cluster_id, instance) > 300000000
    for: 2m
    labels:
      severity: critical
      expr: sum(tikv_cdc_sink_memory_bytes{instance=~".*-tikv.*"}) by (cluster_id, instance) > 300000000
      component: ticdc
    annotations:
      message: >
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
        Usage of CDC sink memory in TiKV side is high which default is 512MB, please refer to SOP https://pingcap.feishu.cn/wiki/OTIewy2EoiAnGCkTezmc0xgHnfc to handle this case.

# Those alerts move to rules/dedicated/swat/data-plane/kernel-tcocp.yaml
# Special rules for specific customers, which will alert both cloud team and kernel team
# one problem will have two alert rule: one for cloud team, one for kernel team
# 1. alert for tenant: wetech(1372813089209061633)
# 2. filter out test project: aws-pressure-test-saas(wetech: 1372813089454536384)
