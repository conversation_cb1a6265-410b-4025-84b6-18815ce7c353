# the customer alert rules for tiflow has been moved to rules/customer/data-migration.yaml
groups:
- name: data-migration
  rules:
  - alert: DMExecutorNotRunning
    annotations:
      summary: Data migration job executor not running for more than 1 minute
      message: Data migration job executor not running for more than 1 minute for data migration job
    expr: |
      max(dataflow_tiflow_server_master_job_num{status="Running"}) > count(dm_worker_task_state)
    for: 1m
    labels:
      severity: critical
      component: data-migration
