groups:
  - name: dataapi_executor
    rules:
      - alert: DataapiExecutorSessionCreateFailed
        annotations:
          message: namespace ： {{ $labels.namespace }} ,  Dataapi-gateway session create failed. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22editorMode%22:%22builder%22,%22expr%22:%22%7Bcluster_env%3D%5C%22prod%5C%22,%20cluster%3D%5C%22base%2Feks%2Fus-west-2%5C%22,%20container%3D%5C%22dataapp-executor%5C%22%7D%20%7C%3D%20%60ERROR%60%22,%22queryType%22:%22range%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          (sum(increase(dataservices_dataapi_executor_session_create_counter{namespace=~".*-ms",ok="false"}[1m])) by (namespace)) > 0
        labels:
          severity: major
          component: dataservice
      - alert: DataapiExecutorSessionNotFound
        annotations:
          message: namespace ： {{ $labels.namespace }} ,  Dataapi-gateway session not found. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22editorMode%22:%22builder%22,%22expr%22:%22%7Bcluster_env%3D%5C%22prod%5C%22,%20cluster%3D%5C%22base%2Feks%2Fus-west-2%5C%22,%20container%3D%5C%22dataapp-executor%5C%22%7D%20%7C%3D%20%60ERROR%60%22,%22queryType%22:%22range%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          (sum(increase(dataservices_dataapi_executor_session_not_found{namespace=~".*-ms"}[1m])) by (namespace)) > 1
        labels:
          severity: major
          component: dataservice
      - alert: DataapiExecutorDBConnFailed
        annotations:
          message: namespace ： {{ $labels.namespace }} ,  Dataapi-gateway connect database failed. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22editorMode%22:%22builder%22,%22expr%22:%22%7Bcluster_env%3D%5C%22prod%5C%22,%20cluster%3D%5C%22base%2Feks%2Fus-west-2%5C%22,%20container%3D%5C%22dataapp-executor%5C%22%7D%20%7C%3D%20%60ERROR%60%22,%22queryType%22:%22range%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          (sum(increase(dataservices_dataapi_executor_database_conn_counter{namespace=~".*-ms",ok="false"}[1m])) by (namespace)) > 0
        labels:
          severity: major
          component: dataservice
      - alert: DataapiExecutorClusterInfoGetFailed
        annotations:
          message: namespace ： {{ $labels.namespace }} ,  Dataapi-gateway get cluster info failed. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22editorMode%22:%22builder%22,%22expr%22:%22%7Bcluster_env%3D%5C%22prod%5C%22,%20cluster%3D%5C%22base%2Feks%2Fus-west-2%5C%22,%20container%3D%5C%22dataapp-executor%5C%22%7D%20%7C%3D%20%60ERROR%60%22,%22queryType%22:%22range%22%7D%5D,%22range%22:%7B%22from%22:%22now-1h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          (sum(increase(dataservices_dataapi_executor_cluster_info_counter{namespace=~".*-ms",ok="false"}[1m])) by (namespace)) > 0
        labels:
          severity: major
          component: dataservice
