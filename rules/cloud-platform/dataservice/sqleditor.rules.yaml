groups:
  - name: chat2query
    rules:
      - alert: Chat2QueryMetaFailure
        annotations:
          message: namespace:{{ $labels.namespace }},Chat2Query User Meta Failure. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bcontainer%3D%5C%22sqleditor%5C%22,cluster_env%3D%5C%22prod%5C%22%7D%20%7C%3D%60meta%60%20%7C~%20%60statusCode%3D%5B5%5D%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22,%22hide%22:false%7D%5D,%22range%22:%7B%22from%22:%22now-6h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          (sum(increase(sqleditor_http_requests_total{status=~"5.*",path="/api/v1/dataapps/sqleditor/orgs/:org_id/projects/:project_id/clusters/:cluster_id/meta", namespace=~".*-ms"}[1m])) by (namespace)) >=1
        labels:
          severity: major
          component: dataservice
      - alert: Chat2QuerySLAAlert
        annotations:
          message: namespace:{{ $labels.namespace }},Chat2Query SLA<98. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bcontainer%3D%5C%22sqleditor%5C%22,cluster_env%3D%5C%22prod%5C%22%7D%20%7C~%20%60statusCode%3D%5B5%5D%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22,%22hide%22:false%7D%5D,%22range%22:%7B%22from%22:%22now-6h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          ((sum(increase (kong_custom_http_status{kube_service=~"(.*)sqleditor(.*)",code=~"2(.*)"} [5m])) by (namespace)
                        / sum(increase (kong_custom_http_status{kube_service=~"(.*)sqleditor(.*)",code=~"5(.*)|2(.*)"}[5m]))  by (namespace))<0.98)  and (sum(increase (kong_custom_http_status{kube_service=~"(.*)sqleditor(.*)",code=~"5(.*)"} [5m])) by (namespace)>10 )
        labels:
          severity: critical
          component: dataservice

      - alert: Chat2Query_Http_Api_5xx
        annotations:
          message: namespace:{{ $labels.namespace }},Chat2Query API Error. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bcontainer%3D%5C%22sqleditor%5C%22,cluster_env%3D%5C%22prod%5C%22%7D%20%7C~%20%60statusCode%3D%5B5%5D%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22,%22hide%22:false%7D%5D,%22range%22:%7B%22from%22:%22now-6h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          (sum(increase(sqleditor_http_requests_total{status=~"5.*", namespace=~".*-ms"}[1m])) by (namespace)) >=1
        labels:
          severity: major
          component: dataservice

      - alert: Chat2Query_Http_Api_5xx_Many
        annotations:
          message: namespace:{{ $labels.namespace }},Chat2Query API Error Greater than 5 in 5min. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bcontainer%3D%5C%22sqleditor%5C%22,cluster_env%3D%5C%22prod%5C%22%7D%20%7C~%20%60statusCode%3D%5B5%5D%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22,%22hide%22:false%7D%5D,%22range%22:%7B%22from%22:%22now-6h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          (sum(increase(sqleditor_http_requests_total{status=~"5.*", namespace=~".*-ms"}[2m])) by (namespace)) >=5
        labels:
          severity: critical
          component: dataservice

      - alert: Chat2Query_Http_Api_5xx_Last5min
        annotations:
          message: namespace:{{ $labels.namespace }},Chat2Query API Error Last 5min. [loki link](https://clinic.pingcap.com/grafana/explore?left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bcontainer%3D%5C%22sqleditor%5C%22,cluster_env%3D%5C%22prod%5C%22%7D%20%7C~%20%60statusCode%3D%5B5%5D%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22,%22hide%22:false%7D%5D,%22range%22:%7B%22from%22:%22now-6h%22,%22to%22:%22now%22%7D%7D&orgId=1)
        expr: |
          (sum(increase(sqleditor_http_requests_total{status=~"5.*", namespace=~".*-ms"}[1m])) by (namespace)) >=1
        for: 5m
        labels:
          severity: critical
          component: dataservice
