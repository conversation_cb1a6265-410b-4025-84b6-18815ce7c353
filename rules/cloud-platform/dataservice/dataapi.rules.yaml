groups:
  - name: dataapi
    rules:
      - alert: DataapiSessionCreateFailed
        annotations:
          message: namespace ： {{ $labels.namespace }} ,  Dataapi session create failed. [loki link](https://clinic.pingcap.com/grafana/explore?orgId=1&left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bapp%3D%5C%22dataservice-dataapi%5C%22%7D%20%7C%3D%60ERROR%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-5m%22,%22to%22:%22now%22%7D%7D)
        expr: |
          (sum(increase(dataservices_dataapi_session_create_failed{namespace=~".*-ms"}[1m])) by (namespace)) > 0
        labels:
          severity: major
          component: dataservice
      - alert: DataapiSessionNotFound
        annotations:
          message: namespace ： {{ $labels.namespace }} ,  Dataapi session not found. [loki link](https://clinic.pingcap.com/grafana/explore?orgId=1&left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bapp%3D%5C%22dataservice-dataapi%5C%22%7D%20%7C%3D%60ERROR%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-5m%22,%22to%22:%22now%22%7D%7D)
        expr: |
          (sum(increase(dataservices_dataapi_session_not_found{namespace=~".*-ms"}[1m])) by (namespace)) > 1
        labels:
          severity: major
          component: dataservice
      - alert: DataapiDBConnFailed
        annotations:
          message: namespace ： {{ $labels.namespace }} ,  Dataapi connect database failed. [loki link](https://clinic.pingcap.com/grafana/explore?orgId=1&left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bapp%3D%5C%22dataservice-dataapi%5C%22%7D%20%7C%3D%60ERROR%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-5m%22,%22to%22:%22now%22%7D%7D)
        expr: |
          (sum(increase(dataservices_dataapi_db_conn_failed{namespace=~".*-ms"}[1m])) by (namespace)) > 0
        labels:
          severity: major
          component: dataservice
      - alert: DataapiClusterInfoGetFailed
        annotations:
          message: namespace ： {{ $labels.namespace }} ,  Dataapi get cluster info failed. [loki link](https://clinic.pingcap.com/grafana/explore?orgId=1&left=%7B%22datasource%22:%22Loki-aws-us-west-2%22,%22queries%22:%5B%7B%22refId%22:%22A%22,%22expr%22:%22%7Bapp%3D%5C%22dataservice-dataapi%5C%22%7D%20%7C%3D%60ERROR%60%22,%22queryType%22:%22range%22,%22editorMode%22:%22code%22%7D%5D,%22range%22:%7B%22from%22:%22now-5m%22,%22to%22:%22now%22%7D%7D)
        expr: |
          (sum(increase(dataservices_dataapi_cluster_info_get_failed{namespace=~".*-ms"}[1m])) by (namespace)) > 0
        labels:
          severity: major
          component: dataservice
