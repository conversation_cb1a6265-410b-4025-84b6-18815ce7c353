groups:
- name: tiflash
  rules:
  - alert: TiflashRestartsFrequently
    expr: sum(increase(kube_pod_container_status_restarts_total{container="tiflash"}[30m]))by(container,tenant,namespace,cluster_id)>5
    for: 10m
    labels:
      severity: critical
      expr: sum(increase(kube_pod_container_status_restarts_total{container="tiflash"}[30m]))by(container,tenant,namespace,cluster_id)>5
      component: metadb
    annotations:
      summary: Tiflash container restart frequently
      message: Container {{ $labels.namespace }}/{{ $labels.container }} is restarting {{ printf "%.2f" $value }} times in 30 minutes.
  - alert: TiFlashFrequentlyUnavailable
    expr: sum_over_time(up{component="tiflash"} [10m]) - count_over_time(up{component="tiflash"} [10m]) * 0.7 < 0
    for: 1m
    labels:
      severity: major
      expr: sum_over_time(up{component="tiflash"} [10m]) - count_over_time(up{component="tiflash"} [10m]) * 0.7 < 0
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.i4g5n2vl9lgp
      value: '{{ $value }}'
      summary: TiFlash server unavailable time more than 3 minute in 10 minut
