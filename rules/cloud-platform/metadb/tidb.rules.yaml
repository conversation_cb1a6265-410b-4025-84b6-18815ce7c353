groups:
- name: tidb
  rules:
  - alert: TiDBServerIsDown
    expr: up{component="tidb"} == 0
    for: 10m
    labels:
      severity: critical
      expr: up{component="tidb"} == 0
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.tfuldltfya4a
      value: '{{ $value }}'
      summary: TiDB server is dow
  - alert: TiDBDiscoveredTimeJumpBack
    expr: increase(tidb_monitor_time_jump_back_total[10m])  > 0
    for: 5m
    labels:
      severity: major
      expr:  increase(tidb_monitor_time_jump_back_total[10m])  > 0
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ph7vof1otr6g
      value: '{{ $value }}'
      summary: TiDB monitor found time jump back erro
  - alert: TiDBHighTokenUsage
    expr: sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type) > 0 and (sum(tidb_server_tokens{}) by (cluster_id, instance, tenant,provider_type) / sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type)) > 0.8
    for: 5m
    labels:
      severity: major
      expr: sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type) > 0 and (sum(tidb_server_tokens{}) by (cluster_id, instance, tenant,provider_type) / sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type)) > 0.8
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }} token usage is values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.es82zlkd14z
      value: '{{ $value }}'
      summary: TiDB token usage too hig
  - alert: TiDBQueryUnexpectedlyFailed
    expr: increase(tidb_server_execute_error_total{type=~".*8027|.*8120|.*8021|.*8024|.*9001|.*9002|.*9003|.*9011|.*9012|.*9013"}[5m]) > 1
    for: 5m
    labels:
      severity: major
      expr: increase(tidb_server_execute_error_total{type=~".*8027|.*8120|.*8021|.*8024|.*9001|.*9002|.*9003|.*9011|.*9012|.*9013"}[5m]) > 1
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, error type: {{ $labels.type }}, count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.vr9n074q793p
      value: '{{ $value }}'
      summary: TiDB query got unexpected erro
  - alert: TiDBFrequentlyUnavailable
    expr: sum_over_time(up{component="tidb"} [10m]) - count_over_time(up{component="tidb"} [10m]) * 0.7 < 0
    for: 1m
    labels:
      severity: major
      expr: sum_over_time(up{component="tidb"} [10m]) - count_over_time(up{component="tidb"} [10m]) * 0.7 < 0
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.u233t02connq
      value: '{{ $value }}'
      summary: TiDB server unavailable time more than 3 minute in 10 minut
  - alert: AllServerIsDown
    expr: count(up{component=~"tidb|tiflash|pd|tikv"} == 1) by (cluster_id,component,tenant,provider_type) == 0
    for: 1m
    labels:
      severity: critical
      expr: count(up{component=~"tidb|tiflash|pd|tikv"} == 1) by (cluster_id,component,tenant,provider_type) == 0
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, component: {{ $labels.component }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.zh4l71dqrluk
      value: '{{ $value }}'
      summary: "All {{ $labels.component }} server is down"
