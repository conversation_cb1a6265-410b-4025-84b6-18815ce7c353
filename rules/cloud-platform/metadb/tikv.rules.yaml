groups:
- name: tikv
  rules:
  - alert: TiKVServerIsDown
    expr: up{component="tikv"} == 0
    for: 10m
    labels:
      severity: critical
      expr: up{component="tikv"} == 0
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.t0ws8abzblbu
      value: '{{ $value }}'
      summary: TiKV server is down
  - alert: TiKVServerDownMoreThan2
    expr: count(up{component="tikv"} == 0) by (cluster_id,tenant,provider_type) > 2
    for: 1m
    labels:
      severity: critical
      expr: count(up{component="tikv"} == 0) by (cluster_id,tenant,provider_type) > 2
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, down tikv server count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.t0ws8abzblbu
      value: '{{ $value }}'
      summary: "TiKV server down count more than 2"
  # - alert: TiKVServerDownMoreThan1AZ
  #   expr: |
  #       count(count(
  #         label_replace((kube_pod_info{pod=~"db-tikv.*"} * on(node) group_left(provider_id) kube_node_info),"az_id",  "$1", "provider_id", "(.*/)(\\w+.*)")
  #         * on(pod,namespace) group_right(az_id)
  #         label_replace(label_replace(up{component="tikv"},"pod",  "$0", "instance", ".*"),"namespace",  "$0", "cluster_id", ".*") == 0
  #       ) by (cluster_id, az_id)) by (cluster_id) > 1
  #   for: 1m
  #   labels:
  #     severity: critical
  #     component: metadb
  #     expr: |
  #         count(count(
  #           label_replace((kube_pod_info{pod=~"db-tikv.*"} * on(node) group_left(provider_id) kube_node_info),"az_id",  "$1", "provider_id", "(.*/)(\\w+.*)")
  #           * on(pod,namespace) group_right(az_id)
  #           label_replace(label_replace(up{component="tikv"},"pod",  "$0", "instance", ".*"),"namespace",  "$0", "cluster_id", ".*") == 0
  #         ) by (cluster_id, az_id)) by (cluster_id) > 1
  #   annotations:
  #     description: 'cluster: {{ $labels.cluster_id }}, down tikv servers which in different AZ, count: {{ $value }}'
  #     runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ijox0linkuyj
  #     value: '{{ $value }}'
  #     summary: "TiKV server down count more than 1 and in different AZ"
  - alert: TiKVLowSpace
    expr: sum(tikv_store_size_bytes{type="available"}) by (cluster_id, instance, tenant,provider_type)  / sum(tikv_store_size_bytes{type="capacity"}) by (cluster_id, instance, tenant,provider_type)  * 100 < 20
    for: 5m
    labels:
      severity: major
      expr: sum(tikv_store_size_bytes{type="available"}) by (cluster_id, instance, tenant,provider_type)  / sum(tikv_store_size_bytes{type="capacity"}) by (cluster_id, instance, tenant,provider_type)  * 100 < 20
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.3tz23916jlht
      value: '{{ $value }}'
      summary: "TiKV store space used more than 80%"
  - alert: TiKVVeryLowSpace
    expr: sum(tikv_store_size_bytes{type="available"}) by (cluster_id, instance, tenant,provider_type)  / sum(tikv_store_size_bytes{type="capacity"}) by (cluster_id, instance, tenant,provider_type)  * 100 < 10
    for: 5m
    labels:
      severity: critical
      expr: sum(tikv_store_size_bytes{type="available"}) by (cluster_id, instance, tenant,provider_type)  / sum(tikv_store_size_bytes{type="capacity"}) by (cluster_id, instance, tenant,provider_type)  * 100 < 10
      component: metadb
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.w4zl0kl6ny53
      value: '{{ $value }}'
      summary: "TiKV store space used more than 90%"
  - alert: TiKVRaftstoreCpuHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"raftstore_.*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"raftstore_.*"}) by (cluster_id, instance, tenant,provider_type)  > 0.8
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Raftstore high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's raftstore is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ynohpbfagetz
  - alert: TiKVApplyWorkerPoolCpuHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"apply_[0-9]+"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"apply_[0-9]+"}) by (cluster_id, instance, tenant,provider_type)  > 0.9
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Apply worker thread pool high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's apply worker thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.7k1o575l3zea
  - alert: TiKVSchedulerWorkerCpuHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"sched_.*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"sched_.*"}) by (cluster_id, instance, tenant,provider_type)  > 0.9
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Scheduler worker high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's scheduler worker thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.qptrnffkw5h
  - alert: TiKVGrpcPollHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"grpc.*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"grpc.*"}) by (cluster_id, instance, tenant,provider_type)  > 0.8
    for: 5m
    labels:
      
      severity: major
      component: metadb
    annotations:
      summary: "gRPC high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's gRPC polling thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.30ra2mgpp1i4
  - alert: TiKVUnifiedReadPoolHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"unified_read_po*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"unified_read_po*"}) by (cluster_id, instance, tenant,provider_type)  > 0.9
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Unified read pool high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's unified read pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.y1jc473vb50t
  - alert: TiKVHighPriorityStorageReadPoolHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"store_read_high*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"store_read_high*"}) by (cluster_id, instance, tenant,provider_type)  > 0.8
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Storage readpool's high priority thread pool high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's storage readpool's high priority thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.4w4edi22y9gn
  - alert: TiKVNormalPriorityStorageReadPoolHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"store_read_norm*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"store_read_norm*"}) by (cluster_id, instance, tenant,provider_type)  > 0.8
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Storage readpool's normal priority thread pool high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's storage readpool's normal priority thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.4w4edi22y9gn
  - alert: TiKVLowPriorityStorageReadPoolHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"store_read_low*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"store_read_low*"}) by (cluster_id, instance, tenant,provider_type)  > 0.8
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Storage readpool's low priority thread pool high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's storage readpool's low priority thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.4w4edi22y9gn
  - alert: TiKVHighPriorityCopCpuHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"cop_high*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"cop_high*"}) by (cluster_id, instance, tenant,provider_type)  > 0.8
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Coprocessor's high priority thread pool high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's coprocessor's high priority thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.th5j33flx0o
  - alert: TiKVNormalPriorityCopCpuHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"cop_normal*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"cop_normal*"}) by (cluster_id, instance, tenant,provider_type)  > 0.8
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Coprocessor's normal priority thread pool high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's coprocessor's normal priority thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.th5j33flx0o
  - alert: TiKVLowPriorityCopCpuHighLoad
    expr: sum(rate(tikv_thread_cpu_seconds_total{name=~"cop_low*"}[1m])) by (cluster_id, instance, tenant,provider_type)  / count(tikv_thread_cpu_seconds_total{name=~"cop_low*"}) by (cluster_id, instance, tenant,provider_type)  > 0.8
    for: 5m
    labels:
      severity: major
      component: metadb
    annotations:
      summary: "Coprocessor's low priority thread pool high CPU load (cluster: {{ $labels.cluster_id }}, instance {{ $labels.instance }})"
      description: "TiKV node's coprocessor's low priority thread pool is under a high load\n  VALUE = {{ $value }}"
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.th5j33flx0o
