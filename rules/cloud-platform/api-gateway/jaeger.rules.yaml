groups:
- name: jaeger
  rules:
  - alert: J<PERSON>gerCollectorNoData
    annotations:
      message: <PERSON><PERSON><PERSON> colletctor hasn't reveived any data last 5 minutes
    expr: |
      absent(sum(rate(jaeger_collector_traces_received_total{}[5m])))
    for: 5m
    labels:
      severity: critical
      component: api-gateway
  - alert: JaegerCollectorSaveTraceFailed
    annotations:
      message: J<PERSON><PERSON> colletctor save data failed last 5 minutes
    expr: |
      sum(rate(jaeger_collector_traces_saved_by_svc_total{result="err"}[5m])) > 0
    for: 5m
    labels:
      severity: critical
      component: api-gateway
