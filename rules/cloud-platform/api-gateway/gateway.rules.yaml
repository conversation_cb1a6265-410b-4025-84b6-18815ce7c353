groups:
- name: api-gateway
  rules:
  - alert: APIGatewayDown
    annotations:
      message: API Gateway metrics has been disappeared for 2 minutes.
    expr: |
      absent(sum(rate(kong_http_status{}[1m])))
    for: 2m
    labels:
      severity: critical
      component: api-gateway
  - alert: APIGatewayTooManyErrors
    annotations:
      message: API Gateway has 502/503 greater than 0.2 qps for 2 minutes.
    expr: |
      sum(rate(kong_http_status{code=~"502|503"}[1m])) by (code) > 0.2
    for: 2m
    labels:
      severity: critical
      component: api-gateway
  - alert: APIGatewayTooMany4xx
    annotations:
      message: API Gateway has 4xx greater than 1 QPS for 3 minutes.
    expr: |
      sum(rate(kong_http_status{code=~"4(.*)",code!="401"}[1m])) by (code) > 1
    for: 3m
    labels:
      severity: major
      component: api-gateway
  - alert: APIGatewayTooMany5xx
    annotations:
      message: API Gateway has 5xx greater than 2 QPS for 3 minutes.
    expr: |
      sum(rate(kong_http_status{code=~"5(.*)"}[1m])) by (code) > 2
    for: 3m
    labels:
      severity: critical
      component: api-gateway
  - alert: APIGatewayQPSIncreased
    annotations:
      message: API Gateway QPS has been greater than 200 qps for 2 minutes.
    expr: |
      sum(rate(kong_http_status{}[1m])) > 200
    for: 2m
    labels:
      severity: major
      component: api-gateway
