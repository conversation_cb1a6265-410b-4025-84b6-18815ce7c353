groups:
  - name: account
    rules:
        # account server alert
      - alert: account_severDown
        annotations:
          description: 'account server down alert.'
          message: namespace ： {{ $labels.namespace }} / {{ $labels.pod }}  down
        expr: |
          up{job=~"account.*"} == 0
        for: 2m
        labels:
          severity: major
          component: account-server
      #  login_success_rate
      - alert: account_loginFailMoreThan5
        annotations:
          description: 'account login fail more than 10  int 5 minutes.'
          message: namespace ： {{ $labels.namespace }} ,  account login fail more than 5  in 30 minutes
        expr: |
          (sum(dbaas_account_login_count{eventResult="FAILD", namespace=~".*-ms"}) by (namespace)
            -sum(dbaas_account_login_count{eventResult="FAILD" ,namespace=~".*-ms"} offset 30m) by (namespace)
          )>5
        for: 2m
        labels:
          severity: major
          component: account-server

          #  login_fail_alert
      - alert: account_loginFailMoreThan10
        annotations:
          description: 'account login fail more than 15  int 30 minutes.'
          message: namespace ： {{ $labels.namespace }} ,  account login fail more than 10  in 30 minutes
        expr: |
          (sum(dbaas_account_login_count{eventResult="FAILD", namespace=~".*-ms"}) by (namespace)
            -sum(dbaas_account_login_count{eventResult="FAILD" ,namespace=~".*-ms"} offset 30m) by (namespace)
          )>10
        for: 5m
        labels:
          severity: critical
          component: account-server

          #  error_code_alert warning
      - alert: account_errorCodeTooMany(20%)
        annotations:
          description: 'account 5** code  too many  in 5 minutes，the percent > 20'
          message: namespace ： {{ $labels.namespace }} ,  account 5** code  too many  in 5 minutes，the percent > 20
        expr: |
          (sum(increase (kong_http_status{code=~"5.*",route=~".*-ms.account-central(.*)"}[30m])) by (namespace)
            / sum(increase (kong_http_status{route=~".*-ms.account-central(.*)"}[30m]))  by (namespace)
           )>0.2
              and (sum(increase (kong_http_status{code=~"5.*",route=~".*-ms.account-central(.*)"}[30m])) by (namespace)>5 )
        for: 1m
        labels:
          severity: major
          component: account-server

          #  error_code_alert  critical
      - alert: account_errorCodeTooMany(30%)
        annotations:
          description: 'account 5** code  too many  in 30 minutes，the percent > 30'
          message: namespace ： {{ $labels.namespace }} ,  account 5** code  too many  in 5 minutes，the percent > 30
        expr: |
          ((sum(increase (kong_http_status{code=~"5.*",route=~".*-ms.account-central(.*)"}[30m])) by (namespace)
            / sum(increase (kong_http_status{route=~".*-ms.account-central(.*)"}[30m]))  by (namespace)
           )>0.3)
                and (sum(increase (kong_http_status{code=~"5.*",route=~".*-ms.account-central(.*)"}[30m])) by (namespace)>10 )
        for: 1m
        labels:
          severity: critical
          component: account-server

        #  sla_alert  warning
      - alert: account_slaAlert(80%)
        annotations:
          description: 'account 2** code in 30 minutes，the percent < 20'
          message: namespace ： {{ $labels.namespace }} ,  account 5** code  too many  in 30 minutes，the percent > 30
        expr: |
          ((sum(increase (kong_custom_http_status{kube_service=~"(.*)account-central(.*)",code=~"2(.*)"} [30m])) by (namespace)
            / sum(increase (kong_custom_http_status{kube_service=~"(.*)account-central(.*)",code=~"5(.*)|2(.*)"}[30m]))  by (namespace))<0.5)  and (sum(increase (kong_custom_http_status{kube_service=~"(.*)account-central(.*)",code=~"5(.*)"} [30m])) by (namespace)>10 )

        for: 1m
        labels:
          severity: warning
          component: account-server

        #  pod_restart  warning
      - alert: account_podRestaetAlert
        annotations:
          description: 'account pod restart count in 24 hours > 2'
          message: namespace ： {{ $labels.namespace }} ,  account pod restart count in 24 hours > 2
        expr: |
          sum(increase (kube_pod_container_status_restarts_total{namespace=~"staging-ms|prod-ms", pod=~"account-(.*)"} [24h])) by (pod) > 2
        for: 1m
        labels:
          severity: warning
          component: account-server
