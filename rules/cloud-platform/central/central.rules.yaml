groups:
- name: central
  rules:
  - alert: DBaaSMetaDBDailyBackupFailed
    expr: |
      sum by (namespace, pod) (
        kube_pod_status_phase{pod=~"backup.*",phase="Failed"} > 0
      )
    labels:
      severity: critical
      component: metadb
    annotations:
      description: The daily backup failed in {{ $labels.namespace }}/{{ $labels.pod }}.
      summary: DBaaS Meta DB Daily Backup Failed
  - alert: QPSOfMetaDBIsZero
    expr: |
      sum(rate(tidb_executor_statement_total{}[1m]))==0
    for: 3m
    labels:
      severity: critical
      component: metadb
    annotations:
      description: The QPS of metadb is 0.
  - alert: LatencyOfCentralUnaryRequestsTooHigh
    expr: |
      histogram_quantile(0.95,
        sum(rate(grpc_server_handling_seconds_bucket{job=~".*-metrics",grpc_type="unary",grpc_service!="central.ClusterService"}[5m])) by (job,grpc_service,le)
      ) > 5
    for: 10m
    labels:
      severity: major
      component: central-server
    annotations:
      message: The central grpc requests(p95) are slower than 5s in {{ $labels.grpc_service }} for 10 mins
  - alert: LatencyOfGetUserProfileV2TooHigh
    expr: |
      histogram_quantile(0.95,
        sum(rate(grpc_server_handling_seconds_bucket{job=~".*-metrics",grpc_type="unary",grpc_service="central.UserService",grpc_method="GetUserProfileV2"}[5m])) by (job,grpc_service,grpc_method,le)
      ) > 1
    for: 10m
    labels:
      severity: major
      component: central-server
    annotations:
      message: The central grpc requests(p95) are slower than 1s in in {{ $labels.grpc_method }} for 10 mins
  - alert: LatencyOfListUsersTooHigh
    expr: |
      histogram_quantile(0.95,
        sum(rate(grpc_server_handling_seconds_bucket{job=~".*-metrics",grpc_type="unary",grpc_service="central.TenantService",grpc_method="ListUsers"}[5m])) by (job,grpc_service,grpc_method,le)
      ) > 0.60
    for: 10m
    labels:
      severity: major
      component: central-server
    annotations:
      message: The central grpc requests(p95) are slower than 600ms in {{ $labels.grpc_method }} for 10 mins
  - alert: LatencyOfListProjectUsersTooHigh
    expr: |
      histogram_quantile(0.95,
        sum(rate(grpc_server_handling_seconds_bucket{job=~".*-metrics",grpc_type="unary",grpc_service="central.TenantService",grpc_method="ListProjectUsers"}[5m])) by (job,grpc_service,grpc_method,le)
      ) > 0.60
    for: 10m
    labels:
      severity: major
      component: central-server
    annotations:
      message: The central grpc requests(p95) are slower than 600ms in {{ $labels.grpc_method }} for 10 mins
  - alert: LatencyOfListOpenAPIKeysTooHigh
    expr: |
      histogram_quantile(0.95,
        sum(rate(grpc_server_handling_seconds_bucket{job=~".*-metrics",grpc_type="unary",grpc_service="central.OpenAPIKeyService",grpc_method="ListOpenAPIKeys"}[5m])) by (job,grpc_service,grpc_method,le)
      ) > 0.60
    for: 10m
    labels:
      severity: major
      component: central-server
    annotations:
      message: The central grpc requests(p95) are slower than 600ms in {{ $labels.grpc_method }} for 10 mins
  - alert: LatencyOfListOrgActiveAuthMethodsTooHigh
    expr: |
      histogram_quantile(0.95,
        sum(rate(grpc_server_handling_seconds_bucket{job=~".*-metrics",grpc_type="unary",grpc_service="central.TenantService",grpc_method="ListOrgActiveAuthMethods"}[5m])) by (job,grpc_service,grpc_method,le)
      ) > 0.30
    for: 10m
    labels:
      severity: major
      component: central-server
    annotations:
      message: The central grpc requests(p95) are slower than 300ms in {{ $labels.grpc_method }} for 10 mins
  - alert: LatencyOfGinHttpServerRequestsTooHigh
    expr: |
      histogram_quantile(0.99,sum(rate(gin_request_duration_seconds_bucket{cluster="base/eks/us-west-2"}[2m])) by (service,url,le)) > 5
    for: 5m
    labels:
      severity: critical
      component: central-server
      team: dbaas-platform
    annotations:
      message: The Gin http-server requests(p99) are slower than 5s in {{ $labels.service }} {{ $labels.url }} for 5 mins
  # The cluster grpc services is being optimized, we should adjust the latency of threshold when the optimization is finished
#  - alert: LatencyOfCentralClusterGrpcServicesRequestsTooHigh
#    expr: |
#      histogram_quantile(0.95,
#        sum(rate(grpc_server_handling_seconds_bucket{job=~".*-metrics",grpc_type="unary",grpc_service="central.ClusterService"}[5m])) by (job,grpc_service,le)
#      ) > 3
#    for: 10m
#    labels:
#      severity: major
#      component: central-server
#    annotations:
#      message: The central grpc requests(p95) are slower than 3s in {{ $labels.grpc_service }} for 10 mins
  - alert: CentralWorkerError
    expr: |
      sum(rate(dbaas_worker_duration_ms_count{status != "ok"}[5m])) by (namespace,tenant,name) > 0
    for: 5m
    labels:
      severity: warning
      component: central-worker
    annotations:
      message: "{{ $labels.name }} worker of tenant {{ $labels.tenant }} project {{ $labels.namespace }} encountered error"
# blackbox alerting
- name: blackbox-exporter
  rules:
  - alert: ProbeDown
    expr: |
      up{job=~"blackbox.*"}==0
    for: 5m
    labels:
      severity: major
      component: central-server
    annotations:
      summary: "Probe job {{ $labels.job }} down"
  - alert: ProbeFailed
    expr: |
      probe_success == 0
    for: 5m
    labels:
      severity: major
      component: central-server
    annotations:
      summary: "Probe failed for instance {{ $labels.instance }}"
  - alert: SlowProbe
    expr: |
      avg_over_time(probe_duration_seconds[1m]) > 1
    for: 5m
    labels:
      severity: major
      component: central-server
    annotations:
      summary: "Slow probe for instance {{ $labels.instance }}"
      description: "Blackbox probe took more than 1s to complete"
  - alert: HttpStatusCode
    expr: |
      probe_http_status_code >= 400
    for: 5m
    labels:
      severity: critical
      component: central-server
    annotations:
      summary: "HTTP Status Code more than 400 for instance {{ $labels.instance }}"
      description: "HTTP status code more than 400"
  - alert: SslCertificateWillExpireSoon
    expr: |
      probe_ssl_earliest_cert_expiry - time() < 86400 * 7
    for: 5m
    labels:
      severity: critical
      component: central-server
    annotations:
      summary: "SSL certificate will expire soon instance for instance {{ $labels.instance }}"
      description: "SSL certificate expires in 7 days"
  # dns module
  - alert: DnsSlowLookup
    expr: |
      avg_over_time(probe_dns_lookup_time_seconds[1m]) > 1
    for: 5m
    labels:
      severity: critical
      component: central-server
    annotations:
      summary: "DNS slow lookup for instance {{ $labels.instance }}"
      description: "DNS lookup took more than 1s"
