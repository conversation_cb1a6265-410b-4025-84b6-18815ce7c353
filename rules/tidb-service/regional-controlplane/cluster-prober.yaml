groups:
  - name: cluster-prober.rules
    rules:
      - alert: ProbedDatabasePingFailed
        annotations:
          message: |
            Failed to ping the cluster {{ $labels.cluster_id }} via the addr 
            {{ $labels.db_addr }}.
        expr: cluster_prober_database_ping == 0
        for: 15m
        labels:
          severity: critical
          component: cluster-prober
      - alert: ProbedDatabaseWriteFailed
        annotations:
          message: |
            Failed to write the cluster {{ $labels.cluster_id }} via the addr
            {{ $labels.db_addr }}.
        expr: cluster_prober_database_write == 0
        for: 15m
        labels:
          severity: critical
          component: cluster-prober
      - alert: ProbedClusterNotExistInInfra
        annotations:
          message: |
            The cluster {{ $labels.cluster_ns }}/{{ $labels.cluster_name }} is 
            not exist in infra apiserver.
        expr: cluster_prober_infra_cluster_exist == 0
        for: 15m
        labels:
          severity: critical
          component: cluster-prober
      - alert: ProbedClusterPhaseNotRunningInInfra
        annotations:
          message: |
            The cluster phase {{ $labels.cluster_ns }}/{{ $labels.cluster_name }} is 
            {{ $labels.cluster_phase }} in infra apiserver.
        expr: cluster_prober_infra_cluster_normal == 0
        for: 15m
        labels:
          severity: critical
          component: cluster-prober
      - alert: ProbedClusterPhaseNotExistInPlatform
        annotations:
          message: |
            The cluster {{ $labels.cluster_id }} is not exist in platform.
        expr: cluster_prober_infra_cluster_normal == 0
        for: 15m
        labels:
          severity: critical
          component: cluster-prober
