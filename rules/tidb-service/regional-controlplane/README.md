# Regional Controlplane Rules

This directory contains monitoring and alerting rules for the regional controlplane components of TiDB Dedicated - Cluster Service.

## Files Overview

### crossplane.rules.yaml
Contains monitoring rules for Crossplane resources, specifically focusing on the responsiveness of Crossplane controllers. 

### infra-exporter.rules.yaml
Defines comprehensive monitoring rules for regional CRs, including:
- Cluster CR monitoring (scaling, upgrading, creation, deletion)
- Regional and cluster upgrade operations
- Status tracking for paused cluster CRs

### infra-provider.yaml
Contains monitoring rules for infrastructure providers, including:
- EKS and GKE provider health checks
- Workqueue depth monitoring
- OOM (Out of Memory) conditions
- Guard check failures
- Configuration change monitoring
- Controller panic detection

### dataplane-components.yaml
Monitoring rules for dataplane components.

### cluster-prober.yaml
Contains rules for cluster probing and health checks.

### gcp-quota-exporter.yaml
Monitoring rules for GCP quota usage and limits.

### prerelease-job-cluster-controller.yaml
Rules for monitoring prerelease job of cluster controller.
