groups:
  - name: dataplane-components.rules
    rules:
      - alert: UnhealthyPod
        annotations:
          message: Pod {{ $labels.namespace }}/{{ $labels.pod }} in {{ $labels.cluster }} has been in a non-ready state for longer than 10 minutes.
        expr: kube_pod_status_phase{phase=~"Pending|Unknown|Failed", namespace=~"tidb-admin|kube-system|cert-manager", pod!~"dataflow-agent.*|tiflow.*"} == 1
        for: 10m
        labels:
          component: kubernetes
          severity: critical
          type: shoot
          tier: dedicated
      - alert: UnhealthyPod
        annotations:
          message: Pod {{ $labels.namespace }}/{{ $labels.pod }} in {{ $labels.cluster }} has been in a non-ready state for longer than 10 minutes.
        expr: kube_pod_status_phase{phase=~"Pending|Unknown|Failed", pod=~"cluster-agent-.*"} == 1
        for: 10m
        labels:
          component: kubernetes
          severity: major
          type: shoot
          tier: dedicated
      - alert: UnhealthyTiDBComponentPod
        annotations:
          message: Pod {{ $labels.namespace }}/{{ $labels.pod }} in {{ $labels.cluster }} has been in a non-ready state for longer than 15 minutes.
        expr: kube_pod_status_phase{phase=~"Pending|Unknown|Failed", namespace=~"tidb\\d+", pod=~"db-(pd|tidb|tikv|tiflash|tiproxy).*"} == 1
        for: 20m
        labels:
          component: kubernetes
          severity: critical
          type: shoot
          tier: dedicated
      - alert: NodeNotReady
        expr: kube_node_status_condition{condition="Ready",status="true"} * on(node,k8s_cluster_info) group_right() kube_node_labels{} == 0
        for: 15m
        labels:
          component: kubernetes
          severity: major
          type: shoot
          tier: dedicated
        annotations:
          description: "Node {{ $labels.node }} in {{ $labels.cluster }} has been unready for 15m+ \n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
      - alert: NodeNotReadyForLongTime
        expr: kube_node_status_condition{condition="Ready",status="true"} * on(node,k8s_cluster_info) group_right() kube_node_labels{} == 0
        for: 50m
        labels:
          component: kubernetes
          severity: critical
          type: shoot
          tier: dedicated
        annotations:
          description: "Node {{ $labels.node }} in {{ $labels.cluster }} has been unready for 50m+ \n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

      - alert: CoreDNSLatencyHigh
        annotations:
          description: CoreDNS in {{ $labels.cluster }} has 99th percentile latency of {{ $value }} seconds.
          runbook_url: https://github.com/povilasv/coredns-mixin/tree/master/runbook.md#alert-name-corednslatencyhigh
          summary: CoreDNS is experiencing high 99th percentile latency.
        expr: |
          histogram_quantile(0.99, sum(rate(coredns_dns_request_duration_seconds_bucket{job="coredns"}[5m])) without (instance,pod)) > 2
        for: 10m
        labels:
          severity: major
          component: kubernetes
          type: shoot
          tier: dedicated

      - alert: CoreDNSForwardLatencyHigh
        annotations:
          description: CoreDNS in {{ $labels.cluster }} has 99th percentile latency of {{ $value }} seconds forwarding
            requests.
          runbook_url: https://github.com/povilasv/coredns-mixin/tree/master/runbook.md#alert-name-corednsforwardlatencyhigh
          summary: CoreDNS is experiencing high latency forwarding requests.
        expr: |
          histogram_quantile(0.99, sum(rate(coredns_forward_request_duration_seconds_bucket{job="coredns"}[5m])) without (pod, instance, rcode)) > 4
        for: 10m
        labels:
          severity: major
          component: kubernetes
          type: shoot
          tier: dedicated

      - alert: ENA_Bandwidth_In_Allowance_Exceeded
        expr: |
          label_replace(
            rate(node_ethtool_bw_in_allowance_exceeded{}[2m]),
            "tenant_id", "$1", "tenant", "(.*)"
          ) > 100
        for: 1h
        labels:
          severity: warning
          component: kubernetes
          type: shoot
          tier: dedicated
          # visibility: region-tse
        annotations:
          message: "Device {{ $labels.device }} of Instance {{ $labels.instance }} in {{ $labels.cluster }} bandwidth-in exceeded"
          description: "Instance {{ $labels.instance }} from {{ $labels.cluster }} has a bandwidth in allowwance greater than {{ $value }}"
          runbook_url: https://fx460onx10s.feishu.cn/wiki/Krjsw8orEilio1kfVPmcPvh7ncg?table=tblbt2tB67rjtzMr&view=vewisBm5UP#doxcnMncYOYLCPOVVy8h0YEIarh

      - alert: ENA_Bandwidth_Out_Allowance_Exceeded
        expr: |
          label_replace(
            rate(node_ethtool_bw_out_allowance_exceeded{}[2m]),
            "tenant_id", "$1", "tenant", "(.*)"
          ) > 100
        for: 1h
        labels:
          severity: warning
          component: kubernetes
          type: shoot
          tier: dedicated
          # visibility: region-tse
        annotations:
          message: "Device {{ $labels.device }} of Instance {{ $labels.instance }} in {{ $labels.cluster }} bandwith-out traffic exceeded"
          description: "Instance {{ $labels.instance }} from {{ $labels.cluster }} has a bandwidth out allowance greater than {{ $value }}"
          runbook_url: https://fx460onx10s.feishu.cn/wiki/Krjsw8orEilio1kfVPmcPvh7ncg?table=tblbt2tB67rjtzMr&view=vewisBm5UP#doxcniakPMvzDiQkI4yrKRvbVX6

      - alert: ENA_PPS_Allowance_Exceeded
        expr: |
          label_replace(
            rate(node_ethtool_pps_allowance_exceeded{}[2m]),
            "tenant_id", "$1", "tenant", "(.*)"
          ) > 0
        for: 30m
        labels:
          severity: major
          component: kubernetes
          type: shoot
          tier: dedicated
          visibility: region-tse
        annotations:
          message: "Device {{ $labels.device }} of Instance {{ $labels.instance }} in {{ $labels.cluster }} (PPS) allowance exceeded"
          description: "Packet Per Second (PPS) allowance for instance {{ $labels.instance }} in {{ $labels.cluster }} is greater than 0. pps_allowance_exceeded indicates number of packets queued or dropped due to Packet Per Second (PPS) exceeding the allowance for the instance. PPS allowance is enforced separately to the overall bandwidth allowance and, while the instance may still be under overall bandwidth allowance, the PPS allowance may exceed if the mean packet size is small."
          runbook_url: https://fx460onx10s.feishu.cn/wiki/Krjsw8orEilio1kfVPmcPvh7ncg?table=tblbt2tB67rjtzMr&view=vewisBm5UP#doxcnHVR8yeGECTW9WbU1Qsoy8d

      - alert: ENA_Conntrack_Allowance_Exceeded
        expr: |
          label_replace(
            rate(node_ethtool_conntrack_allowance_exceeded{}[2m]),
            "tenant_id", "$1", "tenant", "(.*)"
          ) > 0
        for: 30m
        labels:
          severity: major
          component: kubernetes
          type: shoot
          tier: dedicated
          visibility: region-tse
        annotations:
          message: "Device {{ $labels.device }} of Instance {{ $labels.instance }} in {{ $labels.cluster }} conntrack allowance exceeded"
          description: "conntrack_allowance_exceeded indicates the number of packets dropped due to exhaustion of tracked session allowance for the instance, new sessions will fail to establish once this allowance is exceeded."
          runbook_url: https://pingcap.feishu.cn/wiki/LCjYwa3gFiYvmdkNqG6cY7VenYd#DKXjdAJxUoMSS7x0KC0cyNBCnlb

      - alert: ENA_Linklocal_Allowance_Exceeded
        expr: |
          label_replace(
            rate(node_ethtool_linklocal_allowance_exceeded{}[2m]),
            "tenant_id", "$1", "tenant", "(.*)"
          ) > 0
        for: 30m
        labels:
          severity: major
          component: kubernetes
          type: shoot
          tier: dedicated
          visibility: region-tse
        annotations:
          message: "Device {{ $labels.device }} of Instance {{ $labels.instance }} in {{ $labels.cluster }} linklocal allowance exceeded"
          description: "linklocal_allowance_exceeded indicates the number of packets dropped due to PPS rate allowance exceeded for local services such as Route 53 DNS Resolver, Instance Metadata Service, Amazon Time Sync Service. This often points to suboptimal design choices or misconfiguration. This allowance is the same across instances."
          runbook_url: https://pingcap.feishu.cn/wiki/LCjYwa3gFiYvmdkNqG6cY7VenYd#DKXjdAJxUoMSS7x0KC0cyNBCnlb

      - alert: NodeHighNumberConntrackEntriesUsed
        annotations:
          description: '{{ $value | humanizePercentage }} of conntrack entries are used.'
          runbook_url: https://runbooks.prometheus-operator.dev/runbooks/node/nodehighnumberconntrackentriesused/
          summary: Number of conntrack are getting close to the limit.
        expr: |
          label_replace(
            (node_nf_conntrack_entries / node_nf_conntrack_entries_limit),
            "tenant_id", "$1", "tenant", "(.*)"
          ) > 0.7
        for: 10m
        labels:
          severity: major
          component: kubernetes
          type: shoot
          tier: dedicated
          visibility: region-tse
