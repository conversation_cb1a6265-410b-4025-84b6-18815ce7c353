groups:
- name: infra-exporter
  rules:
  - alert: ClusterPDStatusIsScalingMoreThan1h
    annotations:
      message: |
        The pd status of the cluster {{ $labels.exported_namespace }}/{{ $labels.name }} is scaling for more than 1h.
    expr: infra_exporter_cluster{pd_status="Scaling"} == 1
    for: 60m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: ClusterPDStatusIsUpgradingMoreThan1h
    annotations:
      message: |
        The pd status of the cluster {{ $labels.exported_namespace }}/{{ $labels.name }} is scaling for more than 1h.
    expr: infra_exporter_cluster{pd_status="Upgrading"} == 1
    for: 60m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: ClusterCRCreationDurationTooLong
    annotations:
      message: |
        The Cluster CR {{ $labels.exported_namespace }}/{{ $labels.name }} creation time exceeds 25 minutes.
    expr: infra_exporter_cluster{status=~"^$|Creating"} == 1
    for: 25m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: ClusterCRScalingDurationTooLong
    annotations:
      message: |
        Tenant {{ $labels.tenant_id }} 's Cluster CR {{ $labels.exported_namespace }}/{{ $labels.name }} scaling time exceeds 40 minutes.
        Reason: {{ $labels.reason }}
    expr: infra_exporter_cluster{status="Scaling"} == 1
    for: 40m
    labels:
      severity: major
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: ClusterCRKeepDeletingTooLong
    annotations:
      message: |
        The Cluster CR {{ $labels.exported_namespace }}/{{ $labels.name }} keeps being deleting time exceeds 30 minutes.
    expr: infra_exporter_cluster{status="Deleting"} == 1
    for: 30m
    labels:
      severity: major
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: ClusterCRKeepPausedTooLong
    annotations:
      message: |
        The Cluster CR {{ $labels.exported_namespace }}/{{ $labels.name }} keeps being paused time exceeds 30 minutes.
    expr: infra_exporter_cluster{paused="true"} == 1
    for: 30m
    labels:
      severity: major
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: ClusterCRKeepPausedMoreThan1Hour
    annotations:
      message: |
        The Cluster CR {{ $labels.exported_namespace }}/{{ $labels.name }} keeps being paused time exceeds 60 minutes. Do not forget to unpause it.
    expr: infra_exporter_cluster{paused="true"} == 1
    for: 1h
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: PrivateLinkServiceCreationDurationTooLong
    annotations:
      message: |
        The PirvateLink Service {{ $labels.exported_namespace }}/{{ $labels.name }} creation time exceeds 15 minutes.
    expr: infra_exporter_cluster{privatelinkserivce_status=~"^$|Creating"} == 1
    for: 15m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: PrivateLinkServiceKeepDeletingTooLong
    annotations:
      message: |
        The PirvateLink Service {{ $labels.exported_namespace }}/{{ $labels.name }} keeps being deleting time exceeds 15 minutes.
    expr: infra_exporter_cluster{privatelinkserivce_status="Deleting"} == 1
    for: 15m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  # Regional UpgradeOperation
  - alert: RegionalUpgradeOperationMissTheSchedule
    annotations:
      message: |
        The regional UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} does not start at the scheduled time.
    expr: regional_server_upgradeoperation_info{phase="Pending", miss_schedule="true",paused="false"} == 1
    for: 1m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: RegionalUpgradeOperationTimeout
    annotations:
      message: |
        The regional UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} exceeds the desired duration.
    expr: regional_server_upgradeoperation_info{phase="Running", timeout="true",paused="false"} == 1
    for: 1m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: RegionalUpgradeOperationFailed
    annotations:
      message: |
        The regional UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} failed.
    expr: regional_server_upgradeoperation_info{phase="Failed",paused="false"} == 1
    for: 1m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: RegionalUpgradeControlPlaneTimeout
    annotations:
      message: |
        The regional UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} upgrades control plane for over 32 minutes.
    expr: regional_server_upgradeoperation_info{phase="Running", current_target="controlplane", paused="false"} == 1
    for: 32m
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: RegionalUpgradeAdminNodesTimeout
    annotations:
      message: |
        The regional UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} upgrades admin nodes for over 30 minutes.
    expr: regional_server_upgradeoperation_info{phase="Running", current_target="admin-nodes", paused="false"} == 1
    for: 30m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: RegionalUpgradeEKSAddonsTimeout
    annotations:
      message: |
        The regional UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} upgrades eks add-ons for over 45 minutes.
    expr: regional_server_upgradeoperation_info{phase="Running", current_target="addons", paused="false"} == 1
    for: 45m
    labels:
      severity: critical
      component: dedicated-regional-server
  # Cluster UpgradeOperation
  - alert: UpgradeOperationMissTheSchedule
    annotations:
      message: |
        The UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} does not start at the scheduled time.
    expr: infra_exporter_upgradeoperation{phase="Pending", miss_schedule="true",paused="false"} == 1
    for: 1m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: UpgradeOperationTimeout
    annotations:
      message: |
        The UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} exceeds the desired duration.
    expr: infra_exporter_upgradeoperation{phase="Running", timeout="true",paused="false"} == 1
    for: 1m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: UpgradeOperationFailed
    annotations:
      message: |
        The UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} failed.
    expr: infra_exporter_upgradeoperation{phase="Failed",paused="false"} == 1
    for: 1m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: UpgradeEKSControlPlaneTimeout
    annotations:
      message: |
        The UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} upgrades eks control plane for over 15 minutes.
    expr: infra_exporter_upgradeoperation{phase="Running", current_target="controlplane",paused="false"} == 1
    for: 15m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: UpgradeGKEControlPlaneTimeout
    annotations:
      message: |
        The UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} upgrades gke control plane for over 32 minutes.
    expr: infra_exporter_upgradeoperation{phase="Running", current_target="controlplane",paused="false"} == 1
    for: 32m
    labels:
      severity: critical
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: UpgradeEKSAdminNodesTimeout
    annotations:
      message: |
        The UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} upgrades eks admin nodes for over 30 minutes.
    expr: infra_exporter_upgradeoperation{phase="Running", current_target="admin-nodes",paused="false"} == 1
    for: 30m
    labels:
      severity: major
      service: infra-provider
      component: infra-provider
      type: infra
  - alert: UpgradeTiDBNodesTimeout
    annotations:
      message: |
        The UpgradeOperation {{ $labels.exported_namespace }}/{{ $labels.name }} upgrades tidb nodes for over one hour.
    expr: infra_exporter_upgradeoperation{phase="Running", current_target="tidb-nodes",paused="false"} == 1
    for: 1h
    labels:
      severity: major
      service: infra-provider
      component: infra-provider
      type: infra
