groups:
  - name: prerelease-job-cluster-controller.rules
    rules:
      - alert: PrereleaseJobOfClusterControllerFailed
        expr: last_over_time(infra_controller_prerelease_job{controller="cluster", result="failed"}[30m])==1
        for: 0m
        labels:
          component: infra-provider
          service: infra-provider
          severity: major
          type: infra
        annotations:
          description: The prerelease jobs of cluster-controller in {{ $labels.region }} / {{ $labels.namespace }} failed.
