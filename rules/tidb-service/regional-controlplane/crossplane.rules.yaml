groups:
  - name: crossplane.rules
    rules:
    - alert: CrossplaneNotResponsiveAWS
      annotations:
        message: The AWS crossplane resource {{ $labels.name }} keep not responsive more than 15 mins.
      expr: |
        workqueue_depth{namespace="cluster-service", service="aws-provider-upstream-metric"}
        / on (name)
          sum by (name) (
          label_replace(
            rate(
              controller_runtime_reconcile_total{namespace="cluster-service",service="aws-provider-upstream-metric"}[5m]
            ),
            "name",
            "$1",
            "controller",
            "(.*)"
          )
        ) > 30
      # TODO reduce this time
      for: 15m
      labels:
        severity: major
        component: infra-provider
    - alert: CrossplaneNotResponsiveGCP
      annotations:
        message: The GCP crossplane resource {{ $labels.name }} keep not responsive more than 15 mins.
      expr: |
        workqueue_depth{namespace="cluster-service", service="gcp-provider-upstream-metric"}
        / on (name)
          sum by (name) (
          label_replace(
            rate(
              controller_runtime_reconcile_total{namespace="cluster-service",service="gcp-provider-upstream-metric"}[5m]
            ),
            "name",
            "$1",
            "controller",
            "(.*)"
          )
        ) > 30
      # TODO reduce this time
      for: 15m
      labels:
        severity: major
        component: infra-provider
