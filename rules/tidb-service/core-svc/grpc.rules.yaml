groups:
  - name: core-svc-grpc
    rules:
      - alert: TooManyRPCRequests
        expr: |
          sum(rate(grpc_server_handled_total{service=~"dbaas-core-portal-server-metrics|dbaas-core-svc-server-grpc"}[1m])) BY (grpc_service, grpc_method) 
            > 200
        for: 5m
        labels:
          severity: critical
          component: core-svc
        annotations:
          description: RPC QPS is {{ $value }}, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
          diagnosis: "click here to see dashboard: https://clinic.pingcap.com/grafana/d/QdHZ0jMVk/tiinf-grpc?orgId=1&var-kube_deployment=All&var-grpc_service={{ $labels.grpc_service }}&var-grpc_method={{ $labels.grpc_method }}"

      - alert: TooManyFailedRPCRequests
        expr: |
          (sum(rate(grpc_server_handled_total{service=~"dbaas-core-portal-server-metrics|dbaas-core-svc-server-grpc", grpc_code!="OK",grpc_method!~"IsDBUserExisting|DeleteDBUser|CreateDBUser|ExecuteCtlCommand"}[1m])) BY (grpc_service, grpc_method) 
            / sum(rate(grpc_server_handled_total{service=~"dbaas-core-portal-server-metrics|dbaas-core-svc-server-grpc"}[1m])) BY (grpc_service, grpc_method) 
          ) * 100  > 5
          and
            sum(rate(grpc_server_handled_total{service=~"dbaas-core-portal-server-metrics|dbaas-core-svc-server-grpc"}[1m])) BY (grpc_service, grpc_method)
                        > 1
        for: 5m
        labels:
          severity: critical
          component: core-svc
        annotations:
          description: RPC request failure rate is {{ printf "%.2f" $value }}%, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
          diagnosis: "click here to see dashboard: https://clinic.pingcap.com/grafana/d/QdHZ0jMVk/tiinf-grpc?orgId=1&var-kube_deployment=All&var-grpc_service={{ $labels.grpc_service }}&var-grpc_method={{ $labels.grpc_method }}"

      - alert: TooSlowRPCResponse
        expr: |
          histogram_quantile(0.99,
            sum(rate(grpc_server_handling_seconds_bucket{
              service=~"dbaas-core-portal-server-metrics|dbaas-core-svc-server-grpc",
              grpc_type="unary",
              grpc_method!~"UpsertProfileByYaml|SyncInfraProfile|CalcClusterFee|IsDBUserExisting|DeleteDBUser|CreateDBUser|ChangeClusterPasswd|UpdateAuditLogStatus|PrecheckAuditLogConfig|ExecuteCtlCommand"
          }[5m])) by (grpc_service, grpc_method, le)
          )
            > 4
        for: 5m
        labels:
          severity: critical
          component: core-svc
        annotations:
          description: RPC response latency P99 is {{ $value}}) seconds, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
          runbook_url: https://pingcap.feishu.cn/docx/Pc7KdBNGwo8EjjxbBNCcTsmlnAe#I6cxdcaAIo8i16xEgXRcpSXSnNc
          diagnosis: "click here to see dashboard: https://clinic.pingcap.com/grafana/d/QdHZ0jMVk/tiinf-grpc?orgId=1&var-kube_deployment=All&var-grpc_service={{ $labels.grpc_service }}&var-grpc_method={{ $labels.grpc_method }}"

      - alert: TooSlowRPCCall
        expr: |
          histogram_quantile(0.95,
            sum(rate(grpc_client_handling_seconds_bucket{
              pod=~"dbaas-core-.*", grpc_type="unary",
              grpc_method!~"UpsertProfileByYaml|SyncInfraProfile|CalcClusterFee|IsDBUserExisting|DeleteDBUser|CreateDBUser|ChangeClusterPasswd|ChangePassword|UpdateAuditLogStatus|PrecheckAuditLogConfig|ExecuteCtlCommand"
          }[5m])) by (grpc_service, grpc_method, le))
          > 2.5
        for: 5m
        labels:
          severity: major
          component: core-svc
        annotations:
          description: The P99 latency of gRPC call is {{ $value}}) seconds, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.

      - alert: TooSlowRPCCall
        expr: |
          histogram_quantile(0.95,
            sum(rate(grpc_client_handling_seconds_bucket{
              pod=~"dbaas-core-.*", grpc_type="unary",
              grpc_method!~"UpsertProfileByYaml|SyncInfraProfile|CalcClusterFee|IsDBUserExisting|DeleteDBUser|CreateDBUser|ChangeClusterPasswd|ChangePassword|UpdateAuditLogStatus|PrecheckAuditLogConfig|ExecuteCtlCommand"
          }[5m])) by (grpc_service, grpc_method, le))
          > 4
        for: 10m
        labels:
          severity: critical
          component: core-svc
        annotations:
          description: The P99 latency of gRPC call is {{ $value}}) seconds, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}.
