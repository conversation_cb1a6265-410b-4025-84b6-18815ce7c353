# all available metrics:
# {SERVICE_NAME}_eventbus_execution_time_seconds,
# {SERVICE_NAME}_eventbus_events_received_latency_seconds,
# {SERVICE_NAME}_eventbus_events_received_total,
# {SERVICE_NAME}_eventbus_events_published_total,
# {SERVICE_NAME}_eventbus_publish_time_seconds,
groups:
  - name: core-svc-eventbus
    rules:
      - alert: CoreSvcEventbusDLQ
        annotations:
          message: Event enter DLQ at core-svc Eventbus, topic is {{ $labels.event_type }}
          runbook_url: https://pingcap.feishu.cn/docx/Pc7KdBNGwo8EjjxbBNCcTsmlnAe#JL74duVggof44WxIbi1cZnmPnOg
        expr: |
          sum(increase(core_svc_eventbus_events_published_total{target="dlq" }[1m])) by (event_type) > 0
        for: 0m
        labels:
          severity: major
          component: core-svc
      - alert: CoreSvcEventbusPublishFailed
        annotations:
          message: too many failed publish, publisher is {{ $labels.event_type }}/{{ $labels.service }}
        expr: |
          sum(increase(core_svc_eventbus_events_published_total{success="false"}[1m])) by (event_type,service) > 0
          or sum(increase(provider_cost_eventbus_events_published_total{success="false"}[1m])) by (event_type,service) > 0
        for: 0m
        labels:
          severity: major
          component: core-svc
      - alert: CoreSvcConsumerIsTooSlow
        annotations:
          message: core-svc consumer is too slow, P95 is {{ $value }} seconds, consumer is {{ $labels.event_type }}/{{ $labels.group }}
        expr: |
          histogram_quantile(
             0.95, 
            sum(rate(core_svc_eventbus_execution_time_seconds_bucket{}[1m])) by (event_type,group,le)
          ) > 10
        for: 5m
        labels:
          severity: major
          component: core-svc
# this metrics is abnormal that its P10 value is also 60s.
#      - alert: CoreSvcConsumerLagIsTooLarge
#        annotations:
#          message: core-svc consumer's lag is too large, P95 is {{ %value }} seconds, consumer is {{ $labels.event_type }}/{{ $labels.group }}
#        expr: |
#          histogram_quantile(
#             0.95,
#            sum(rate(core_svc_eventbus_events_received_latency_seconds_bucket{}[1m])) by (event_type,group,le)
#          ) > 30
#        for: 5m
#        labels:
#          severity: major
#          component: core-svc
