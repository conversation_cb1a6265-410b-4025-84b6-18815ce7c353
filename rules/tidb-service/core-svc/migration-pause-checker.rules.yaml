groups:
  - name: migration-paused-checker
    rules:
      - alert: MigrationPausedChecker
        annotations:
          message: 3-layer arch migrated {{ $labels.pause_target }} CR is not in the PAUSED state. project_id is {{ $labels.project_id }}, cluster_id is {{ $labels.cluster_id }}.
        expr: |
          core_svc_migration_pause_checker_total{success="false"} > 1
        for: 0m
        labels:
          severity: critical
          component: core-svc
