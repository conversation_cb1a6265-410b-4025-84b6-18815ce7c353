# see https://pingcap.feishu.cn/docx/Qrpvd5NKOouE46xZXeJcQCwxnUf to get more detail
# all available metrics:
# core_svc_metering_meter_duration_seconds_bucket,core_svc_metering_meter_duration_seconds_sum,core_svc_metering_meter_duration_seconds_count
# core_svc_metering_meter_total
# core_svc_metering_meter_lag_seconds
# core_svc_metering_report_lag_seconds
groups:
  - name: core-svc-metering
    rules:
      - alert: ProjectMeterLagTooLarge
        annotations:
          message: the lag of project metering is {{ $value | humanizeDuration }} and more than 3.5 days
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          core_svc_metering_meter_lag_seconds{meter_type="project"} > 302400
        for: 0m
        labels:
          severity: major
          component: core-svc
      - alert: ProjectMeterLagTooLarge
        annotations:
          message: the lag of project metering is {{ $value | humanizeDuration }} and more than 5 days
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          core_svc_metering_meter_lag_seconds{meter_type="project"} > 432000
        for: 0m
        labels:
          severity: critical
          component: core-svc
      - alert: ClusterMeterLagTooLarge
        annotations:
          message: the lag of cluster metering is {{ $value | humanizeDuration }} and more than 15 minutes. cluster_id is {{ $labels.cluster_id }}
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          core_svc_metering_meter_lag_seconds{meter_type="cluster"} > 900
        for: 0m
        labels:
          severity: major
          component: core-svc
      - alert: ClusterMeterLagTooLarge
        annotations:
          message: the lag of cluster metering is {{ $value | humanizeDuration }} and more than 30 minutes. cluster_id is {{ $labels.cluster_id }}
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          core_svc_metering_meter_lag_seconds{meter_type="cluster"} > 1800
        for: 0m
        labels:
          severity: critical
          component: core-svc
      - alert: MeteringReportLagTooLarge
        annotations:
          message: the lag of metering report is {{ $value | humanizeDuration }} and more than 1 hour.
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          core_svc_metering_report_lag_seconds{} > 3600
        for: 0m
        labels:
          severity: major
          component: core-svc
      - alert: MeteringReportLagTooLarge
        annotations:
          message: the lag of metering report is {{ $value | humanizeDuration }} and more than 3 hours.
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          core_svc_metering_report_lag_seconds{} > 10800
        for: 0m
        labels:
          severity: critical
          component: core-svc

      - alert: ClusterMeterFailureRateTooHigh
        annotations:
          message: the failure rate of cluster metering is {{ $value | humanize }}. cluster_id is {{ $labels.cluster_id }}
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          (100-(sum(rate(core_svc_metering_meter_total{cluster_id!="0", meter_type="cluster", success="true"}[10m])) BY (cluster_id) 
            / sum(rate(core_svc_metering_meter_total{cluster_id!="0", meter_type="cluster"}[10m])) BY (cluster_id) 
          ) * 100) > 40
        for: 5m
        labels:
          severity: major
          component: core-svc
      - alert: ProjectMeterFailureRateTooHigh
        annotations:
          message: the failure rate of project metering is {{ $value | humanize }}. project_id is {{ $labels.project_id }}
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          (100-(sum(rate(core_svc_metering_meter_total{project_id!="0", meter_type="project", success="true"}[10h])) BY (cluster_id) 
            / sum(rate(core_svc_metering_meter_total{project_id!="0", meter_type="project"}[10h])) BY (cluster_id) 
          ) * 100) > 40
        for: 0m
        labels:
          severity: major
          component: core-svc

      - alert: ClusterMeterDurationP95TooLong
        annotations:
          message: the full cluster metering cost time is ${{ $value | humanizeDuration }} and more than 1 minute
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          histogram_quantile(0.95,
            sum(rate(core_svc_metering_meter_duration_seconds_bucket{
              cluster_id="0", meter_type="cluster"
          }[5m])) by (le)) > 60
        for: 3m
        labels:
          severity: major
          component: core-svc
      - alert: ProjectMeterDurationP95TooLong
        annotations:
          message: the full project metering cost time is ${{ $value | humanizeDuration }} and more than 120 seconds
          diagnosis: click https://pingcap-cn.feishu.cn/wiki/TuGLwHYEAip2LkkqsHDc50EEnye to get more info
        expr: |
          histogram_quantile(0.95,
            sum(rate(core_svc_metering_meter_duration_seconds_bucket{
              project_id="0", meter_type="project"
          }[2h])) by (le)) > 120
        for: 0m
        labels:
          severity: major
          component: core-svc
