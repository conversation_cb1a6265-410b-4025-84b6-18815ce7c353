groups:
  - name: core-svc
    rules:
      - alert: CoreSvcEventPendingTotal
        annotations:
          message: More than 10 events that have not yet been published in 5 consecutive minutes.
        expr: |
          core_svc_event_pending_total{} > 10
        for: 5m
        labels:
          severity: critical
          component: core-svc

      - alert: CoreSvcEventDelayDurationSeconds
        annotations:
          message: Publish event delayed by 60 seconds.
        expr: |
          core_svc_event_delay_duration_seconds{} > 60
        labels:
          severity: critical
          component: core-svc

      - alert: CoreSvcNoAvailableReplicas
        expr: kube_deployment_status_replicas_available{deployment=~"dbaas-core-svc-worker|dbaas-core-svc-server|dbaas-core-portal-server"}==0
        for: 5m
        labels:
          severity: critical
          component: core-svc
        annotations:
          description: There are no running core-svc pods.

      - alert: ControllerWorkqueueDepthTooLarge
        expr: workqueue_depth{job="dbaas-core-svc-worker-svc"} > 20
        for: 1h
        labels:
          component: core-svc
          service: core-svc
          severity: major
        annotations:
          description: "core-svc controller runtime workqueue depth too large. This will cause objects to be reconcile for a long time. see code in here https://github.com/tidbcloud/core-svc/blob/master/svc/core_ctx/infra/controller/watcher_infraapi.go#107"

      - alert: ClusterDeletingDurationMoreThan3h
        annotations:
          message: The TiDB Cluster {{ $labels.tenant_name }}/{{ $labels.name }} deleting duration more than 3h may be stuck in final-backup
          runbook_url: https://pingcap.feishu.cn/docx/Pc7KdBNGwo8EjjxbBNCcTsmlnAe#M72JdtCZgogRRHx7sJOcAT2knWh
          diagnosis: 'click here to see log: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Loki-aws-us-west-2","queries":[{"refId":"A","expr":"{app=\"dbaas-core-svc-worker\"} |= \"{{ $labels.cluster_id }}\"","queryType":"range","editorMode":"code"}],"range":{"from":"now-15m","to":"now"}}'
        expr: |
          dbaas_tidb_cluster_info{status="deleted", dev_tier="false"} == 1
        for: 3h
        labels:
          severity: major
          component: core-svc

      - alert: ClusterDeletingDurationMoreThan24h
        annotations:
          message: The TiDB Cluster {{ $labels.tenant_name }}/{{ $labels.name }} deleting duration more than 24h may be stuck in final-backup
          runbook_url: https://pingcap.feishu.cn/docx/Pc7KdBNGwo8EjjxbBNCcTsmlnAe#N2RRd4h8WopYMSxGOxFcalKLnfg
          diagnosis: 'click here to see log: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Loki-aws-us-west-2","queries":[{"refId":"A","expr":"{app=\"dbaas-core-svc-worker\"} |= \"{{ $labels.cluster_id }}\"","queryType":"range","editorMode":"code"}],"range":{"from":"now-15m","to":"now"}}'
        expr: |
          dbaas_tidb_cluster_info{status="deleted", dev_tier="false"} == 1
        for: 24h
        labels:
          severity: critical
          component: core-svc
