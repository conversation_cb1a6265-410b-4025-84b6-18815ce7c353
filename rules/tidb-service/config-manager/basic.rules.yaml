groups:
  - name: config-manager-basic
    rules:
      - alert: GoroutineContinuouslyIncrease
        annotations:
          message: Goroutine of pod {{ $labels.pod }} is continuous increasing
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"go_goroutines{pod=\"{{ $labels.pod }}\"}","legendFormat":"__auto","range":true,"instant":true}],"range":{"from":"now-1h","to":"now"}}'
        expr: |
          avg(delta(go_goroutines{pod=~"config-manager.*"}[5m])) by (pod)
          > 10
        for: 10m
        labels:
          service: infra-provider
          severity: major
          component: config-manager

      - alert: GoroutineContinuouslyIncreaseNumThan100
        annotations:
          message: Goroutine of pod {{ $labels.pod }} is continuous increasing
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"go_goroutines{pod=\"{{ $labels.pod }}\"}","legendFormat":"__auto","range":true,"instant":true}],"range":{"from":"now-1h","to":"now"}}'
        expr: |
          avg(delta(go_goroutines{pod=~"config-manager.*"}[5m])) by (pod)
          > 100
        for: 5m
        labels:
          service: infra-provider
          severity: major
          component: config-manager

      - alert: GoroutineCountIsTooHigh
        annotations:
          message: Goroutine of pod {{ $labels.pod }} is more than 1000
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"go_goroutines{pod=\"{{ $labels.pod }}\"}","legendFormat":"__auto","range":true,"instant":true}],"range":{"from":"now-1h","to":"now"}}'
        expr: |
          go_goroutines{pod=~"config-manager.*"}
          > 1000
        for: 5m
        labels:
          service: infra-provider
          severity: major
          component: config-manager

      - alert: CPUUsageIsTooHighThan40PercentFor5m
        annotations:
          message: CPU utilization of pod {{ $labels.pod }} is more than 40% for 5m.
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"(sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{ pod=\"{{ $labels.pod }}\"}) by (pod)/sum(kube_pod_container_resource_limits{resource=\"cpu\", pod=\"{{ $labels.pod }}\"}) by (pod)) *100","legendFormat":"__auto","range":true,"instant":true,"hide":false}],"range":{"from":"now-1h","to":"now"}}'
        expr: |
          (sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{pod=~"config-manager.*"}) by (namespace,pod)/
            sum(kube_pod_container_resource_limits{resource="cpu",pod=~"config-manager.*"}) by (namespace,pod)
          ) *100 > 40
        for: 5m
        labels:
          service: infra-provider
          severity: major
          component: config-manager

      - alert: CPUUsageIsTooHighThan50PercentFor10m
        annotations:
          message: CPU utilization of pod {{ $labels.pod }} is more than 50% for 10m.
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"(sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{ pod=\"{{ $labels.pod }}\"}) by (pod)/sum(kube_pod_container_resource_limits{resource=\"cpu\", pod=\"{{ $labels.pod }}\"}) by (pod)) *100","legendFormat":"__auto","range":true,"instant":true,"hide":false}],"range":{"from":"now-1h","to":"now"}}'
        expr: |
          (sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{pod=~"config-manager.*"}) by (namespace,pod)/
            sum(kube_pod_container_resource_limits{resource="cpu",pod=~"config-manager.*"}) by (namespace,pod)
          ) *100 > 50
        for: 10m
        labels:
          service: infra-provider
          severity: major
          component: config-manager

      - alert: MemUsageIsTooHigh
        annotations:
          message: Memory utilization of pod {{ $labels.pod }} is more than 60% for 5m.
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"(sum(container_memory_working_set_bytes{container!=\"POD\", container!=\"\", image!=\"\", pod=\"{{ $labels.pod }}\"})by(pod)/sum(kube_pod_container_resource_limits{resource=\"memory\", pod=\"{{ $labels.pod }}\"}) by (pod) * 100)","legendFormat":"__auto","range":true,"instant":true,"hide":false}],"range":{"from":"now-1h","to":"now"}}'
        expr: |
          (sum(container_memory_working_set_bytes{pod=~"config-manager.*",container!="POD", container!="", image!=""})by(pod)/
          sum(kube_pod_container_resource_limits{resource="memory",pod=~"config-manager.*"}) by (pod)
          * 100) > 60
        for: 5m
        labels:
          service: infra-provider
          severity: major
          component: config-manager

      - alert: NoAvailableReplicas
        expr: kube_deployment_status_replicas_available{deployment=~"config-manager.*"}==0
        for: 5m
        labels:
          service: infra-provider
          severity: critical
          component: config-manager
        annotations:
          summary: There are no running config-manager pods.
