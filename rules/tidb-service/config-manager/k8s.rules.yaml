groups:
  - name: confing-manager-k8s
    rules:
      - alert: ControllerWorkqueueDepthTooLarge
        expr: workqueue_depth{pod=~"config-manager.*"} > 20
        for: 1h
        labels:
          component: config-manager
          service: infra-provider
          severity: major
        annotations:
          summary: "config-manager controller runtime workqueue depth too large."
          message: "Current depth is {{ $value }} "
      - alert: ControllerWorkqueueDepthTooLarge
        expr: workqueue_depth{pod=~"config-manager.*"} > 200
        for: 10m
        labels:
          component: config-manager
          service: infra-provider
          severity: critical
        annotations:
          summary: "config-manager controller runtime workqueue depth too large."
          message: "Current depth is {{ $value }} "

