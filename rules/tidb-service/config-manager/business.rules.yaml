groups:
  # After config-manager was migrated to regional, we can remove all rules in the file
  - name: config-manager-business
    rules:
      - alert: MergedPRNumByCommitIsInvalid
        expr: infra_cluster_pr_num_by_commit{} != 1
        for: 1m
        labels:
          component: config-manager
          service: infra-provider
          severity: critical
          type: infra
        annotations:
          description: The number({{ $value }}) of merged PR associated with a commit(  {{ $labels.commit_id }} ) is invalid, cluster_id {{ $labels.cluster_id }}

      - alert: ConfigChangeFailed
        expr: infra_exporter_configchange{phase="Failed"} == 1
        for: 1m
        labels:
          component: infra-provider
          service: infra-provider
          severity: critical
          type: infra
        annotations:
          description: The ConfigChange {{ $labels.exported_namespace }}/{{ $labels.name }} failed.
          diagnosis: https://pingcap.feishu.cn/wiki/wikcnRe6t2VlsB14p9drxXexdRQ#doxcnqhILMwGQ1WaXANJA0d6Kme
      - alert: ConfigChangeDurationTooLong
        expr: |
          label_replace(dbaas_tidb_cluster_info{status!~"paused|pausing|resuming"}, "cluster_name", "$1", "cluster_id", "(.*)")
          * on (cluster_name) group_left() infra_exporter_configchange{phase!~"Completed|Failed"} == 1
        for: 2h
        labels:
          component: infra-provider
          service: infra-provider
          severity: critical
          type: infra
        annotations:
          description: The ConfigChange {{ $labels.exported_namespace }}/{{ $labels.name }} runs too long than 1 hours.
          diagnosis: https://pingcap.feishu.cn/wiki/wikcnRe6t2VlsB14p9drxXexdRQ#doxcn8xnrJwhbjniWVDeuXOZFjh
