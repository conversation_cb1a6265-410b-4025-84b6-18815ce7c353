groups:
  - name: infra-pods.rules
    rules:
      - alert: <PERSON>bePod<PERSON>rashLooping
        annotations:
          message: Pod {{ $labels.namespace }}/{{ $labels.pod }} in the base cluster is restarting {{ printf "%.2f" $value }} times in 1 hours.
          runbook_url: https://github.com/tidbcloud/runbooks/wiki/KubePodCrashLooping
        expr: |
          increase(kube_pod_container_status_restarts_total{namespace="infra", pod!~"^dp-.*"}[1h]) > 5
        for: 1m
        labels:
          service: infra-provider
          component: infra-provider
          type: infra
          severity: critical
