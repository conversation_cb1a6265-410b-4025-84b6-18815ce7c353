groups:
- name: aws-quota-exporter
  rules:
  - alert: AWSCloudQuotaUsageThreshold
    annotations:
      message: |
        The AWS usage for cloud resource {{ $labels.resource }} of account {{ $labels.aws_account }} has reached 75% of its quota, current is {{ printf "%.0f" $value }}%.
        Check https://pingcap.feishu.cn/docx/doxcnN9EDMLNDVNuca9iwNJzItt#doxcnoEsEeaW8iWSSa2PUbmc9Ed
    expr: (awsaccount_quota_usage{}/awsaccount_quota_limit{}) * 100 > 75 and awsaccount_quota_limit{} > 0  and awsaccount_quota_usage{} <= awsaccount_quota_limit{}
    for: 1m
    labels:
      severity: major
      component: quota-exporter
  - alert: AWSCloudQuotaUsageThreshold
    annotations:
      message: |
        The AWS usage for cloud resource {{ $labels.resource }} of account {{ $labels.aws_account }} has reached 85% or more of its quota, current is {{ printf "%.0f" $value }}%.
        Check https://pingcap.feishu.cn/docx/doxcnN9EDMLNDVNuca9iwNJzItt#doxcnoEsEeaW8iWSSa2PUbmc9Ed
    expr: (awsaccount_quota_usage{} / awsaccount_quota_limit{}) * 100 > 85 and awsaccount_quota_limit{} > 0 and awsaccount_quota_usage{} <= awsaccount_quota_limit{}
    for: 1m
    labels:
      severity: critical
      component: quota-exporter
  - alert: AWSCloudRolesQuotaUsageThreshold
    annotations:
      message: |
        The AWS usage for roles of account {{ $labels.aws_account }} has reached 85% or more of its quota, current is {{ printf "%.0f" $value }}%.
        Check https://pingcap.feishu.cn/docx/doxcnN9EDMLNDVNuca9iwNJzItt#doxcnoEsEeaW8iWSSa2PUbmc9Ed
    expr: (awsaccount_quota_usage{resource="Roles"}/awsaccount_quota_limit{resource="Roles"}) * 100 > 85 and awsaccount_quota_limit{resource="Roles"} > 0  and awsaccount_quota_usage{} <= awsaccount_quota_limit{}
    for: 5m
    labels:
      severity: critical
      component: quota-exporter
