groups:
  - name: infra-aws-provider.rules
    rules:
      - alert: InfraAwsProviderNoAvailableReplicas
        expr: kube_deployment_status_replicas_available{deployment="aws-provider", namespace="infra"}==0
        for: 5m
        labels:
          component: infra-provider
          service: infra-aws-provider
          severity: critical
          type: infra-api

        annotations:
          description: There are no running infra-aws-provider pods.
  - name: infra-gcp-provider.rules
    rules:
      - alert: InfraGcpProviderNoAvailableReplicas
        expr: kube_deployment_status_replicas_available{deployment="gcp-provider", namespace="infra"}==0
        for: 5m
        labels:
          component: infra-provider
          service: infra-gcp-provider
          severity: critical
          type: infra-api

        annotations:
          description: There are no running infra-gcp-provider pods.
  - name: infra-provider.rules
    rules:
      - alert: InfraProviderWorkqueueDepthTooLarge
        expr: workqueue_depth{job="infra-provider-metric"} > 50
        for: 1h
        labels:
          component: infra-provider
          service: infra-provider
          severity: major
          type: infra
        annotations:
          description: Infra provider controller runtime workqueue depth too large. This will cause objects to be reconcile for a long time
