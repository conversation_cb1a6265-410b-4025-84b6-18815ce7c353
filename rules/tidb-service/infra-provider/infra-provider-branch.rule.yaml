groups:
  - name: infra-provider-branch
    rules:
      # adjust the threshold after test
      - alert: ProviderBranchReconcileError
        expr: |
          sum(increase(controller_runtime_reconcile_errors_total{controller="Branch"}[1m])) > 0
        labels:
          tier: serverless
          severity: warning
          component: ecosystem-server
          provider_type: aws-free-tier
        annotations:
          description: Branch reconcile error in {{ $labels.region }}.
