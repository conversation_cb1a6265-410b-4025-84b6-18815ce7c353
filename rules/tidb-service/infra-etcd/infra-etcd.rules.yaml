groups:
  - name: etcd
    rules:
      - alert: InfraEtcdMembersDown
        annotations:
          summary: "infra-etcd cluster {{ $labels.job }}: members are down ({{ $value }})."
        expr: |-
          max by (job) (
            sum by (job) (up{job=~".*etcd.*", namespace="infra"} == bool 0)
          or
            count by (job,endpoint) (
              sum by (job,endpoint,To) (rate(etcd_network_peer_sent_failures_total{job=~".*etcd.*",namespace="infra"}[3m])) > 0.01
            )
          )
          > 0
        for: 3m
        labels:
          component: infra-api
          severity: critical
      - alert: InfraEtcdInsufficientMembers
        annotations:
          summary: "infra-etcd cluster {{ $labels.job }}: insufficient members ({{ $value }})."
        expr: sum(up{job=~".*etcd.*", namespace="infra"} == bool 1) by (job) < ((count(up{job=~".*etcd.*", namespace="infra"}) by (job) + 1) / 2)
        for: 3m
        labels:
          component: infra-api
          severity: critical
      - alert: InfraEtcdNoLeader
        annotations:
          summary: "infra-etcd cluster {{ $labels.job }}: member {{ $labels.instance }} has no leader."
        expr: etcd_server_has_leader{job=~".*etcd.*", namespace="infra"} == 0
        for: 1m
        labels:
          component: infra-api
          severity: critical
      # skip rule validation
      # ignore_validations: rateBeforeAggregation
      - alert: InfraEtcdHighNumberOfLeaderChanges
        annotations:
          summary: "infra-etcd cluster {{ $labels.job }}: {{ $value }} leader changes within the last 15 minutes. Frequent elections may be a sign of insufficient resources, high network latency, or disruptions by other components and should be investigated."
        expr: increase((max by (job) (etcd_server_leader_changes_seen_total{job=~".*etcd.*", namespace="infra"}) or 0*absent(etcd_server_leader_changes_seen_total{job=~".*etcd.*", namespace="infra"}))[15m:1m]) >= 3
        for: 5m
        labels:
          component: infra-api
          severity: major
      - alert: InfraEtcdGRPCRequestsSlow
        annotations:
          summary: "infra-etcd cluster {{ $labels.job }}: gRPC requests to {{ $labels.grpc_method }} are taking {{ $value }}s on etcd instance {{ $labels.instance }}."
        expr: |-
          histogram_quantile(0.99, sum(rate(grpc_server_handling_seconds_bucket{job=~".*etcd.*", grpc_type="unary", namespace="infra"}[5m])) by (job, instance, grpc_service, grpc_method, le))
          > 0.15
        for: 10m
        labels:
          component: infra-api
          severity: critical
      - alert: InfraEtcdMemberCommunicationSlow
        annotations:
          summary: "infra-etcd cluster {{ $labels.job }}: member communication with {{ $labels.To }} is taking {{ $value }}s on etcd instance {{ $labels.instance }}."
        expr: |-
          histogram_quantile(0.99, rate(etcd_network_peer_round_trip_time_seconds_bucket{job=~".*etcd.*", namespace="infra"}[5m]))
          > 0.15
        for: 10m
        labels:
          component: infra-api
          severity: major
      - alert: InfraEtcdHighNumberOfFailedProposals
        annotations:
          summary: "infra-etcd cluster {{ $labels.job }}: {{ $value }} proposal failures within the last 30 minutes on etcd instance {{ $labels.instance }}."
        expr: rate(etcd_server_proposals_failed_total{job=~".*etcd.*", namespace="infra"}[15m]) > 5
        for: 15m
        labels:
          component: infra-api
          severity: major
      - alert: InfraEtcdHighFsyncDurations
        annotations:
          summary: "Infra-etcd cluster {{ $labels.job }}: 99th percentile fync durations are {{ $value }}s on etcd instance {{ $labels.instance }}."
        expr: |-
          histogram_quantile(0.99, rate(etcd_disk_wal_fsync_duration_seconds_bucket{job=~".*etcd.*", namespace="infra"}[5m]))
          > 0.5
        for: 10m
        labels:
          component: infra-api
          severity: major
      - alert: InfraEtcdHighCommitDurations
        annotations:
          summary: "infra-etcd cluster {{ $labels.job }}: 99th percentile commit durations {{ $value }} on etcd instance {{ $labels.instance }}."
        expr: |-
          histogram_quantile(0.99, rate(etcd_disk_backend_commit_duration_seconds_bucket{job=~".*etcd.*", namespace="infra"}[5m]))
          > 0.25
        for: 10m
        labels:
          component: infra-api
          severity: major
      - alert: InfraEtcdHighNumberOfFailedHTTPRequests
        annotations:
          summary: "{{ $value }}% of requests for {{ $labels.method }} failed on etcd instance {{ $labels.instance }}"
        expr: |-
          sum(rate(etcd_http_failed_total{job=~".*etcd.*", code!="404", namespace="infra"}[5m])) BY (method) / sum(rate(etcd_http_received_total{job=~".*etcd.*", namespace="infra"}[5m]))
          BY (method) > 0.01
        for: 10m
        labels:
          component: infra-api
          severity: major
      - alert: InfraEtcdHighNumberOfFailedHTTPRequests
        annotations:
          summary: "{{ $value }}% of requests for {{ $labels.method }} failed on infra-etcd instance {{ $labels.instance }}."
        expr: |-
          sum(rate(etcd_http_failed_total{job=~".*etcd.*", code!="404", namespace="infra"}[5m])) BY (method) / sum(rate(etcd_http_received_total{job=~".*etcd.*", namespace="infra"}[5m]))
          BY (method) > 0.05
        for: 10m
        labels:
          component: infra-api
          severity: critical
      - alert: InfraEtcdHTTPRequestsSlow
        annotations:
          summary: "infra etcd instance {{ $labels.instance }} HTTP requests to {{ $labels.method }} are slow."
        expr: |-
          histogram_quantile(0.99, rate(etcd_http_successful_duration_seconds_bucket{namespace="infra"}[5m]))
          > 0.15
        for: 10m
        labels:
          component: infra-api
          severity: major
