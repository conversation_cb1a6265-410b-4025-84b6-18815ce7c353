groups:
  - name: manager.rules
    rules:
      - alert: AwsSharedTierProviderFailToRequestApiserver
        expr: increase(rest_client_requests_total{job="manager",code=~"5.."}[5m]) > 10
        for: 5m
        labels:
          component: shared-tier-provider
          severity: critical
          type: infra-api
          provider_type: aws-shared-tier
        annotations:
          description: API requests from provider under kubernetes {{ $labels.cluster }} to apiserver {{ $labels.host }} is failing.
