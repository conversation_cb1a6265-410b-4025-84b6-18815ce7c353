groups:
  - name: infra-apiserver
    rules:
      - alert: InfraApiserverDown
        annotations:
          description: infra-apiserver is down
        expr: up{job="api-server", namespace="infra"} == 0
        for: 5m
        labels:
          component: infra-api
          service: infra-apiserver
          severity: critical
      - alert: InfraApiserverCPUThrottlingHigh
        expr: |-
          sum(increase(container_cpu_cfs_throttled_periods_total{namespace="infra", container="kube-apiserver"}[5m])) by (pod, namespace)
            /
          sum(increase(container_cpu_cfs_periods_total{namespace="infra", container="kube-apiserver"}[5m])) by (pod, namespace)
            > 0.6
        for: 5m
        labels:
          component: infra-api
          service: infra-apiserver
          severity: major
        annotations:
          message: "Global Infra API Server Pod {{ $labels.namespace }}/{{ $labels.pod }} experience elevated CPU throttling"
      - alert: InfraApiserverMemoryUsageHigh
        expr: |-
          sum(container_memory_working_set_bytes{cluster="base/eks/us-west-2", namespace="infra",container="kube-apiserver",control_plane_info="base/aws-us-west-2"}) by (pod) 
            /
          sum(kube_pod_container_resource_limits{cluster="base/eks/us-west-2", namespace="infra",container="kube-apiserver",control_plane_info="base/aws-us-west-2"}) by (pod)
            > 0.8
        for: 10m
        labels:
          component: infra-api
          service: infra-apiserver
          severity: major
        annotations:
          message: "Global Infra API Server Pod {{ $labels.namespace }}/{{ $labels.pod }} memory usage is high"
