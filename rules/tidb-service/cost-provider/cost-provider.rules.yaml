
# TODO: we already add metrics for cost-provider-server in https://github.com/tidbcloud/dbaas/pull/3526,
# we can add alerts by cost-provider-server metrics endpoint in the next release
   
groups:
- name: cost-provider-server
  rules:
  - alert: CostProviderDown
    annotations:
      message: Cost provider has disappeared from Prometheus target discovery.
    expr: |
       sum(kube_pod_status_ready{pod=~"cost-provider-server-.*",condition="true"})==0
    for: 10m
    labels:
      severity: critical
      component: cost-provider-server