validationRules:
  - name: check-severity-label
    scope: Alert
    validations:
      - type: hasLabels
        params:
          labels: ["severity"]
      - type: labelHasAllowedValue
        params:
          label: "severity"
          allowedValues:
            - "warning"
            - "major"
            - "Major"
            - "critical"
            - "emergency"
  - name: check-component-label
    scope: Alert
    validations:
      - type: hasLabels
        params:
          labels: ["component"]

  - name: check-groups
    scope: Group
    validations:
      - type: hasAllowedEvaluationInterval
        params:
          minimum: "10s"
      # - type: maxRulesPerGroup
      #   params:
      #     limit: 100
