groups:
- name: tso
  rules:
  - alert: TSOServerDowntimeIsTooLong
    expr: up{component="tso", job=~".*tso.*"} == 0
    for: 10m
    labels:
      severity: critical
      tier: next-gen
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'TSO server downtime is more than 10 min - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, downtime value: {{ $value }}'
      value: '{{ $value }}'
      summary: TSO server downtime is more than 10 min
  - alert: TSOServerFrequentlyRestart
    expr: sum(changes(floor(process_start_time_seconds{job=~".*tso.*"})[1h:15s])) by (cluster_id, instance, tenant,provider_type) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'TSO server restart count is more than 3 times in 1 hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      value: '{{ $value }}'
      summary: TSO server restarted {{ $value }} times in past an hour
  - alert: TSOClusterFrequentlyRestart
    expr: |-
      sum by (cluster_id, tenant, provider_type) (
          resets((-floor(process_start_time_seconds{job=~".*tso.*"}))[1h:15s])
        and
          max_over_time(process_start_time_seconds{job=~".*tso.*"}[30m] offset 1h) > 0
      ) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'TSO cluster restart count is more than 3 times in 1 hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      value: '{{ $value }}'
      summary: TSO servers in a cluster restarted {{ $value }} times in past an hour
  # Disable the following rule for ci check
  # ignore_validations: expressionDoesNotUseRangeShorterThan
  - alert: TSOPrimaryChange
    expr: |
      changes(service_member_role{service="TSO Service Group 0"}[15s]) > 0
      unless on(instance)
      (
        (
          sum(
            count_over_time(
              pd_service_maxprocs{component=~".*tso.*"}[30s]
            )
          ) by (instance)
          or on(instance)
          0 * pd_service_maxprocs{component=~".*tso.*"}
        ) <= 1
      )
    labels:
      severity: major
      tier: next-gen
      component: pd
    annotations:
      message: 'TSO primary changes, might cause performance degrade - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, change count: {{ $value }}'
      summary: TSO primary changes, might cause performance degrade.
  - alert: TSOServerGenerateTSOFailed
    expr: increase(pd_tso_events{type="exceeded_max_retry"}[10m]) > 0
    labels:
      severity: major
      expr: increase(pd_tso_events{type="exceeded_max_retry""}[10m]) > 0
      tier: next-gen
      component: pd
    annotations:
      message: 'TSO server generate tso failed - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, failure count: {{ $value }}'
      summary: TSO server generate tso failed
