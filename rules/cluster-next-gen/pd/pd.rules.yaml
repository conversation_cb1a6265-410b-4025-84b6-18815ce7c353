groups:
- name: pd
  rules:
  - alert: PDServerDowntimeIsTooLong
    expr: up{component="pd", job=~".*pd.*"} == 0
    for: 10m
    labels:
      severity: critical
      expr: up{component="pd", job=~".*pd.*"} == 0
      tier: next-gen
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      message: 'PD server downtime is more than 10 min - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, downtime value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.f5wwrkav7k5f
      value: '{{ $value }}'
      summary: PD server downtime is more than 10 min
  - alert: PDServerFrequentlyRestart
    expr: sum(changes(floor(process_start_time_seconds{job=~".*pd.*"})[1h:15s])) by (cluster_id, instance, tenant,provider_type) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'PD server restart count is more than 3 times in 1 hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.u233t02connq
      value: '{{ $value }}'
      summary: PD server restarted {{ $value }} times in past an hour
  - alert: PDClusterFrequentlyRestart
    expr: |-
      sum by (cluster_id, tenant, provider_type) (
          resets((-floor(process_start_time_seconds{job=~".*pd.*"}))[1h:15s])
        and
          max_over_time(process_start_time_seconds{job=~".*pd.*"}[30m] offset 1h) > 0
      ) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'PD cluster restart count is more than 3 times in 1 hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.u233t02connq
      value: '{{ $value }}'
      summary: PD servers in a cluster restarted {{ $value }} times in past an hour
  - alert: PDRegionDownPeerTooLong
    expr: histogram_quantile(0.99, sum(rate(pd_regions_abnormal_peer_duration_seconds_bucket{type="down-peer"}[5m])) by (le, type, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
    for: 5m
    labels:
      severity: warning
      tier: next-gen
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'PD found some region has down peer too long duration - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, duration: {{ $value }}s'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.2boq1zqje9s
      value: '{{ $value }}'
      summary: "PD found some region has down peer too long duration"
  - alert: PDRegionMissPeerTooLong
    expr: histogram_quantile(0.99, sum(rate(pd_regions_abnormal_peer_duration_seconds_bucket{type="miss-voter-peer"}[5m])) by (le, type, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
    for: 5m
    labels:
      severity: major
      tier: next-gen
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'PD found some region has miss voter peer too long duration - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, duration: {{ $value }}s'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.my2gfezc47ow
      value: '{{ $value }}'
      summary: "PD found some region has miss votor peer too long duration"
  - alert: TiKVRegionPendingPeerTooLong
    expr: histogram_quantile(0.99, sum(rate(tikv_raftstore_peer_pending_duration_seconds_bucket[1m])) BY (le, instance, cluster_id, tenant, provider_type)) > (60 * 60 * 2)
    for: 5m
    labels:
      severity: major
      tier: next-gen
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'TiKV found some region has pending peer too long duration - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, duration: {{ $value }}s'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.lizm9t5f8fpu
      value: '{{ $value }}'
      summary: "TiKV found some region has pending peer too long duration"
  - alert: EtcdNodeTermLagTooLong
    expr: (pd_server_etcd_state{type="term"} or on() vector(0)) < max(pd_server_etcd_state{type="term"})
    for: 2m
    labels:
      severity: warning
      tier: next-gen
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'Some PD instance term is lagged too long - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, term lag: {{ $value }}'
      value: '{{ $value }}'
      summary: some PD instance term is lagged too long
  # Disable the following rule for ci check
  # ignore_validations: expressionDoesNotUseRangeShorterThan
  - alert: PDLeaderChange
    expr: |
      changes(service_member_role{service="PD"}[15s]) > 0
      unless on(instance)
      (
        (
          sum(
            count_over_time(
              pd_service_maxprocs{component=~".*pd.*"}[30s]
            )
          ) by (instance)
          or on(instance)
          0 * pd_service_maxprocs{component=~".*pd.*"}
        ) <= 1
      )
    labels:
      severity: major
      tier: next-gen
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'PD leader changes, might cause performance degrade - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, change count: {{ $value }}'
      value: '{{ $value }}'
      summary: pd leader changes, might cause performance degrade.
