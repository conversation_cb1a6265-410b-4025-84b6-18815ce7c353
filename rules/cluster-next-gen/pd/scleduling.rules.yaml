groups:
- name: scheduling
  rules:
  - alert: SchedulingServerDowntimeIsTooLong
    expr: up{component="scheduling", job=~".*schedul.*"} == 0
    for: 10m
    labels:
      severity: critical
      expr: up{component="scheduling", job=~".*schedul.*"} == 0
      tier: next-gen
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'Scheduling server downtime is more than 10 min - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, downtime value: {{ $value }}'
      value: '{{ $value }}'
      summary: Scheduling server downtime is more than 10 min
  - alert: SchedulingServerFrequentlyRestart
    expr: sum(changes(floor(process_start_time_seconds{job=~".*schedul.*"})[1h:15s])) by (cluster_id, instance, tenant,provider_type) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      expr: sum(changes(floor(process_start_time_seconds{job=~".*schedul.*"})[1h:15s])) by (cluster_id, instance, tenant,provider_type) >= 3
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'Scheduling server restart count is more than 3 times in 1 hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      value: '{{ $value }}'
      summary: Scheduling server restarted {{ $value }} times in past an hour
  - alert: SchedulingClusterFrequentlyRestart
    expr: |-
      sum by (cluster_id, tenant, provider_type) (
          resets((-floor(process_start_time_seconds{job=~".*schedul.*"}))[1h:15s])
        and
          max_over_time(process_start_time_seconds{job=~".*schedul.*"}[30m] offset 1h) > 0
      ) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'Scheduling cluster restart count is more than 3 times in 1 hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      value: '{{ $value }}'
      summary: Scheduling servers in a cluster restarted {{ $value }} times in past an hour
  - alert: SchedulingServerDiscoverDownStore
    expr: (sum ( pd_cluster_status{type="store_down_count"} ) by (cluster_id, instance, tenant, provider_type) > 0) and (sum(service_member_role{service="Scheduling Service"}) by (cluster_id, instance, tenant, provider_type) > 0)
    for: 5m
    labels:
      severity: critical
      tier: next-gen
      expr:  (sum ( pd_cluster_status{type="store_down_count"} ) by (cluster_id, instance, tenant, provider_type) > 0) and (sum(service_member_role{service="Scheduling Service"}) by (cluster_id, instance, tenant, provider_type) > 0)
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'Scheduling server found store is down - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, down store count: {{ $value }}'
      value: '{{ $value }}'
      summary: "Scheduling server found store is down"
  - alert: SchedulingServerDiscoverLowSpaceStore
    expr: (sum ( pd_cluster_status{type="store_low_space_count"} ) by (cluster_id, instance, tenant, provider_type) > 0) and (sum(service_member_role{service="Scheduling Service"}) by (cluster_id, instance, tenant, provider_type) > 0)
    for: 5m
    labels:
      severity: warning
      tier: next-gen
      expr:  (sum ( pd_cluster_status{type="store_low_space_count"} ) by (cluster_id, instance, tenant, provider_type) > 0) and (sum(service_member_role{service="Scheduling Service"}) by (cluster_id, instance, tenant, provider_type) > 0)
      component: pd
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      message: 'Scheduling server found some store space is too low - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, low space store count: {{ $value }}'
      value: '{{ $value }}'
      summary: "Scheduling server found some store space is too low"
