groups:
- name: tikv
  rules:
  - alert: TiKVRestartedUnexpectedly
    expr: (rate(kube_pod_container_status_restarts_total{container="tikv"}[5m])>0) * ignoring (reason) group_left(reason) (kube_pod_container_status_last_terminated_reason{reason!='Completed', container="tikv"}==1)
    labels:
      severity: major
      expr: (rate(kube_pod_container_status_restarts_total{container="tikv"}[5m])>0) * ignoring (reason) group_left(reason) (kube_pod_container_status_last_terminated_reason{reason!='Completed', container="tikv"}==1)
      component: tikv
      tier: next-gen
    annotations:
      message: 'tikv container under cluster: {{ $labels.cluster }}, {{ $labels.namespace }}/{{ $labels.pod }} have restarted due to container {{ $labels.reason }}'
      summary: tikv container just restarted unexpectedly
