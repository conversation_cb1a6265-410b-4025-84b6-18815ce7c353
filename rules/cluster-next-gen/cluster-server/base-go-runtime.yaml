groups:
  - name: csng.go-runtime
    rules:
      - alert: GoroutineContinuouslyIncrease
        annotations:
          message: Goroutine of pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} is continuous increasing
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left=%7B%22datasource%22%3A%22Ak6VpZD4z%22%2C%22queries%22%3A%5B%7B%22refId%22%3A%22A%22%2C%22editorMode%22%3A%22code%22%2C%22expr%22%3A%22go_goroutines%7Bpod%3D%5C%22%7B%7B%20%24labels.pod%20%7D%7D%5C%22%7D%22%2C%22legendFormat%22%3A%22__auto%22%2C%22range%22%3Atrue%2C%22instant%22%3Atrue%7D%5D%2C%22range%22%3A%7B%22from%22%3A%22now-1h%22%2C%22to%22%3A%22now%22%7D%7D'
        expr: |
          avg(increase(go_goroutines{namespace=~"cluster-ng-service"}[3m])) by (pod)
          > 500
        for: 3m
        labels:
          severity: major
          component: cluster-ng-service

      - alert: GoroutineCountIsTooHigh
        annotations:
          message: Goroutine of pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} is more than 1000
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left=%7B%22datasource%22%3A%22Ak6VpZD4z%22%2C%22queries%22%3A%5B%7B%22refId%22%3A%22A%22%2C%22editorMode%22%3A%22code%22%2C%22expr%22%3A%22go_goroutines%7Bpod%3D%5C%22%7B%7B%20%24labels.pod%20%7D%7D%5C%22%7D%22%2C%22legendFormat%22%3A%22__auto%22%2C%22range%22%3Atrue%2C%22instant%22%3Atrue%7D%5D%2C%22range%22%3A%7B%22from%22%3A%22now-1h%22%2C%22to%22%3A%22now%22%7D%7D'
        expr: |
          avg(go_goroutines{namespace=~"cluster-ng-service"}) by (pod)
          > 1000
        for: 5m
        labels:
          severity: major
          component: cluster-ng-service
