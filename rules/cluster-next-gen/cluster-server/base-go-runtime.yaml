groups:
  - name: csng.go-runtime
    rules:
      - alert: GoroutineContinuouslyIncrease
        annotations:
          message: Goroutine of pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} is continuous increasing
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"go_goroutines{pod=\"{{ $labels.pod }}\"}","legendFormat":"__auto","range":true,"instant":true}],"range":{"from":"now-1h","to":"now"}}'
        expr: |
          avg(increase(go_goroutines{namespace=~"cluster-ng-service"}[3m])) by (pod)
          > 500
        for: 3m
        labels:
          severity: major
          component: cluster-ng-service

      - alert: GoroutineCountIsTooHigh
        annotations:
          message: Goroutine of pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} is more than 1000
          diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"go_goroutines{pod=\"{{ $labels.pod }}\"}","legendFormat":"__auto","range":true,"instant":true}],"range":{"from":"now-1h","to":"now"}}'
        expr: |
          avg(go_goroutines{namespace=~"cluster-ng-service"}) by (pod)
          > 1000
        for: 5m
        labels:
          severity: major
          component: cluster-ng-service
