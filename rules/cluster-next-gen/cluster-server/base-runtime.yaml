groups:
  - name: csng.runtime
    rules:
      - alert: ExecutorTaskTooManyErrors
        annotations:
          message: Runtime Executor task {{ $labels.task_name }} of {{ $labels.executor_name }}/{{ $labels.action_name }} on region {{ $labels.tidb_cluster_id }} has too many errors.
        expr: |
           sum(increase(runtime_executor_dag_action_duration_seconds_count{result="error", action_name!~"clean-up-cert|pre-create-tidbgroup"}[5m])) by (executor_name, action_name, task_name) > 25
        for: 1m
        labels:
          severity: critical
          component: cluster-ng-service

      - alert: ExecutorAsyncTaskRunningTooLong
        annotations:
          message: Runtime Executor task {{ $labels.task_name }} of {{ $labels.executor_name }}/{{ $labels.action_name }} on region {{ $labels.tidb_cluster_id }} is running too long.
        expr: |
          avg by (executor_name, action_name, task_name) (runtime_executor_dag_action_duration_seconds_sum{action_name=~"pre-create-tidbcluster|clean-up-cert|pre-create-tidbgroup|clear-dataplane|create-dataplane"}) > 2400
        for: 1m
        labels:
          severity: critical
          component: cluster-ng-service

      - alert: ExecutorTaskRunningTooLong
        annotations:
          message: Runtime Executor task {{ $labels.task_name }} of {{ $labels.executor_name }}/{{ $labels.action_name }} on region {{ $labels.tidb_cluster_id }} is running too long.
        expr: |
          avg by (executor_name, action_name, task_name) (runtime_executor_dag_action_duration_seconds_sum{action_name!~"pre-create-tidbcluster|clean-up-cert|pre-create-tidbgroup|clear-dataplane|create-dataplane"}) > 600
        for: 1m
        labels:
          severity: critical
          component: cluster-ng-service

          
