groups:
  - name: csng.grpc
    rules:
      - alert: gRPCServerErrorRate > 10%
        expr: |
          (
            sum by (grpc_service, grpc_method, job) (
              rate(
                (
                  grpc_server_handled_total{grpc_code!="OK",namespace=~"cluster-ng-service"}
                  unless
                  grpc_server_handled_total{grpc_code="NotFound",grpc_method="GetTiDBGroup",namespace=~"cluster-ng-service"}
                  unless
                  grpc_server_handled_total{grpc_code="FailedPrecondition",grpc_method="DeleteTiDBCluster",namespace=~"cluster-ng-service"}
                  unless
                  grpc_server_handled_total{grpc_code="NotFound",grpc_method="GetPrivateLinkService",namespace=~"cluster-ng-service"}
                )[5m:]
              )
            )
            /
            sum by (grpc_service, grpc_method, job) (
              rate(
                grpc_server_handled_total{namespace=~"cluster-ng-service"}[5m]
              )
            )
          ) * 100
          > 10
        for: 3m
        labels:
          severity: critical
          component: cluster-ng-service
        annotations:
          message: gRPC server on region {{ $labels.tidb_cluster_id }} failure rate is {{ printf "%.2f" $value }}%, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}, component={{ $labels.job }}.
      - alert: gRPCServerErrorRate > 5%
        expr: |
          (
            sum by (grpc_service, grpc_method, job) (
              rate(
                (
                  grpc_server_handled_total{grpc_code!="OK",namespace=~"cluster-ng-service"}
                  unless
                  grpc_server_handled_total{grpc_code="NotFound",grpc_method="GetTiDBGroup",namespace=~"cluster-ng-service"}
                  unless
                  grpc_server_handled_total{grpc_code="FailedPrecondition",grpc_method="DeleteTiDBCluster",namespace=~"cluster-ng-service"}
                  unless
                  grpc_server_handled_total{grpc_code="NotFound",grpc_method="GetPrivateLinkService",namespace=~"cluster-ng-service"}
                )[5m:]
              )
            )
            /
            sum by (grpc_service, grpc_method, job) (
              rate(
                grpc_server_handled_total{namespace=~"cluster-ng-service"}[5m]
              )
            )
          ) * 100
          > 5
        for: 3m
        labels:
          severity: major
          component: cluster-ng-service
        annotations:
          message: gRPC server on region {{ $labels.tidb_cluster_id }} failure rate is {{ printf "%.2f" $value }}%, method={{ $labels.grpc_service }}/{{ $labels.grpc_method }}, component={{ $labels.job }}.
  