groups:
- name: tiflash
  rules:
  #- alert: TiFlashServerDowntimeIsTooLong
  #  expr: up{component=".*tiflash"} == 0 and max_over_time(up{component=".*tiflash", job=~".*tiflash.*"}[100m] offset 10m) > 0
  #  for: 10m
  #  labels:
  #    tier: next-gen
  #    severity: major
  #    expr: up{component=".*tiflash"} == 0
  #    component: tiflash
  #  annotations:
  #    message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
  #    runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ig9wflpfgm34
  #    value: '{{ $value }}'
  #    summary: TiFlash server downtime is more than 5 min
  - alert: TiFlashServerDowntimeIsTooLong
    expr: up{component=".*tiflash"} == 0
    for: 10m
    labels:
      tier: next-gen
      severity: critical
      expr: up{component=".*tiflash"} == 0
      component: tiflash
    annotations:
      message: 'TiFlash server downtime is more than 10 min - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, downtime value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ig9wflpfgm34
      value: '{{ $value }}'
      summary: TiFlash server downtime is more than 10 min
  - alert: TiFlashServerFrequentlyRestart
    expr: sum(changes(floor(tiflash_proxy_process_start_time_seconds{job=~".*tiflash.*"})[1h:15s])) by (cluster_id, instance, tenant, provider_type) >=3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      expr: sum(changes(tiflash_proxy_process_start_time_seconds{job=~".*tiflash.*"}[1h])) by (cluster_id, instance, tenant, provider_type) >=3
      component: tiflash
    annotations:
      message: 'TiFlash server is down for at least 3 times in a hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ig9wflpfgm34
      value: '{{ $value }}'
      summary: TiFlash server is down for at least 3 times in a hour
  - alert: TiFlashClusterFrequentlyRestart
    expr: |-
      sum by (cluster_id, tenant, provider_type) (
          resets((-floor(tiflash_proxy_process_start_time_seconds{job=~".*tiflash.*"}))[1h:15s])
        and
          max_over_time(tiflash_proxy_process_start_time_seconds{job=~".*tiflash.*"}[30m] offset 1h) > 0
      ) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      component: tiflash
    annotations:
      message: 'TiFlash servers in cluster are down for at least 3 times in a hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ig9wflpfgm34
      value: '{{ $value }}'
      summary: TiFlash servers in cluster are down for at least 3 times in a hour
  - alert: TiFlashServerRestarted
    expr: sum(changes(floor(tiflash_proxy_process_start_time_seconds{job=~".*tiflash.*"})[1h:15s])) by (cluster_id, instance, tenant, provider_type) >=1 and sum(max_over_time(up{job=~".*tiflash.*"}[100m] offset 60m)) by (cluster_id, instance, tenant, provider_type) > 0
    for: 10m
    labels:
      tier: next-gen
      severity: warning
      expr: sum(changes(floor(tiflash_proxy_process_start_time_seconds{job=~".*tiflash.*"})[1h:15s])) by (cluster_id, instance, tenant, provider_type) >=1 and sum(max_over_time(up{job=~".*tiflash.*"}[100m] offset 60m)) by (cluster_id, instance, tenant, provider_type) > 0
      component: tiflash
    annotations:
      message: 'TiFlash server is down for at least 1 times in a hour - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, restart count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ig9wflpfgm34
      value: '{{ $value }}'
      summary: TiFlash server is down for at least 1 times in a hour
  - alert: TiFlashLowSpace
    expr: |
      sum(tiflash_system_current_metric_StoreSizeAvailable) by (cluster_id, instance, tenant, provider_type) 
      / sum(tiflash_system_current_metric_StoreSizeCapacity) by (cluster_id, instance, tenant, provider_type) * 100 
      < 20
    for: 5m
    labels:
      severity: warning
      tier: next-gen
      expr: sum(tiflash_system_current_metric_StoreSizeAvailable) by (cluster_id, instance, tenant,provider_type) /  sum(tiflash_system_current_metric_StoreSizeCapacity) by (cluster_id, instance, tenant,provider_type) * 100 < 20
      component: tiflash
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.jsri2yl8n8dp
      value: '{{ $value }}'
      summary: "TiFlash store space used more than 80%"

#  alert: TiFlashLowSpaceForWetech move to rules/next-gen/swat/data-plane/kernel-tcocp.yaml

  - alert: TiFlashVeryLowSpace
    expr: |
      sum(tiflash_system_current_metric_StoreSizeAvailable) by (cluster_id, instance, tenant, provider_type) 
      / sum(tiflash_system_current_metric_StoreSizeCapacity) by (cluster_id, instance, tenant,provider_type) * 100 
      < 10
    for: 5m
    labels:
      severity: major
      tier: next-gen
      expr: sum(tiflash_system_current_metric_StoreSizeAvailable) by (cluster_id, instance, tenant, provider_type) /  sum(tiflash_system_current_metric_StoreSizeCapacity) by (cluster_id, instance, tenant,provider_type) * 100 < 10
      component: tiflash
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.skuuhxdpaymf
      value: '{{ $value }}'
      summary: "TiFlash store space used more than 90%"
  - alert: TiFlashInodeUsageMoreThan90%
    expr: |
      kubelet_volume_stats_inodes_used{component=".*tiflash"}/kubelet_volume_stats_inodes{component=".*tiflash"} > 0.9
    for: 5m
    labels:
      severity: critical
      tier: next-gen
      component: tiflash
    annotations:
      message: 'inode usage more than 90%, cluster: {{ $labels.tidb_cluster }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      value: '{{ $value }}'
      runbook_url: https://pingcap.feishu.cn/wiki/ZqMDwonIYi1o91kbk7AcOBvTnig
      summary: "TiFlash inode usage more than 90%"
