# TODO: we already add metrics for dbaas-proxy in https://github.com/tidbcloud/dbaas/pull/3526,
# we can add alerts by dbaas-proxy metrics endpoint in the next release
groups:
- name: dbaas-proxy.rules
  rules:
  - alert: DBaaSProxyNoAvailableReplicas
    expr: kube_deployment_status_replicas_available{deployment="dbaas-proxy"}==0
    for: 15m
    labels:
      service: shoot-dbaas-proxy
      severity: critical
      type: shoot
      tier: next-gen
      component: dbaas-proxy
    annotations:
      message: There are no running dbaas proxy pods.
