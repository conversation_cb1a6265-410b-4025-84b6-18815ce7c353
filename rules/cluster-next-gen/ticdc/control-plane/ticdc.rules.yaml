groups:
  - name: ticdc
    rules:
      - alert: TiCDCPodsUnhealthyPhase
        expr: kube_pod_status_phase{phase!="Running", pod=~".*-ticdc-.*"} == 1
        for: 15m
        labels:
          severity: critical
          component: ticdc
        annotations:
          message: >
            TiCDC pod {{ $labels.namespace }}/{{ $labels.pod }} in {{ $labels.cluster }} 
            has been in {{ $labels.phase }} phase for longer than 15 minutes
