groups:
- name: tidb
  rules:
  - alert: TiDBServerRestartedUnexpectedly
    expr: (rate(kube_pod_container_status_restarts_total{container="tidb"}[5m])>0) * ignoring (reason) group_left(reason) (kube_pod_container_status_last_terminated_reason{reason!='Completed', container="tidb"}==1)
    labels:
      severity: major
      expr: (rate(kube_pod_container_status_restarts_total{container="tidb"}[5m])>0) * ignoring (reason) group_left(reason) (kube_pod_container_status_last_terminated_reason{reason!='Completed', container="tidb"}==1)
      component: tidb
      tier: dedicated
    annotations:
      message: 'tidb container under cluster: {{ $labels.cluster }}, {{ $labels.namespace }}/{{ $labels.pod }} have restarted due to container {{ $labels.reason }}'
      summary: tidb container just restarted unexpectedly
