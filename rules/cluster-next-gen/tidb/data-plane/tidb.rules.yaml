groups:
- name: tidb
  rules:
  - alert: TiDBServerDowntimeIsTooLong
    expr: up{component="tidb", job=~".*tidb.*"} == 0
    for: 10m
    labels:
      tier: next-gen
      severity: critical
      expr: up{component="tidb", job=~".*tidb"} == 0
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }} is down for 10 mins'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.tfuldltfya4a
      summary: TiDB server downtime is more than 10 min
  - alert: TiDBWorkerDowntimeIsTooLong
    expr: up{component="worker-tidb", job=~".*tidb.*"} == 0
    for: 10m
    labels:
      tier: next-gen
      severity: critical
      expr: up{component="worker-tidb", job=~".*tidb.*"} == 0
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }} is down for 10 mins'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.tfuldltfya4a
      summary: TiDB server downtime is more than 10 min
  #- alert: TiDBServerDowntimeIsTooLong
  #  expr: up{component=~"tidb|worker-tidb", job=~".*tidb"} == 0
  #  for: 5m
  #  labels:
  #    tier: next-gen
  #    severity: major
  #    expr: up{component=~"tidb|worker-tidb", job=~".*tidb"} == 0
  #    component: tidb
  #  annotations:
  #    message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }} is down for 5 mins'
  #    runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.tfuldltfya4a
  #    summary: TiDB server downtime is more than 5 min
  - alert: TiDBDiscoveredTimeJumpBack
    expr: increase(tidb_monitor_time_jump_back_total[10m])  > 0
    for: 5m
    labels:
      tier: next-gen
      severity: warning
      expr: increase(tidb_monitor_time_jump_back_total[10m])  > 0
      component: tidb
    annotations:
      message: 'TiDB monitor found time jump back error - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, jump count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ph7vof1otr6g
      summary: TiDB monitor found time jump back error
  - alert: TiDBHighTokenUsage
    expr: sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type) > 0 and (sum(tidb_server_tokens{}) by (cluster_id, instance, tenant,provider_type) / sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type)) > 0.8
    for: 5m
    labels:
      tier: next-gen
      severity: warning
      expr: sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type) > 0 and (sum(tidb_server_tokens{}) by (cluster_id, instance, tenant,provider_type) / sum(tidb_config_status{type="token-limit"}) by (cluster_id, instance, tenant,provider_type)) > 0.8
      component: tidb
    annotations:
      message: 'TiDB token usage too high - cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, token usage ratio: {{ $value }}'
      value: '{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.es82zlkd14z
      summary: TiDB token usage too high
  - alert: TiDBQueryUnexpectedlyFailed
    expr: increase(tidb_server_execute_error_total{type=~".*8027|.*8120|.*8021|.*8024|.*9001|.*9002|.*9003|.*9011|.*9012|.*9013|unknown"}[5m]) > 1
    for: 5m
    labels:
      tier: next-gen
      severity: warning
      expr: increase(tidb_server_execute_error_total{type=~".*8027|.*8120|.*8021|.*8024|.*9001|.*9002|.*9003|.*9011|.*9012|.*9013|unknown"}[5m]) > 1
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, error type: {{ $labels.type }}, count: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.vr9n074q793p
      summary: TiDB query got unexpected error
  - alert: TiDBServerFrequentlyRestart
    expr: sum(changes(floor(process_start_time_seconds{job=~".*tidb"})[1h:15s])) by (cluster_id, instance, tenant,provider_type) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      expr: sum(changes(floor(process_start_time_seconds{job=~".*tidb"})[1h:15s])) by (cluster_id, instance, tenant,provider_type) >= 3
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.u233t02connq
      summary: TiDB server restarted {{ $value }} times in past an hour
  - alert: TiDBClusterFrequentlyRestart
    expr: |-
      sum by (cluster_id, tenant, provider_type) (
          resets((-floor(process_start_time_seconds{job=~".*tidb"}))[1h:15s])
        and
          max_over_time(process_start_time_seconds{job=~".*tidb"}[30m] offset 1h) > 0
      ) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.u233t02connq
      summary: TiDB servers in a cluster restarted {{ $value }} times in past an hour
  - alert: TiDBServerStatsUnhealthy
    expr: avg(tidb_statistics_stats_healthy{type="[0,50)"}) by (cluster_id, instance, tenant, provider_type) / avg(tidb_statistics_stats_healthy{type="[0,100]"}) by (cluster_id, instance, tenant, provider_type) > 0.5
    for: 1d
    labels:
      tier: next-gen
      severity: warning
      expr: avg(tidb_statistics_stats_healthy{type="[0,50)"}) by (cluster_id, instance, tenant, provider_type) / avg(tidb_statistics_stats_healthy{type="[0,100]"}) by (cluster_id, instance, tenant, provider_type) > 0.5
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.k4nn0mporild
      summary: More than half of tables's statisitics TiDB server are in bad health for one day
  - alert: MaxTiDBMemoryUsageTooHighInternal
    expr: |-
      1 - min by (tidb_cluster_id) (
        avg by (instance, tidb_cluster_id) (
          (
            node_memory_MemFree_bytes{component=~"tidb|worker-tidb"} + 
            node_memory_Buffers_bytes{component=~"tidb|worker-tidb"} + 
            node_memory_Cached_bytes{component=~"tidb|worker-tidb"}
          ) 
          / 
          node_memory_MemTotal_bytes{component=~"tidb|worker-tidb"}
        )
      ) > 0.7
    for: 10m
    labels:
      tier: next-gen
      severity: warning
      expr: |-
        1 - min by (tidb_cluster_id) (
          avg by (instance, tidb_cluster_id) (
            (
              node_memory_MemFree_bytes{component=~"tidb|worker-tidb"} +
              node_memory_Buffers_bytes{component=~"tidb|worker-tidb"} +
              node_memory_Cached_bytes{component=~"tidb|worker-tidb"}
            ) 
            / 
            node_memory_MemTotal_bytes{component=~"tidb|worker-tidb"}
          )
        ) > 0.7
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.k4nn0mporild
      summary: Max memory utilization across TiDB nodes exceeded 70% for 10 minutes
  - alert: MaxTiDBCPUUsageTooHighInternal
    expr: max(avg(sum by (cpu, instance, tidb_cluster_id) (irate(node_cpu_seconds_total{component=~"tidb|worker-tidb", mode!~"idle"}[2m]))) by (instance, tidb_cluster_id))by (tidb_cluster_id) > 0.8
    for: 10m
    labels:
      tier: next-gen
      severity: warning
      expr: max(avg(sum by (cpu, instance, tidb_cluster_id) (irate(node_cpu_seconds_total{component=~"tidb|worker-tidb", mode!~"idle"}[2m]))) by (instance, tidb_cluster_id))by (tidb_cluster_id) > 0.8
      component: tidb
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.k4nn0mporild
      summary: Max CPU utilization across TiDB nodes exceeded 80% for 10 minutes
