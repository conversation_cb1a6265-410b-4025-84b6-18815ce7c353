groups:
- name: dataplane-manager-resource-operations
  rules:
  - alert: DataplaneCreationDurationTooLong
    annotations:
      message: The Dataplane Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 20 minutes
    expr: |
      max(dataplane_manager_dataplane_info{service="dataplane-controller", status=~"Pending|ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 20m
    labels:
      severity: major
      component: dataplane-controller
  - alert: DataplaneCreationDurationTooLong
    annotations:
      message: The Dataplane Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 30 minutes
    expr: |
      max(dataplane_manager_dataplane_info{service="dataplane-controller", status=~"Pending|ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 30m
    labels:
      severity: critical
      component: dataplane-controller
  - alert: DataplaneDeletionDurationTooLong
    annotations:
      message: The Dataplane Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 30 minutes
    expr: |
      max(dataplane_manager_dataplane_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 30m
    labels:
      severity: major
      component: dataplane-controller
  - alert: DataplaneDeletionDurationTooLong
    annotations:
      message: The Dataplane Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 24 hours
    expr: |
      max(dataplane_manager_dataplane_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 24h
    labels:
      severity: critical
      component: dataplane-controller
  - alert: VPCCreationDurationTooLong 
    annotations:
      message: The VPC Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 5 minutes
    expr: |
      max(dataplane_manager_vpc_info{service="dataplane-controller", status=~"Pending|ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 5m
    labels:
      severity: major
      component: dataplane-controller
  - alert: VPCCreationDurationTooLong
    annotations:
      message: The VPC Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 10 minutes
    expr: |
      max(dataplane_manager_vpc_info{service="dataplane-controller", status=~"Pending|ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 10m
    labels:
      severity: critical
      component: dataplane-controller
  - alert: VPCDeletionDurationTooLong
    annotations:
      message: The VPC Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 15 minutes
    expr: |
      max(dataplane_manager_vpc_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 15m
    labels:
      severity: major
      component: dataplane-controller
  - alert: InternetResourceCreationDurationTooLong
    annotations:
      message: The Internet Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 10 minutes
    expr: |
      max(dataplane_manager_internet_info{service="dataplane-controller", status=~"ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 10m
    labels:
      severity: major
      component: dataplane-controller
  - alert: InternetResourceCreationDurationTooLong
    annotations:
      message: The Internet Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 20 minutes
    expr: |
      max(dataplane_manager_internet_info{service="dataplane-controller", status=~"ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 20m
    labels:
      severity: critical
      component: dataplane-controller
  - alert: InternetResourceDeletionDurationTooLong
    annotations:
      message: The Internet Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 15 minutes
    expr: |
      max(dataplane_manager_internet_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 15m
    labels:
      severity: major
      component: dataplane-controller
  - alert: InternetResourceDeletionDurationTooLong
    annotations:
      message: The Internet Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 24 hours
    expr: |
      max(dataplane_manager_internet_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 24h
    labels:
      severity: critical
      component: dataplane-controller
  - alert: K8sCreationDurationTooLong
    annotations:
      message: K8s Resource(APPBox CR) {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 20 minutes
    expr: |
      max(dataplane_manager_appbox_info{service="dataplane-controller", status=~"ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 20m
    labels:
      severity: major
      component: dataplane-controller
  - alert: K8sCreationDurationTooLong
    annotations:
      message: K8s Resource(APPBox CR) {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 30 minutes
    expr: |
      max(dataplane_manager_appbox_info{service="dataplane-controller", status=~"ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 30m
    labels:
      severity: critical
      component: dataplane-controller
  - alert: K8sDeletionDurationTooLong
    annotations:
      message: K8s Resource(APPBox CR) {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 30 minutes
    expr: |
      max(dataplane_manager_appbox_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 30m
    labels:
      severity: major
      component: dataplane-controller
  - alert: K8sDeletionDurationTooLong
    annotations:
      message: K8s Resource(APPBox CR) {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 24 hours
    expr: |
      max(dataplane_manager_appbox_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 24h
    labels:
      severity: critical
      component: dataplane-controller
  - alert: PrivateLinkServiceCreationDurationTooLong
    annotations:
      message: The Private Link Service Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 15 minutes
    expr: |
      max(dataplane_manager_privatelinkservice_info{service="dataplane-controller", status=~"Pending|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 15m
    labels:
      severity: major
      component: dataplane-controller
  - alert: PrivateLinkServiceCreationDurationTooLong
    annotations:
      message: The Private Link Service Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 30 minutes
    expr: |
      max(dataplane_manager_privatelinkservice_info{service="dataplane-controller", status=~"Pending|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 30m
    labels:
      severity: critical
      component: dataplane-controller
  - alert: PrivateLinkServiceDeletionDurationTooLong
    annotations:
      message: The Private Link Service Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 20 minutes
    expr: |
      max(dataplane_manager_privatelinkservice_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 20m
    labels:
      severity: major
      component: dataplane-controller
  - alert: LoadBalancerCreationDurationTooLong
    annotations:
      message: The Load Balancer Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 10 minutes
    expr: |
      max(dataplane_manager_loadbalancer_info{service="dataplane-controller", status=~"Pending|ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 10m
    labels:
      severity: major
      component: dataplane-controller
  - alert: LoadBalancerCreationDurationTooLong
    annotations:
      message: The Load Balancer Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} creation time exceeds 20 minutes
    expr: |
      max(dataplane_manager_loadbalancer_info{service="dataplane-controller", status=~"Pending|ReadyForCreate|Creating|Failed"})by(name, region, tenant_id) >= 1
    for: 20m
    labels:
      severity: critical
      component: dataplane-controller
  - alert: LoadBalancerDeletionDurationTooLong
    annotations:
      message: The Load Balancer Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 15 minutes
    expr: |
      max(dataplane_manager_loadbalancer_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 15m
    labels:
      severity: major
      component: dataplane-controller
  - alert: LoadBalancerDeletionDurationTooLong
    annotations:
      message: The Load Balancer Resource {{ $labels.name }} in {{ $labels.tidb_cluster_id }}/{{ $labels.tenant_id }} deletion time exceeds 24 hours
    expr: |
      max(dataplane_manager_loadbalancer_info{service="dataplane-controller", status=~"Deleting"})by(name, region, tenant_id) >= 1
    for: 24h
    labels:
      severity: critical
      component: dataplane-controller
