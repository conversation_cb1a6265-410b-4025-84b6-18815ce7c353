groups:
- name: csi-driver.rules
  rules:
  - alert: <PERSON><PERSON><PERSON>od<PERSON>rashLooping
    annotations:
      message: Pod {{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container
        }}) is restarting {{ printf "%.2f" $value }} times in 1 hours.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepodcrashlooping
    expr: |
      increase(kube_pod_container_status_restarts_total{pod=~"csi-driver-.*"}[1h]) > 5
    for: 15m
    labels:
      component: csi-driver
      service: csi-driver
      severity: major
      type: shoot
      tier: next-gen

  - alert: KubePodNotReady
    annotations:
      message: Pod {{ $labels.namespace }}/{{ $labels.pod }} has been in a non-ready
        state for longer than 15 minutes.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepodnotready
    expr: |
      sum by (namespace, pod) (max by(namespace, pod) (kube_pod_status_phase{job="kube-state-metrics", phase=~"Pending|Unknown", pod=~"csi-driver-.*"}) * on(namespace, pod) group_left(owner_kind) max by(namespace, pod, owner_kind) (kube_pod_owner{owner_kind!="Job"})) > 0
    for: 15m
    labels:
      component: csi-driver
      service: csi-driver
      severity: major
      type: shoot
      tier: next-gen

