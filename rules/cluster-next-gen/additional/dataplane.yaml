groups:
- name: csngdataplane.rules
  rules:
  - alert: NextGenPersistentVolumeFillingUp
    annotations:
      message: The PersistentVolume claimed by {{ $labels.persistentvolumeclaim
        }} in Namespace {{ $labels.namespace }} on region {{ $labels.tidb_cluster_id }} is only {{ $value | humanizePercentage
        }} free.
      runbook_url: https://github.com/tidbcloud/runbooks/wiki/KubePersistentVolumeFillingUp
    expr: |
      kubelet_volume_stats_available_bytes{persistentvolumeclaim=~".*-db-.*", namespace=~"tidb.*"} /
      kubelet_volume_stats_capacity_bytes{persistentvolumeclaim=~".*-db-.*", namespace=~"tidb.*"} < 0.3
    for: 1m
    labels:
      component: cluster-ng-service
      severity: critical

  - alert: NextGenCPUUsageIsTooHigh
    annotations:
      message: CPU utilization of pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} is more than 90%.
      diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left=%7B%22datasource%22%3A%22Ak6VpZD4z%22%2C%22queries%22%3A%5B%7B%22refId%22%3A%22A%22%2C%22editorMode%22%3A%22code%22%2C%22expr%22%3A%22%28sum%28node_namespace_pod_container%3Acontainer_cpu_usage_seconds_total%3Asum_rate%7B%20pod%3D%5C%22%7B%7B%20%24labels.pod%20%7D%7D%5C%22%7D%29%20by%20%28pod%29%2Fsum%28kube_pod_container_resource_limits%7Bresource%3D%5C%22cpu%5C%22%2C%20pod%3D%5C%22%7B%7B%20%24labels.pod%20%7D%7D%5C%22%7D%29%20by%20%28pod%29%29%20%2A100%22%2C%22legendFormat%22%3A%22__auto%22%2C%22range%22%3Atrue%2C%22instant%22%3Atrue%2C%22hide%22%3Afalse%7D%5D%2C%22range%22%3A%7B%22from%22%3A%22now-1h%22%2C%22to%22%3A%22now%22%7D%7D'
    expr: |
      (sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{namespace=~"tidb.*"}) by (namespace,pod)/
        sum(kube_pod_container_resource_limits{resource="cpu",namespace=~"tidb.*"}) by (namespace,pod)
      ) *100 > 90
    for: 3m
    labels:
      severity: critical
      component: cluster-ng-service

  - alert: NextGenMemUsageIsTooHigh
    annotations:
      message: Memory utilization of pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} is more than 90%.
      diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={\"datasource\":\"Ak6VpZD4z\",\"queries\":[{\"refId\":\"A\",\"editorMode\":\"code\",\"expr\":\"(sum(container_memory_working_set_bytes{container!=\\\"POD\\\", container!=\\\"\\\", image!=\\\"\\\", pod=\\\"{{ $labels.pod }}\\\"})by(pod)/sum(kube_pod_container_resource_limits{resource=\\\"memory\\\", pod=\\\"{{ $labels.pod }}\\\"}) by (pod) * 100)\",\"legendFormat\":\"__auto\",\"range\":true,\"instant\":true,\"hide\":false}],\"range\":{\"from\":\"now-1h\",\"to\":\"now\"}}'
    expr: |
      (sum(container_memory_working_set_bytes{namespace=~"tidb.*",container!="POD", container!="", image!=""})by(pod)/
      sum(kube_pod_container_resource_limits{resource="memory",namespace=~"tidb.*"}) by (pod)
      * 100) > 90
    for: 3m
    labels:
      severity: critical
      component: cluster-ng-service

  - alert: NextGenPodRestartsFrequently
    annotations:
      summary: The pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} restart frequently
      message: Container {{ $labels.namespace }}/{{ $labels.container }} is restarting {{ printf "%.2f" $value }} times.
    expr: |
      increase(kube_pod_container_status_restarts_total{namespace=~"tidb.*"}[2m]) > 2
    for: 5m
    labels:
      severity: critical
      component: cluster-ng-service

  - alert: NextGenPodCrashLooping
    annotations:
      message: Pod {{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container
        }}) is restarting {{ printf "%.2f" $value }} times in 1 hours.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepodcrashlooping
    expr: |
      increase(kube_pod_container_status_restarts_total{namespace=~"tidb.*"}[1h]) > 5
    for: 10m
    labels:
      component: cluster-ng-service
      severity: critical

  - alert: NextGenPodDowntimeIsTooLong
    expr: up{namespace=~"tidb.*"} == 0
    for: 10m
    labels:
      severity: critical
      expr: up{namespace=~"tidb.*"} == 0
      tier: next-gen
      component: cluster-ng-service
    annotations:
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.f5wwrkav7k5f
      value: '{{ $value }}'
      message: Component {{ $labels.component }}/{{ $labels.pod_name }} downtime is more than 10 min