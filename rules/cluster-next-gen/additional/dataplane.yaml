groups:
- name: csngdataplane.rules
  rules:
  - alert: NextGenPersistentVolumeFillingUp
    annotations:
      message: The PersistentVolume claimed by {{ $labels.persistentvolumeclaim
        }} in Namespace {{ $labels.namespace }} on region {{ $labels.tidb_cluster_id }} is only {{ $value | humanizePercentage
        }} free.
      runbook_url: https://github.com/tidbcloud/runbooks/wiki/KubePersistentVolumeFillingUp
    expr: |
      kubelet_volume_stats_available_bytes{persistentvolumeclaim=~".*-db-.*", namespace=~"tidb.*"} /
      kubelet_volume_stats_capacity_bytes{persistentvolumeclaim=~".*-db-.*", namespace=~"tidb.*"} < 0.3
    for: 1m
    labels:
      component: cluster-ng-service
      severity: critical

  - alert: NextGenCPUUsageIsTooHigh
    annotations:
      message: CPU utilization of pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} is more than 90%.
      diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"(sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{ pod=\"{{ $labels.pod }}\"}) by (pod)/sum(kube_pod_container_resource_limits{resource=\"cpu\", pod=\"{{ $labels.pod }}\"}) by (pod)) *100","legendFormat":"__auto","range":true,"instant":true,"hide":false}],"range":{"from":"now-1h","to":"now"}}'
    expr: |
      (sum(node_namespace_pod_container:container_cpu_usage_seconds_total:sum_rate{namespace=~"tidb.*"}) by (namespace,pod)/
        sum(kube_pod_container_resource_limits{resource="cpu",namespace=~"tidb.*"}) by (namespace,pod)
      ) *100 > 90
    for: 3m
    labels:
      severity: critical
      component: cluster-ng-service

  - alert: NextGenMemUsageIsTooHigh
    annotations:
      message: Memory utilization of pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} is more than 90%.
      diagnosis: 'click here to see detail: https://clinic.pingcap.com/grafana/explore?orgId=1&left={"datasource":"Ak6VpZD4z","queries":[{"refId":"A","editorMode":"code","expr":"(sum(container_memory_working_set_bytes{container!=\"POD\", container!=\"\", image!=\"\", pod=\"{{ $labels.pod }}\"})by(pod)/sum(kube_pod_container_resource_limits{resource=\"memory\", pod=\"{{ $labels.pod }}\"}) by (pod) * 100)","legendFormat":"__auto","range":true,"instant":true,"hide":false}],"range":{"from":"now-1h","to":"now"}}'
    expr: |
      (sum(container_memory_working_set_bytes{namespace=~"tidb.*",container!="POD", container!="", image!=""})by(pod)/
      sum(kube_pod_container_resource_limits{resource="memory",namespace=~"tidb.*"}) by (pod)
      * 100) > 90
    for: 3m
    labels:
      severity: critical
      component: cluster-ng-service

  - alert: NextGenPodRestartsFrequently
    annotations:
      summary: The pod {{ $labels.pod }} on region {{ $labels.tidb_cluster_id }} restart frequently
      message: Container {{ $labels.namespace }}/{{ $labels.container }} is restarting {{ printf "%.2f" $value }} times.
    expr: |
      increase(kube_pod_container_status_restarts_total{namespace=~"tidb.*"}[2m]) > 2
    for: 5m
    labels:
      severity: critical
      component: cluster-ng-service

  - alert: NextGenPodCrashLooping
    annotations:
      message: Pod {{ $labels.namespace }}/{{ $labels.pod }} ({{ $labels.container
        }}) is restarting {{ printf "%.2f" $value }} times in 1 hours.
      runbook_url: https://github.com/kubernetes-monitoring/kubernetes-mixin/tree/master/runbook.md#alert-name-kubepodcrashlooping
    expr: |
      increase(kube_pod_container_status_restarts_total{namespace=~"tidb.*"}[1h]) > 5
    for: 10m
    labels:
      component: cluster-ng-service
      severity: critical

  - alert: NextGenPodDowntimeIsTooLong
    expr: up{namespace=~"tidb.*"} == 0
    for: 10m
    labels:
      severity: critical
      expr: up{namespace=~"tidb.*"} == 0
      tier: next-gen
      component: cluster-ng-service
    annotations:
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.f5wwrkav7k5f
      value: '{{ $value }}'
      message: Component {{ $labels.component }}/{{ $labels.pod_name }} downtime is more than 10 min