groups:
  - name: swat-kernel-tcocp-control-plane
    rules:
      - alert: ImportFailForBig3
        annotations:
          message: >
            import {{ $labels.import_id }} for cluster {{ $labels.cluster_id }} failed: {{ $labels.msg }}.
            source_tcoc: TCOC-3634
        expr: |
          dataflow_import_fail{tenant=~"1372813089209061633|1372813089209238420|1372813089209256793", project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"}
        for: 1m
        labels:
          severity: major
          component: lightning
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-3634
      - alert: LighningPodNotReadyForBig3
        annotations:
          summary: 'Lightning Pod is not ready for 15 min'
          message: Lightning of Tenant {{ $labels.tenant}}, Cluster {{ $labels.namespace }} has been in a non-ready state for longer than 15 minutes.
        expr: |
          sum(kube_pod_status_phase{job="kube-state-metrics", phase=~"Pending|Unknown", pod=~"import.*",tenant=~"1372813089209061633|1372813089209238420|1372813089209256793", cluster!~".*1372813089454536384.*|.*1372813089454561820.*|.*1372813089454576822.*|.*1372813089454574660.*"}) by (namespace, tenant) > 0
        for: 15m
        labels:
          component: lightning
          severity: critical
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-3817
      - alert: TiCDCPodsUnhealthyPhaseForBig3
        expr: kube_pod_status_phase{phase!="Running", pod=~".*-ticdc-.*",tenant=~"1372813089209061633|1372813089209238420|1372813089209256793", cluster!~".*1372813089454536384.*|.*1372813089454561820.*|.*1372813089454576822.*|.*1372813089454574660.*"} == 1
        for: 15m
        labels:
          severity: major
          component: ticdc
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-3799
        annotations:
          message: >
            TiCDC pod {{ $labels.namespace }}/{{ $labels.pod }} in {{ $labels.cluster }} 
            has been in {{ $labels.phase }} phase for longer than 15 minutes    
      - alert: UnhealthyTiDBComponentPodForBig3
        annotations:
          message: Pod {{ $labels.namespace }}/{{ $labels.pod }} in {{ $labels.cluster }} has been in a non-ready state for longer than 20 minutes.
        expr: kube_pod_status_phase{phase=~"Pending|Unknown|Failed", namespace=~"tidb\\d+", pod=~"db-(pd|tidb|tikv|tiflash|tiproxy).*",tenant=~"1372813089209061633|1372813089209238420|1372813089209256793", cluster!~".*1372813089454536384.*|.*1372813089454561820.*|.*1372813089454576822.*|.*1372813089454574660.*"} == 1
        for: 20m
        labels:
          component: kubernetes
          severity: critical
          type: shoot
          tier: dedicated
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-3492
      - alert: EBSElasticVolumeIOPSLimitExceededForBig3
        expr: |
          max by(cluster_id)(
            label_replace(
              (
                (sum_over_time(
                  (
                    label_replace(
                      rate(aws_ebs_csi_exceeded_iops_seconds_total{
                        tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                        project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
                      }[1m]),
                      "node", "$1", "instance", "(.+)"
                    )
                    * on(node) group_right()
                    kube_pod_info{
                      namespace=~"tidb.*",
                      pod=~"db-tikv-.*"
                    }
                    > 0.3
                  )[24h:15s]
                ) * 15
                ) > 60
                and
                (
                  (sum_over_time(
                    (
                      label_replace(
                        rate(aws_ebs_csi_exceeded_iops_seconds_total{
                          tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                          project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
                        }[1m]),
                        "node", "$1", "instance", "(.+)"
                      )
                      * on(node) group_right()
                      kube_pod_info{
                        namespace=~"tidb.*",
                        pod=~"db-tikv-.*"
                      }
                      > 0.3
                    )[24h:15s]
                  ) * 15
                  )
                  /
                  (sum_over_time(
                    (
                      label_replace(
                        rate(aws_ebs_csi_exceeded_iops_seconds_total{
                          tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                          project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
                        }[1m]),
                        "node", "$1", "instance", "(.+)"
                      )
                      * on(node) group_right()
                      kube_pod_info{
                        namespace=~"tidb.*",
                        pod=~"db-tikv-.*"
                      }
                      > 0.3
                    )[7d:1m]
                  ) * 60
                  )
                ) > 1.5
              ),
              "cluster_id", "$1", "namespace", "(.+)"
            )
          )
        for: 0s
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-3905
        annotations:
          summary: EBS IOPS limit exceeded on cluster '{{ $labels.cluster_id }}'.24h over-threshold time >1m and 1.5× 7day avg.
          message: >
            EBS IOPS limit exceeded on cluster '{{ $labels.cluster_id }}'.24h over-threshold time >1m and 1.5× 7day avg.
            source_tcoc: TCOC-3905
            dashboard: Node-Info -> EBS Volume Performance -> EBS Volume IO Exceeded
      - alert: EBSElasticVolumeThroughputLimitExceededForBig3
        expr: |
          max by(cluster_id)(
            label_replace(
              (
                (sum_over_time(
                  (
                    label_replace(
                      rate(aws_ebs_csi_exceeded_tp_seconds_total{
                        tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                        project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
                      }[1m]),
                      "node", "$1", "instance", "(.+)"
                    )
                    * on(node) group_right()
                    kube_pod_info{
                      namespace=~"tidb.*",
                      pod=~"db-tikv-.*"
                    }
                    > 0.3
                  )[24h:15s]
                ) * 15
                ) > 60
                and
                (
                  (sum_over_time(
                    (
                      label_replace(
                        rate(aws_ebs_csi_exceeded_tp_seconds_total{
                          tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                          project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
                        }[1m]),
                        "node", "$1", "instance", "(.+)"
                      )
                      * on(node) group_right()
                      kube_pod_info{
                        namespace=~"tidb.*",
                        pod=~"db-tikv-.*"
                      }
                      > 0.3
                    )[24h:15s]
                  ) * 15
                  )
                  /
                  (sum_over_time(
                    (
                      label_replace(
                        rate(aws_ebs_csi_exceeded_tp_seconds_total{
                          tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                          project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
                        }[1m]),
                        "node", "$1", "instance", "(.+)"
                      )
                      * on(node) group_right()
                      kube_pod_info{
                        namespace=~"tidb.*",
                        pod=~"db-tikv-.*"
                      }
                      > 0.3
                    )[7d:1m]
                  ) * 60
                  )
                ) > 1.5
              ),
              "cluster_id", "$1", "namespace", "(.+)"
            )
          )
        for: 0s
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-3905
        annotations:
          summary: EBS Throughput limit exceeded on cluster '{{ $labels.cluster_id }}'.24h over-threshold time >1m and 1.5× 7day avg.
          message: >
            EBS Throughput limit exceeded on cluster '{{ $labels.cluster_id }}'.24h over-threshold time >1m and 1.5× 7day avg.
            source_tcoc: TCOC-3905
            dashboard: Node-Info -> EBS Volume Performance -> EBS Volume IO Exceeded
      - alert: TiKVOOMRelatedPodRestartsForBig3
        expr: |
          sum by (namespace, pod) (
            increase(
              kube_pod_container_status_restarts_total{
                tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
                pod=~"db-tikv-.*"
              }[15m]
            ) > 0
          )
          * on(namespace, pod) group_left(reason)
          max by (namespace, pod, reason) (
            kube_pod_container_status_last_terminated_reason{
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
              pod=~"db-tikv-.*",
              reason="OOMKilled"
            } > 0
          )
        for: 0s
        labels:
          severity: critical
          component: tikv
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-4157
        annotations:
          message: >
            TiKV Pod {{ $labels.namespace }}/{{ $labels.pod }} restarted due to OOMKilled.
            source_tcoc: TCOC-4157
      - alert: TiDBOOMRelatedPodRestartsForBig3
        expr: |
          sum by (namespace, pod) (
            increase(
              kube_pod_container_status_restarts_total{
                tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
                pod=~"db-tidb-.*"
              }[15m]
            ) > 0
          )
          * on(namespace, pod) group_left(reason)
          max by (namespace, pod, reason) (
            kube_pod_container_status_last_terminated_reason{
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
              pod=~"db-tidb-.*",
              reason="OOMKilled"
            } > 0
          )
        for: 0s
        labels:
          severity: critical
          component: tidb-executor
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-4157
        annotations:
          message: >
            TiDB Pod {{ $labels.namespace }}/{{ $labels.pod }} restarted due to OOMKilled.
            source_tcoc: TCOC-4157
      - alert: TiFlashOOMRelatedPodRestartsForBig3
        expr: |
          sum by (namespace, pod) (
            increase(
              kube_pod_container_status_restarts_total{
                tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
                pod=~"db-tiflash-.*"
              }[15m]
            ) > 0
          )
          * on(namespace, pod) group_left(reason)
          max by (namespace, pod, reason) (
            kube_pod_container_status_last_terminated_reason{
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
              pod=~"db-tiflash-.*",
              reason="OOMKilled"
            } > 0
          )
        for: 0s
        labels:
          severity: critical
          component: tiflash
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-4157
        annotations:
          message: >
            TiFlash Pod {{ $labels.namespace }}/{{ $labels.pod }} restarted due to OOMKilled.
            source_tcoc: TCOC-4157
      - alert: TiCDCOOMRelatedPodRestartsForBig3
        expr: |
          sum by (namespace, pod) (
            increase(
              kube_pod_container_status_restarts_total{
                tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
                pod=~"db-ticdc-.*"
              }[15m]
            ) > 0
          )
          * on(namespace, pod) group_left(reason)
          max by (namespace, pod, reason) (
            kube_pod_container_status_last_terminated_reason{
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
              pod=~"db-ticdc-.*",
              reason="OOMKilled"
            } > 0
          )
        for: 0s
        labels:
          severity: critical
          component: ticdc-kernel
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-4157
        annotations:
          message: >
            TiCDC Pod {{ $labels.namespace }}/{{ $labels.pod }} restarted due to OOMKilled.
            source_tcoc: TCOC-4157
      - alert: PDOOMRelatedPodRestartsForBig3
        expr: |
          sum by (namespace, pod) (
            increase(
              kube_pod_container_status_restarts_total{
                tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
                pod=~"db-pd-.*"
              }[15m]
            ) > 0
          )
          * on(namespace, pod) group_left(reason)
          max by (namespace, pod, reason) (
            kube_pod_container_status_last_terminated_reason{
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660",
              pod=~"db-pd-.*",
              reason="OOMKilled"
            } > 0
          )
        for: 0s
        labels:
          severity: critical
          component: pd
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-4157
        annotations:
          message: >
            PD Pod {{ $labels.namespace }}/{{ $labels.pod }} restarted due to OOMKilled.
            source_tcoc: TCOC-4157
      - alert: TiKVIOPSDroppedTo0ForBig3
        expr: |
          label_replace(
            rate(
              aws_ebs_csi_read_ops_total{
                tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
              }[1m]
            ), "node", "$1", "instance", "(.+)")
          * on(node) group_right()
          kube_pod_info{
            namespace=~"tidb.*",
            pod=~"db-tikv-.*"
          } == 0
          and
          label_replace(
            rate(
              aws_ebs_csi_write_ops_total{
                tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
                project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
              }[1m]
            ), "node", "$1", "instance", "(.+)")
          * on(node) group_right()
          kube_pod_info{
            namespace=~"tidb.*",
            pod=~"db-tikv-.*"
          } == 0
        for: 0s
        labels:
          severity: critical
          component: tikv
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-4157
        annotations:
          message: >
            TiKV IOPS dropped to 0 in  {{ $labels.namespace }}/{{ $labels.pod }}.
            source_tcoc: TCOC-4157
