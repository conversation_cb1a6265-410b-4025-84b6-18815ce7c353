groups:
- name: tidb-operator.rules
  rules:
  - alert: TiDBControllerManagerNoAvailableReplicas
    expr: kube_deployment_status_replicas_available{deployment="tidb-controller-manager"}==0
    for: 15m
    labels:
      component: tidb-operator
      service: tidb-controller-manager
      severity: critical
      type: shoot
      tier: dedicated

    annotations:
      description: There are no running tidb controller manager pods.
  - alert: AdvancedStatefulsetControllerNoAvailableReplicas
    expr: kube_deployment_status_replicas_available{deployment="advanced-statefulset-controller"}==0
    for: 15m
    labels:
      component: tidb-operator
      service: advanced-statefulset-controller
      severity: critical
      type: shoot
      tier: dedicated

    annotations:
      description: There are no running advanced statefulset controller pods.
