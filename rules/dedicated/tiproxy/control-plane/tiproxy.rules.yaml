groups:
- name: tiproxy
  rules:
  - alert: TiProxyRestartedUnexpectedly
    expr: (increase(kube_pod_container_status_restarts_total{container="tiproxy"}[5m])>0) * ignoring (reason) group_left(reason) (kube_pod_container_status_last_terminated_reason{reason!='Completed', container="tiproxy"}==1)
    labels:
      severity: major
      expr: (increase(kube_pod_container_status_restarts_total{container="tiproxy"}[5m])>0) * ignoring (reason) group_left(reason) (kube_pod_container_status_last_terminated_reason{reason!='Completed', container="tiproxy"}==1)
      component: tiproxy
      tier: dedicated
    annotations:
      description: 'tiproxy container under cluster: {{ $labels.cluster }}, {{ $labels.namespace }}/{{ $labels.pod }} have restarted due to container {{ $labels.reason }}'
      summary: tiproxy container just restarted unexpectedly
  - alert: TiProxyRestartedFrequently
    expr: (increase(kube_pod_container_status_restarts_total{container="tiproxy"}[30m])>=3) * ignoring (reason) group_left(reason) (kube_pod_container_status_last_terminated_reason{reason!='Completed', container="tiproxy"}==1)
    labels:
      severity: critical
      expr: (increase(kube_pod_container_status_restarts_total{container="tiproxy"}[30m])>=3) * ignoring (reason) group_left(reason) (kube_pod_container_status_last_terminated_reason{reason!='Completed', container="tiproxy"}==1)
      component: tiproxy
      tier: dedicated
    annotations:
      description: 'tiproxy container under cluster: {{ $labels.cluster }}, {{ $labels.namespace }}/{{ $labels.pod }} have restarted due to container {{ $labels.reason }}'
      summary: tiproxy container just restarted unexpectedly more than 3 times in 30 minutes

