groups:
- name: tiproxy
  rules:
  - alert: TiProxyServerIsDown
    expr: up{component="tiproxy"} == 0
    for: 5m
    labels:
      tier: dedicated
      severity: major
      expr: up{component="tiproxy"} == 0
      component: tiproxy
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }} is down for 10 mins'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.mvt8hlb2k7p
      summary: TiProxy server is down
  - alert: TiProxyFrequentlyUnavailable
    expr: sum(changes(process_start_time_seconds{job=~".*tiproxy"}[1h])) by (cluster_id, instance, tenant,provider_type) >=4
    for: 1m
    labels:
      tier: dedicated
      severity: critical
      expr: sum(changes(process_start_time_seconds{job=~".*tiproxy"}[1h])) by (cluster_id, instance, tenant,provider_type) >=4
      component: tiproxy
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.utnvsvbf6hy8
      summary: TiProxy server unavailable time more than 3 times in 1 hour

  - alert: TiProxyDiscoveredTimeJumpBack
    expr: increase(tiproxy_monitor_time_jump_back_total[10m])  > 0
    for: 5m
    labels:
      tier: dedicated
      severity: major
      expr: increase(tiproxy_monitor_time_jump_back_total[10m])  > 0
      component: tiproxy
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values:{{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.ttcqlika9pqq
      summary: TiProxy monitor found time jump back error
  - alert: TiProxyReportErr
    expr: increase(tiproxy_server_err[10m]) > 0
    for: 5m
    labels:
      tier: dedicated
      severity: major
      expr: increase(tiproxy_server_err[10m]) > 0
      component: tiproxy
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.bb5bh8qqc12b
      summary: TiProxy reports critical error
  - alert: TiProxyGetBackendFail
    expr: sum(rate(tiproxy_backend_get_backend{res="fail"}[1m])) by (cluster_id, instance, tenant, provider_type) > 10
    for: 5m
    labels:
      tier: dedicated
      severity: major
      expr: sum(rate(tiproxy_backend_get_backend{res="fail"}[1m])) by (cluster_id, instance, tenant, provider_type) > 10
      component: tiproxy
    annotations:
      description: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, value: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.k8bm4rl7zzt3
      summary: TiProxy fails to get backends for 5 minutes
