groups:
- name: tidb-monitor
  rules:
  - alert: TiDBMonitorDown
    expr: up{job="tidb-monitor"} == 0
    for: 10m
    labels:
      tier: dedicated
      component: prometheus
      service: tidb-monitor
      severity: critical
      type: shoot

    annotations:
      description: tidb monitor has disappeared from Prometheus target discovery.
  - alert: TiKVMonitorDown
    expr: up{job="tikv-monitor"} == 0
    for: 10m
    labels:
      tier: dedicated
      component: prometheus
      service: tikv-monitor
      severity: critical
      type: shoot

    annotations:
      description: tikv monitor has disappeared from Prometheus target discovery.
