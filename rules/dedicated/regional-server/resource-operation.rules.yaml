# use infra-layer metrics to alert such case
groups:
- name: dedicated-regionalserver-resource-operations
  rules:
  - alert: VPCCreationDurationTooLong 
    annotations:
      message: The VPC Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 5 minutes
    expr: |
      max(regional_server_vpc_info{service="regional-server", status=~"Pending|ReadyForCreate|Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 5m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: VPCCreationDurationTooLong
    annotations:
      message: The VPC Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 10 minutes
    expr: |
      max(regional_server_vpc_info{service="regional-server", status=~"Pending|ReadyForCreate|Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 10m
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: VPCDeletionDurationTooLong
    annotations:
      message: The VPC Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} deletion time exceeds 15 minutes
    expr: |
      max(regional_server_vpc_info{service="regional-server", status=~"Deleting"})by(name, region, project_id, tenant_id) >= 1
    for: 15m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: InternetResourceCreationDurationTooLong
    annotations:
      message: The Internet Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 10 minutes
    expr: |
      max(regional_server_internet_info{service="regional-server", status=~"ReadyForCreate|Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 10m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: InternetResourceCreationDurationTooLong
    annotations:
      message: The Internet Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 20 minutes
    expr: |
      max(regional_server_internet_info{service="regional-server", status=~"ReadyForCreate|Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 20m
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: InternetResourceDeletionDurationTooLong
    annotations:
      message: The Internet Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} deletion time exceeds 15 minutes
    expr: |
      max(regional_server_internet_info{service="regional-server", status=~"Deleting"})by(name, region, project_id, tenant_id) >= 1
    for: 15m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: InternetResourceDeletionDurationTooLong
    annotations:
      message: The Internet Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} deletion time exceeds 24 hours
    expr: |
      max(regional_server_internet_info{service="regional-server", status=~"Deleting"})by(name, region, project_id, tenant_id) >= 1
    for: 24h
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: K8sCreationDurationTooLong
    annotations:
      message: K8s Resource(APPBox CR) {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 20 minutes
    expr: |
      max(regional_server_appbox_info{service="regional-server", status=~"ReadyForCreate|Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 20m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: K8sCreationDurationTooLong
    annotations:
      message: K8s Resource(APPBox CR) {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 30 minutes
    expr: |
      max(regional_server_appbox_info{service="regional-server", status=~"ReadyForCreate|Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 30m
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: K8sDeletionDurationTooLong
    annotations:
      message: K8s Resource(APPBox CR) {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} deletion time exceeds 30 minutes
    expr: |
      max(regional_server_appbox_info{service="regional-server", status=~"Deleting"})by(name, region, project_id, tenant_id) >= 1
    for: 30m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: K8sDeletionDurationTooLong
    annotations:
      message: K8s Resource(APPBox CR) {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} deletion time exceeds 24 hours
    expr: |
      max(regional_server_appbox_info{service="regional-server", status=~"Deleting"})by(name, region, project_id, tenant_id) >= 1
    for: 24h
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: TiDBClusterRouteCreationDurationTooLong
    annotations:
      message: The TiDBClusterRoute Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 5 minutes
    expr: |
      max(regional_server_approute_info{service="regional-server", status=~"Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 5m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: TiDBClusterRouteCreationDurationTooLong
    annotations:
      message: The TiDBClusterRoute Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 10 minutes
    expr: |
      max(regional_server_approute_info{service="regional-server", status=~"Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 10m
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: TiDBClusterRouteDeletionDurationTooLong
    annotations:
      message: The TiDBClusterRoute Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} deletion time exceeds 30 minutes
    expr: |
      max(regional_server_approute_info{service="regional-server", status=~"Deleting"})by(name, region, project_id, tenant_id) >= 1
    for: 30m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: VPCPeeringCreationDurationTooLong
    annotations:
      message: The VPC Peering Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 10 minutes
    expr: |
      max(regional_server_vpcpeering_info{service="regional-server", status=~"Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 10m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: VPCPeeringCreationDurationTooLong
    annotations:
      message: The VPC Peering Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 20 minutes
    expr: |
      max(regional_server_vpcpeering_info{service="regional-server", status=~"Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 20m
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: VPCPeeringPendingDurationTooLong
    annotations:
      message: The VPC Peering Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} pending time exceeds 2 hours, waiting for the peer VPC to accept the peering
    expr: |
      max(regional_server_vpcpeering_info{service="regional-server", status=~"Pending"})by(name, region, project_id, tenant_id) >= 1
    for: 2h
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: VPCPeeringDeletionDurationTooLong
    annotations:
      message: The VPC Peering Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 20 minutes
    expr: |
      max(regional_server_vpcpeering_info{service="regional-server", status=~"Deleting"})by(name, region, project_id, tenant_id) >= 1
    for: 20m
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: PrivateLinkServiceCreationDurationTooLong
    annotations:
      message: The Private Link Service Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 15 minutes
    expr: |
      max(regional_server_privatelinkservice_info{service="regional-server", status=~"Pending|Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 15m
    labels:
      severity: major
      component: dedicated-regional-server
  - alert: PrivateLinkServiceCreationDurationTooLong
    annotations:
      message: The Private Link Service Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} creation time exceeds 30 minutes
    expr: |
      max(regional_server_privatelinkservice_info{service="regional-server", status=~"Pending|Creating|Failed"})by(name, region, project_id, tenant_id) >= 1
    for: 30m
    labels:
      severity: critical
      component: dedicated-regional-server
  - alert: PrivateLinkServiceDeletionDurationTooLong
    annotations:
      message: The Private Link Service Resource {{ $labels.name }} in {{ $labels.region }}/{{ $labels.project_id }}/{{ $labels.tenant_id }} deletion time exceeds 20 minutes
    expr: |
      max(regional_server_privatelinkservice_info{service="regional-server", status=~"Deleting"})by(name, region, project_id, tenant_id) >= 1
    for: 20m
    labels:
      severity: major
      component: dedicated-regional-server
