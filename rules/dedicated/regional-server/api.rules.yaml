groups:
- name: dedicated-regionalserver-api
  rules:
  - alert: TooManyHTTP5xxErrors
    expr: |
      (sum(rate(regional_server_http_requests_total{service="regional-server", status_code=~"5.."}[1m])) by (exported_endpoint)
      / sum(rate(regional_server_http_requests_total{service=~"regional-server"}[1m])) by (exported_endpoint)
      ) * 100 > 0
    for: 1s
    labels:
      severity: major
      component: dedicated-regional-server
    annotations:
      description: HTTP 5xx error rate is {{ $value }}
