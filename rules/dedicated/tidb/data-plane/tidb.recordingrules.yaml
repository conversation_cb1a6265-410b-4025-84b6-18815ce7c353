groups:
  - name: tidb.rules
    interval: 15s
    rules:
        - record: tidb_server_handle_query_duration_seconds_bucket:le:sum:rate
          expr: sum(rate(tidb_server_handle_query_duration_seconds_bucket{}[2m])) by (cluster_id,le)
        - record: tidb_server_handle_query_duration_seconds_bucket:le_sqltype_not:sum:rate
          expr: sum(rate(tidb_server_handle_query_duration_seconds_bucket{sql_type!="internal"}[2m])) by (cluster_id,le)
        - record: tidb_server_handle_query_duration_seconds_bucket:instance_le_sqltype_not:sum:rate
          expr: sum(rate(tidb_server_handle_query_duration_seconds_bucket{sql_type!="internal"}[2m])) by (cluster_id,instance,le)
        - record: tidb_server_handle_query_duration_seconds_bucket:le_sqltype:sum:rate
          expr: sum(rate(tidb_server_handle_query_duration_seconds_bucket{}[2m])) by (cluster_id,le,sql_type)
        - record: tidb_server_handle_query_duration_seconds_bucket:le_instance:sum:rate
          expr: sum(rate(tidb_server_handle_query_duration_seconds_bucket{}[2m])) by (cluster_id,instance,le)
        - record: tidb_server_handle_query_duration_seconds_bucket:sqltype_instance_le:sum:rate
          expr: sum(rate(tidb_server_handle_query_duration_seconds_bucket{}[2m])) by (cluster_id,instance,sql_type,le)

        - record: tidb_server_conn_idle_duration_seconds_bucket:in_txn_zero:sum:rate
          expr: sum(rate(tidb_server_conn_idle_duration_seconds_bucket{in_txn='0'}[2m])) by (cluster_id,le,in_txn)
        - record: tidb_server_conn_idle_duration_seconds_bucket:in_txn_one:sum:rate
          expr: sum(rate(tidb_server_conn_idle_duration_seconds_bucket{in_txn='1'}[2m])) by (cluster_id,le,in_txn)
        - record: tidb_server_conn_idle_duration_seconds_bucket:instance_in_txn_zero:sum:rate
          expr: sum(rate(tidb_server_conn_idle_duration_seconds_bucket{in_txn='0'}[2m])) by (cluster_id,instance,le,in_txn)
        - record: tidb_server_conn_idle_duration_seconds_bucket:instance_in_txn_one:sum:rate
          expr: sum(rate(tidb_server_conn_idle_duration_seconds_bucket{in_txn='1'}[2m])) by (cluster_id,instance,le,in_txn)

        - record: tidb_session_transaction_duration_seconds_bucket:le_txn_mode:sum:rate
          expr: sum(rate(tidb_session_transaction_duration_seconds_bucket{}[2m])) by (cluster_id,le, txn_mode)
        - record: tidb_session_transaction_duration_seconds_bucket:le_txn_mode_instance:sum:rate
          expr: sum(rate(tidb_session_transaction_duration_seconds_bucket{}[2m])) by (cluster_id,le, txn_mode, instance)
        - record: tidb_session_transaction_duration_seconds_bucket:le_sql_type:sum:rate
          expr: sum(rate(tidb_session_transaction_duration_seconds_bucket{}[2m])) by (cluster_id,le,sql_type)

        - record: tidb_tikvclient_txn_cmd_duration_seconds_bucket:sum:rate
          expr: sum(rate(tidb_tikvclient_txn_cmd_duration_seconds_bucket{}[2m])) by (cluster_id,le,type)

        - record: tikv_grpc_msg_duration_seconds_bucket:le_type:sum:rate
          expr: sum(rate(tikv_grpc_msg_duration_seconds_bucket{}[2m])) by (cluster_id,le,type)

  - name: topsql.rules
    interval: 1m
    rules:
        - record: topsql_cpu_time_ms:sum_over_time_5m:sum_sql_digest
          expr: sum by (sql_digest) (sum_over_time(topsql_cpu_time_ms[5m]))
        - record: topsql_stmt_exec_count:tidb:sum_over_time_5m:sum_sql_digest
          expr: sum by (sql_digest) (sum_over_time(topsql_stmt_exec_count{instance_type="tidb"}[5m]))
