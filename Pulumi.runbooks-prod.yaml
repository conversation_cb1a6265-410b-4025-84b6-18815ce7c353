encryptionsalt: v1:qVx3M++Az0k=:v1:LBFORuLnPYyTvr2s:s+LuJPTFAh90AdZq1TAFpvFS6hzgtQ==
config:
  runbooks:alert-dns-zone: prod.aws.tidbcloud.com
  runbooks:autonomousPagerDutyKey:
    secure: v1:n7woz+KRCoaIW+jz:T7lw5HizANhzH9XDBLjiBEEXHeMHBoIs3lkzo10luqf87EEdM2ecloNj2ByMyomF
  runbooks:computePagerDutyKey:
    secure: v1:+CDKXo70kFCpzEu8:AnRXDCQ7EPUUGy0RlkBqUqq5lKLTmDr3p6MDcby7uawgK9hDstCIAnCujYUJh4Ha
  runbooks:dbaas_platform_pagerduty_key:
    secure: v1:istX5g4YE2aTzPdM:+9bF2Yi27EKH4Ld5ukj5LKBh4pXgyK40FgUuGnf2n6z3is3HCMTWupY7KwqLkzYl
  runbooks:dpPagerDutyKey:
    secure: v1:Pt7PChh62EYZkmxq:+cqDNyfx9xn2Bqzf90oDnoXnNiFct9UBtiH2hrtgt6AgIKIdG4fZV4byTqQFW5qb
  runbooks:ecosystemPagerDutyKey:
    secure: v1:NCUx1mPsbQM/SpFV:HZwaBPlvDz8YvA+nUGCuSu8wdFzz2/CN0CMfO/OXPvNdWOBbrGqRVLfsiXWCBYdn
  runbooks:enableAlertmanager: "false"
  runbooks:enableCumstomerAlert: "true"
  runbooks:enableInfra: "true"
  runbooks:env: prod
  runbooks:freetier-stacks:
  - sre-bot/eks-freetier/prod-eu-central-1-f01
  - sre-bot/eks-freetier/prod-ap-northeast-1-f01
  - sre-bot/eks-freetier/prod-ap-southeast-1-f01
  - sre-bot/eks-freetier/prod-us-west-2-f01
  - sre-bot/eks-freetier/prod-us-east-1-f01
  - sre-bot/eks-freetier/prod-ca-central-1-f01
  runbooks:freetierPagerdutyKey:
    secure: v1:1gKrfAVfFvfzIarl:EDdJ4J6XnN6eCSVIgp6E562WFX57agbcC5tNOkDuSCDaguWExay/wh2xpfmHouas
  runbooks:gatewayTlsPagerDutyKey:
    secure: v1:ywzu18NujXheivpL:9FXUIfNu4lYHOnuHyryJKzjYTZRLF+rigi9Ho1cIkHjxbohHK86pZttKuZBaSuea
  runbooks:grafanaAuth0ClientId: xEf4i0lIkTu6z9AGOH27mLeOstqQvADr
  runbooks:grafanaAuth0ClientSecret:
    secure: v1:9sbAtiJmwo56mpnF:SlBezRkqN0TfMa+xJ8LFFJEoK9uLelegWr/************************************************************************=
  runbooks:grafanaAuth0Domain: tidb-soc2.us.auth0.com
  runbooks:infra-base-stack: sre-bot/eks/prod-base-us-west-2
  runbooks:infra-seed-stacks:
  - sre-bot/eks/prod-seed-ap-northeast-1
  - sre-bot/eks/prod-seed-ap-southeast-1
  - sre-bot/eks/prod-seed-us-east-1
  - sre-bot/eks/prod-seed-us-west-2
  - sre-bot/eks/prod-seed-ap-northeast-2
  - sre-bot/gke/prod-seed-asia-northeast1
  - sre-bot/gke/prod-seed-asia-southeast1
  - sre-bot/gke/prod-seed-us-central1
  - sre-bot/gke/prod-seed-us-west1
  - sre-bot/eks/prod-seed-eu-central-1
  - sre-bot/eks/prod-seed-ap-south-1
  - sre-bot/gke/prod-seed-asia-east1
  - sre-bot/gke/prod-seed-asia-northeast2
  - sre-bot/gke/prod-seed-us-east4
  - sre-bot/eks/prod-seed-ap-southeast-3
  runbooks:infraMonitoringToken: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  runbooks:jpPagerDutyKey:
    secure: v1:t2VN7TFIwHPYvWDY:bs1VQijGXndhXgXMbRHd6hMC016jZPg7BB5A/l1tXmH+WKQ7tK7Se1/+EWEvxf2Z
  runbooks:o11y-seed-stack-whitelist:
  - sre-bot/eks/prod-seed-ap-northeast-1
  - sre-bot/eks/prod-seed-ap-southeast-1
  - sre-bot/eks/prod-seed-us-east-1
  - sre-bot/eks/prod-seed-us-west-2
  - sre-bot/eks/prod-seed-ap-northeast-2
  - sre-bot/gke/prod-seed-asia-northeast1
  - sre-bot/gke/prod-seed-asia-southeast1
  - sre-bot/gke/prod-seed-us-central1
  - sre-bot/gke/prod-seed-us-west1
  - sre-bot/eks/prod-seed-eu-central-1
  - sre-bot/eks/prod-seed-ap-south-1
  - sre-bot/gke/prod-seed-asia-east1
  - sre-bot/gke/prod-seed-asia-northeast2
  - sre-bot/gke/prod-seed-us-east4
  - sre-bot/eks/prod-seed-ap-southeast-3
  - sre-bot/eks/prod-seed-ap-southeast-2
  runbooks:need-disabled-garden-namespace:
  - sre-bot/eks/prod-seed-ap-southeast-3
  - sre-bot/eks/prod-seed-ap-southeast-2
  runbooks:pagerduty_key:
    secure: v1:hhXpt7jeo7QleMnk:RGE6CkNVM5PdfYvJ6EC8/09mcPfk2mJEB8ZHsMS/OQSA3pvQV4wCAyh6W4u0LGeM
  runbooks:portal-namespace: prod-ms
  runbooks:sharedtierPagerdutyKey:
    secure: v1:8Uqooj759RNep+hp:kMyhEgptPihexJNn1eykPD+HONafR9UArm/KADoiSjDPFwgoutvUDrhCmhnoCY2r
  runbooks:slack_url:
    secure: v1:OROHl7FGWBiDxP/J:yS6ApOLgVXe3rmHy42zO5pENa7KNcPPYVXF6Rwq3kMc4rsFWhVgxHksRF8MS9leJbY8l1SVA/86eSN+jTaP4o0XiEHNwMf1vnpfaQgxJm8Ik4lJmmA8IO8B2Yojd
  runbooks:storagePagerDutyKey:
    secure: v1:USTzschK9geqCqly:pETFaw7zsA9sdHlhSY5uUu/OUaqvH3JVZU98x7Uv6+izXaDfiG7Szu2fwdmeD1iq
