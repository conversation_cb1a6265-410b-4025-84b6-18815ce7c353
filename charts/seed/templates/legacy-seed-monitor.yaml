# only monitor cluster-autoscaler when use eks
{{ if contains "eks" .Values.stack }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}cluster-autoscaler
  namespace: kube-system
  labels:
    app: cluster-autoscaler
spec:
  selector:
    app: cluster-autoscaler
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8085
    targetPort: 8085
    protocol: TCP
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Release.Name }}-cluster-autoscaler
  namespace: kube-system
spec:
  endpoints:
  - interval: 30s
    scrapeTimeout: 10s
    path: /metrics
    port: http
  namespaceSelector:
    matchNames:
      - 'kube-system'
  selector:
    matchLabels:
      app: cluster-autoscaler
{{ end }}
