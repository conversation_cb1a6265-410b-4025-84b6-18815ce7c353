groups:
- name: gitops.rules
  rules:
  - alert: ClusterWithOutConfigRevisionRefInConfigManager
    annotations:
      description: |-
        VALUE = {{ $value }}
        LABELS = {{ $labels }}
      summary: Config Manager detect some cluster without config_revision_ref
      expr: sum(gitops_config_revision_ref_is_nil) > 0
      for: 1m
    expr:  sum(gitops_config_revision_ref_is_nil) > 0
    for: 1m
    labels:
      component: gitops
      severity: critical
  - alert: ClusterWithOutConfigRevisionRefInInfraProvider
    annotations:
      description: |-
        VALUE = {{ $value }}
        LABELS = {{ $labels }}
      summary: Infra Provider detect some cluster without config_revision_ref
      expr: sum(infra_config_revision_ref_is_nil) > 0
      for: 1m
    expr:  sum(infra_config_revision_ref_is_nil)> 0
    for: 1m
    labels:
      component: gitops
      severity: critical