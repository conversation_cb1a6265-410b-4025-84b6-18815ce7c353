groups:
- name: tikv-worker
  rules:
  - alert: TiKVWorkerDowntimeIsTooLong
    expr: up{component=~"tikv-worker|coprocessor-worker", job=~".*tikv-worker.*|.*coprocessor-worker.*"} == 0
    for: 10m
    labels:
      tier: next-gen
      severity: critical
      expr: up{component=~"tikv-worker|coprocessor-worker", job=~".*tikv-worker.*|.*coprocessor-worker.*"} == 0
      component: tikv
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.t0ws8abzblbu
      value: '{{ $value }}'
      summary: TiKV worker downtime is more than 10 min
  - alert: TiKVWorkerFrequentlyRestart
    expr: sum(changes(floor(process_start_time_seconds{job=~".*tikv-worker.*|.*coprocessor-worker.*"})[1h:15s])) by (cluster_id, instance, tenant, provider_type) >=3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      expr: sum(changes(process_start_time_seconds{job=~".*tikv-worker.*|.*coprocessor-worker.*"}[1h])) by (cluster_id, instance, tenant, provider_type) >=3
      component: tikv
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.t0ws8abzblbu
      value: '{{ $value }}'
      summary: TiKV worker is down for at least 3 times in a hour
  - alert: TiKVClusterFrequentlyRestart
    expr: |-
      sum by (cluster_id, tenant, provider_type) (
          resets((-floor(process_start_time_seconds{job=~".*tikv-worker.*|.*coprocessor-worker.*"}))[1h:15s])
        and
          max_over_time(process_start_time_seconds{job=~".*tikv-worker.*|.*coprocessor-worker.*"}[30m] offset 1h) > 0
      ) >= 3
    for: 1m
    labels:
      tier: next-gen
      severity: critical
      component: tikv
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.t0ws8abzblbu
      value: '{{ $value }}'
      summary: TiKV instances for a cluster is down for at least 3 times in a hour
  - alert: TiKVWorkerRestarted
    expr: sum(changes(floor(process_start_time_seconds{job=~".*tikv-worker.*|.*coprocessor-worker.*"})[1h:15s])) by (cluster_id, instance, tenant, provider_type) >=1 and sum(max_over_time(up{job=~".*tikv-worker.*|.*coprocessor-worker.*"}[100m] offset 60m)) by (cluster_id, instance, tenant, provider_type) > 0
    for: 10m
    labels:
      tier: next-gen
      severity: warning
      expr: sum(changes(floor(process_start_time_seconds{job=~".*tikv-worker.*|.*coprocessor-worker.*"})[1h:15s])) by (cluster_id, instance, tenant, provider_type) >=1 and sum(max_over_time(up{job=~".*tikv-worker.*|.*coprocessor-worker.*"}[100m] offset 60m)) by (cluster_id, instance, tenant, provider_type) > 0
      component: tikv
    annotations:
      message: 'cluster: {{ $labels.cluster_id }}, instance: {{ $labels.instance }}, values: {{ $value }}'
      runbook_url: https://docs.google.com/document/d/17dQQS3ONxhqdlqKth9kZHTtpANp49UgUBF_dMk5ewCE/edit#heading=h.t0ws8abzblbu
      value: '{{ $value }}'
      summary: TiKV worker is down for at least 1 times in a hour