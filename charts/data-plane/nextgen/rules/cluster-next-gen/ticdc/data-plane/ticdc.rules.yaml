groups:
- name: ticdc
  rules:
  - alert: BizChangefeedCheckpointHighDelay1h
    expr: ticdc_owner_checkpoint_ts_lag{} > 3600
    for: 10m # reduce noise for new created changefeed
    labels:
      severity: critical
      expr: ticdc_owner_checkpoint_ts_lag{} > 1h
      component: ticdc
    annotations:
      message: >
        Checkpoint of {{ $labels.changefeed }} delay more than 1 hour.
        TiDB Cluster: {{ $labels.tidb_cluster_id }}
        Reported by {{ $labels.instance }}
        Region: {{ $labels.seed_region }}
        Infra Arch: {{ $labels.seed_provider }}
  - alert: BizChangefeedCheckpointHighDelay3h
    expr: ticdc_owner_checkpoint_ts_lag{} > 10800
    for: 10m # reduce noise for new created changefeed
    labels:
      severity: major
      expr: ticdc_owner_checkpoint_ts_lag{} > 3h
      component: ticdc
    annotations:
      message: >
        Checkpoint of {{ $labels.changefeed }} delay more than 3 hours.
        TiDB Cluster: {{ $labels.tidb_cluster_id }}
        Reported by {{ $labels.instance }}
        Region: {{ $labels.seed_region }}
        Infra Arch: {{ $labels.seed_provider }}
  - alert: BizChangefeedIsFailedByKernelStatus
    expr: ticdc_owner_status{} == 2
    labels:
      severity: critical
      expr: ticdc_owner_status{} == 2
      component: ticdc
    annotations:
      message: >
        Changefeed: {{ $labels.changefeed }}
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
  - alert: BizChangefeedIsWarningByKernelStatus
    expr: ticdc_owner_status{} == 6
    for: 30m
    labels:
      severity: major
      expr: ticdc_owner_status{} == 6
      component: ticdc
    annotations:
      message: >
        Changefeed: {{ $labels.changefeed }}
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
  - alert: TiKVSideCDCSinkMemoryHighUsage
    expr: sum(tikv_cdc_sink_memory_bytes{instance=~".*-tikv.*"}) by (cluster_id, instance) > 300000000
    for: 2m
    labels:
      severity: critical
      expr: sum(tikv_cdc_sink_memory_bytes{instance=~".*-tikv.*"}) by (cluster_id, instance) > 300000000
      component: ticdc
    annotations:
      message: >
        TiDB Cluster: {{ $labels.cluster_id }}
        Instance: {{ $labels.instance }}
        Usage of CDC sink memory in TiKV side is high which default is 512MB, please refer to SOP https://pingcap.feishu.cn/wiki/OTIewy2EoiAnGCkTezmc0xgHnfc to handle this case.
  - alert: BizTiCDCCoreUsageOver80
    expr: (rate(process_cpu_seconds_total{component="ticdc"}[2m])/ticdc_server_go_max_procs{component="ticdc"}) * 100 > 80
    for: 30m
    labels:
      severity: major
      component: ticdc
      expr: (rate(process_cpu_seconds_total{component="ticdc"}[2m])/ticdc_server_go_max_procs{component="ticdc"}) * 100 > 80

    annotations:
      message: >
        TiCDC core usage is over 80% for 30 minutes.
        TiDB Cluster: {{ $labels.tidb_cluster_id }}
        Reported by {{ $labels.instance }}
        Region: {{ $labels.seed_region }}
        Infra Arch: {{ $labels.seed_provider }}
