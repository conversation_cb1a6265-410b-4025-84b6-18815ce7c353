groups:
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/tidb/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/pd/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/tiflash/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/tikv/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/ticdc/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tiproxy pod rules
{{ range $path, $_ :=  .Files.Glob "rules/cluster-next-gen/tiproxy/data-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tidb pod's tidb-auditlog sidecar container
# Metrics scrape config refer to
# https://github.com/tidbcloud/observability/blob/90f59cf7b1ad8c20b5361ebe90de56247de7d26d/agent-control-server/config/deployments/base/shoot-config-tmpls/metric-config-tidbcluster-v7-3-0.yaml#L419
{{ range $path, $_ :=  .Files.Glob "rules/cluster-next-gen/tidb-auditlog/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}


# cluster cluster-next-gen data-plane
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/additional/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
