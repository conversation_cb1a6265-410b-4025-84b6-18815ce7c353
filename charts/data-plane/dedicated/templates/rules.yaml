groups:
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/tidb/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/pd/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/tiflash/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/tikv/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/swat/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/data-platform/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tiproxy pod rules
{{ range $path, $_ :=  .Files.Glob "rules/dedicated/tiproxy/data-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tidb pod's tidb-auditlog sidecar container
# Metrics scrape config refer to
# https://github.com/tidbcloud/observability/blob/90f59cf7b1ad8c20b5361ebe90de56247de7d26d/agent-control-server/config/deployments/base/shoot-config-tmpls/metric-config-tidbcluster-v7-3-0.yaml#L419
{{ range $path, $_ :=  .Files.Glob "rules/dedicated/tidb-auditlog/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# move customer rules into data-plane
{{ range $path, $_ :=  .Files.Glob  "rules/customer/**.yaml" }}
{{- if and (ne $path "rules/customer/control-plane/k8s.rules.yaml") (ne $path "rules/customer/serverless.rules.yaml") -}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}

# resilience incident
{{ range $path, $_ :=  .Files.Glob  "rules/resilience/incident/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# resilience incident
{{ range $path, $_ :=  .Files.Glob  "rules/resilience/customized/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
