groups:
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/tidb/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/pd/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/tiflash/data-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/tikv/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ range $path, $_ :=  .Files.Glob  "rules/data-platform/data-plane/**.yaml" }}
# DP 在 dev tier 只提供 import lightning 的功能
{{- if and (ne $path "rules/data-platform/data-plane/ticdc.rules.yaml") (ne $path "rules/data-platform/data-plane/backup-restore.rules.yaml") -}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{- end -}}
{{ end }}
