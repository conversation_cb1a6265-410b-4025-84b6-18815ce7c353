groups:
# shoot & seed k8s
{{ range $path, $_ :=  .Files.Glob  "rules/common/k8s/**.rules.yaml" }}
{{- if and (ne $path "rules/common/k8s/controller-manager.rules.yaml") (ne $path "rules/common/k8s/kube-apiserver.rules.yaml") (ne $path "rules/common/k8s/kube-scheduler.rules.yaml") -}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}

# shoot & seed node-exporter
{{ range $path, $_ :=  .Files.Glob  "rules/common/node-exporter/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# shoot & seed kube-state-metrics
{{ range $path, $_ :=  .Files.Glob  "rules/common/kube-state-metrics/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# kube-dns
{{ range $path, $_ :=  .Files.Glob  "rules/common/kube-dns/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# only monitor cluster-autoscaler when use eks
{{ if contains "eks" .Values.stack }}
{{ range $path, $_ :=  .Files.Glob  "rules/common/cluster-autoscaler/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}

# shoot cert-manager
{{ range $path, $_ :=  .Files.Glob "rules/common/cert-manager/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# dbaas proxy
{{ range $path, $_ :=  .Files.Glob "rules/dedicated/dbaas-proxy/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# shoot tidb-operator
{{ range $path, $_ :=  .Files.Glob "rules/dedicated/tidb-operator/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# shoot calico
{{ range $path, $_ :=  .Files.Glob "rules/common/calico/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# shoot tiflash control-plane
{{ range $path, $_ :=  .Files.Glob "rules/dedicated/tiflash/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# shoot tidb control-plane
{{ range $path, $_ :=  .Files.Glob "rules/dedicated/tidb/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# shoot tikv control-plane
{{ range $path, $_ :=  .Files.Glob "rules/dedicated/tikv/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# shoot tiproxy control-plane
{{ range $path, $_ :=  .Files.Glob "rules/dedicated/tiproxy/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# shoot data-platform control-plane
{{ range $path, $_ :=  .Files.Glob "rules/data-platform/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# dedicated k8s components
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/k8s/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# regional infra providers、non-tidb components in dataplanes
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/regional-controlplane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# infra providers gitops
{{ range $path, $_ :=  .Files.Glob  "rules/autonomous/gitops/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# special cases for customer alert
{{ range $path, $_ :=  .Files.Glob  "rules/customer/control-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# infra provider
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/infra-provider/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# dedicated regional-server
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/regional-server/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tcidm regional-tcidm-server
{{ range $path, $_ :=  .Files.Glob  "rules/data-platform/regional-tcidm-server/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tcidm import-controller
{{ range $path, $_ :=  .Files.Glob  "rules/data-platform/import-controller/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# metering server
{{ range $path, $_ :=  .Files.Glob  "rules/autonomous/metering-server/seed/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# resilience incident
{{ range $path, $_ :=  .Files.Glob  "rules/resilience/incident/control-plane/dedicated-k8s-infra/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# dedicated swat
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/swat/control-plane/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}



##### next gen alerting #####
# cluster-next-gen cluster-server
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/cluster-server/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/dataplane-manager/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
