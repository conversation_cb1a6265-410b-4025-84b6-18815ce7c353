groups:
# k8s
{{ range $path, $_ :=  .Files.Glob  "rules/common/k8s/**.rules.yaml" }}
{{ if and (ne $path "rules/common/k8s/controller-manager.rules.yaml") (ne $path "rules/common/k8s/kube-scheduler.rules.yaml") }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}
# cert-manager
{{ range $path, $_ :=  .Files.Glob  "rules/common/cert-manager/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# node-exporter
{{ range $path, $_ :=  .Files.Glob  "rules/common/node-exporter/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# kube-state-metrics
{{ range $path, $_ :=  .Files.Glob  "rules/common/kube-state-metrics/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# kube-dns
{{ range $path, $_ :=  .Files.Glob  "rules/common/kube-dns/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# cluster-autoscaler
{{ if contains "eks" .Values.stack }}
{{ range $path, $_ :=  .Files.Glob  "rules/common/cluster-autoscaler/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}
# data-platfrom
{{ range $path, $_ :=  .Files.Glob  "rules/data-platform/data-plane/lightning.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# serverless tier
{{ range $path, $_ :=  .Files.Glob  "rules/serverless/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
