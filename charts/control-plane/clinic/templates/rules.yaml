groups:
# k8s
{{ range $path, $_ :=  .Files.Glob  "rules/common/k8s/**.rules.yaml" }}
{{ if and (ne $path "rules/common/k8s/controller-manager.rules.yaml") (ne $path "rules/common/k8s/kube-scheduler.rules.yaml") }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}
# clinic
{{ range $path, $_ :=  .Files.Glob  "rules/resilience/clinic/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
