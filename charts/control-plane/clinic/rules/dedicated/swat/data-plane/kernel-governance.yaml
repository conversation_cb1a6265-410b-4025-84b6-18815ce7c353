groups:
  - name: swat-kernel-governance
    rules:
      - alert: WetechQPSRaised
        annotations:
          message: >
            Wetech Cluster QPS last 7 days Raised more than 20%, need be scaled-out
        expr: |
          (avg_over_time(sum by (cluster_id) (rate(tidb_server_query_total{tenant=~"1372813089209061633", project!~"1372813089454536384"}[1d]))[7d:]) / avg_over_time(sum by (cluster_id) (rate(tidb_server_query_total{tenant=~"1372813089209061633", project!~"1372813089454536384"}[1d]))[14d:])) > 1.2
        for: 60s
        labels:
          severity: major
          component: resilience
          stability_governance: capacity
      - alert: TiDBCPUCoreUsageOver80ForBig3
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, value: {{ $value }}, nodegroup: {{ $labels.cluster }}'
          summary: 'TiDB single node CPU usage over 80%'
          message: >
            TiDB single node CPU usage exceeds 80% of total core usage.
        expr: |
          100 * max by (tenant, cluster_id, cluster) (
            sum(rate(node_cpu_seconds_total{
              mode!="idle",
              component=~"tidb",
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820"
            }[2m])) by (tenant, cluster_id, cluster, instance)
            /
            sum(rate(node_cpu_seconds_total{
              component=~"tidb",
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820"
            }[2m])) by (tenant, cluster_id, cluster, instance)
          ) > 80
        for: 5m
        labels:
          severity: major
          component: tidb-executor
          stability_governance: kernel-governance
      - alert: TiKVCPUCoreUsageOver80ForBig3
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, value: {{ $value }}'
          summary: 'TiKV single node CPU usage over 80%'
          message: >
            TiKV single node CPU usage exceeds 80% of total core usage.
        expr: |
          100 * max by (tenant, cluster_id) (
            sum(rate(node_cpu_seconds_total{
              mode!="idle",
              component=~"tikv",
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820",
              cluster_id!~".*10615475230796159612|.*10355969091959802668"
            }[2m])) by (tenant, cluster_id, instance)
            /
            sum(rate(node_cpu_seconds_total{
              component=~"tikv",
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820",
              cluster_id!~".*10615475230796159612|.*10355969091959802668"
            }[2m])) by (tenant, cluster_id, instance)
          ) > 80
        for: 5m
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-governance
      - alert: TiKVCPUCoreUsageOver90ForBingoC66
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, value: {{ $value }}'
          summary: 'TiKV single node CPU usage over 90%'
          message: >
            TiKV single node CPU usage exceeds 80% of total core usage.
        expr: |
          100 * max by (tenant, cluster_id) (
            sum(rate(node_cpu_seconds_total{
              mode!="idle",
              component=~"tikv",
              tenant=~".*1372813089209238420",
              project!~"1372813089454561820",
              cluster_id=~".*10355969091959802668"
            }[2m])) by (tenant, cluster_id, instance)
            /
            sum(rate(node_cpu_seconds_total{
              component=~"tikv",
              tenant=~".*1372813089209238420",
              project!~"1372813089454561820",
              cluster_id=~".*10355969091959802668"
            }[2m])) by (tenant, cluster_id, instance)
          ) > 80
        for: 20m
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-governance
      - alert: TiFlashCPUCoreUsageOver80ForBig3
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, value: {{ $value }}'
          summary: 'TiFlash single node CPU usage over 80%'
          message: >
            TiFlash single node CPU usage exceeds 80% of total core usage.
        expr: |
          100 * max by (tenant, cluster_id) (
                      sum(rate(node_cpu_seconds_total{
                        mode!="idle",
                        component=~"tiflash",
                        tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                        project!~"1372813089454536384|1372813089454561820"
                      }[2m])) by (tenant, cluster_id, instance)
                      /
                      sum(rate(node_cpu_seconds_total{
                        component=~"tiflash",
                        tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                        project!~"1372813089454536384|1372813089454561820"
                      }[2m])) by (tenant, cluster_id, instance)
                    ) > 80
        for: 5m
        labels:
          severity: major
          component: tiflash
          stability_governance: kernel-governance
      - alert: PDCPUCoreUsageOver80ForBig3
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, value: {{ $value }}'
          summary: 'PD single node CPU usage over 80%'
          message: >
            PD single node CPU usage exceeds 80% of total core usage.
        expr: |
          100 * max by (tenant, cluster_id) (
            sum(rate(node_cpu_seconds_total{
              mode!="idle",
              component=~"pd",
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820"
            }[2m])) by (tenant, cluster_id, instance)
            /
            sum(rate(node_cpu_seconds_total{
              component=~"pd",
              tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
              project!~"1372813089454536384|1372813089454561820"
            }[2m])) by (tenant, cluster_id, instance)
          ) > 80
        for: 5m
        labels:
          severity: major
          component: pd
          stability_governance: kernel-governance
      - alert: HighCPUImbalanceOnTiDB
        expr: |
          (
            (
              max by (cluster_id, cluster) (
                (
                  100 * sum(rate(node_cpu_seconds_total{
                    mode!="idle",
                    component="tidb",
                    tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                    project!~"1372813089454536384|1372813089454561820"
                  }[5m])) by (cluster_id, cluster, instance)
                )
                /
                (
                  sum(rate(node_cpu_seconds_total{
                    component="tidb",
                    tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                    project!~"1372813089454536384|1372813089454561820"
                  }[5m])) by (cluster_id, cluster, instance)
                )
              )
              /
              (
                (
                  100 * avg(
                    sum(rate(node_cpu_seconds_total{
                      mode!="idle",
                      component="tidb",
                      tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                      project!~"1372813089454536384|1372813089454561820"
                    }[5m])) by (cluster_id, cluster)
                  )
                )
                /
                (
                  avg(
                    sum(rate(node_cpu_seconds_total{
                      component="tidb",
                      tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                      project!~"1372813089454536384|1372813089454561820"
                    }[5m])) by (cluster_id, cluster)
                  )
                )
              )
            ) > 1.5
          )
          and
          (
            (
              max by (cluster_id, cluster) (
                (
                  100 * sum(rate(node_cpu_seconds_total{
                    mode!="idle",
                    component="tidb",
                    tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                    project!~"1372813089454536384|1372813089454561820"
                  }[5m])) by (cluster_id, cluster, instance)
                )
                /
                (
                  sum(rate(node_cpu_seconds_total{
                    component="tidb",
                    tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                    project!~"1372813089454536384|1372813089454561820"
                  }[5m])) by (cluster_id, cluster, instance)
                )
              )
            ) > 80
          )
        for: 5m
        labels:
          severity: major
          component: tidb-executor
          stability_governance: kernel-governance
        annotations:
          summary: "TiDB instance CPU usage is significantly higher than cluster average"
          description: "One TiDB node's CPU usage exceeds 80% and is 1.5x higher than the cluster's 15-minute average. This may indicate workload imbalance, SQL hotspots, or connection skew. Please check whether it is ddl leader / analyze owner / gc owner node."
      - alert: HighCPUImbalanceOnTiKV
        expr: |
          ((Max by (cluster_id)((100 * sum(rate(node_cpu_seconds_total{
            mode!="idle",
            component="tikv",
            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820"
          }[5m])) by (cluster_id,instance))
          /
          (sum(rate(node_cpu_seconds_total{
            component="tikv",
            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820"
          }[5m])) by (cluster_id,instance)) ))
          /
          ((100 * avg(sum(rate(node_cpu_seconds_total{
            mode!="idle",
            component="tikv",
            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820"
          }[5m])) by (cluster_id))
          /
          avg(sum(rate(node_cpu_seconds_total{
            component="tikv",
            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820"
          }[5m])) by (cluster_id))))) > 1.5
           and (Max by (cluster_id)((100 * sum(rate(node_cpu_seconds_total{
            mode!="idle",
            component="tikv",
            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820"
          }[5m])) by (cluster_id,instance))
          /
          (sum(rate(node_cpu_seconds_total{
            component="tikv",
            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820"
          }[5m])) by (cluster_id,instance)) ))>65
        for: 5m
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-governance
        annotations:
          summary: "TiKV instance CPU usage is significantly higher than cluster average"
          description: "One TiKV node's CPU usage exceeds 65% and is 1.5x higher than the cluster's 15-minute average."

#      - alert: HighCPUImbalanceOnTiFlash
#        expr: |
#          ((Max by (cluster_id)((100 * sum(rate(node_cpu_seconds_total{
#            mode!="idle",
#            component="tiflash",
#            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
#            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
#          }[5m])) by (cluster_id,instance))
#          /
#          (sum(rate(node_cpu_seconds_total{
#            component="tiflash",
#            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
#            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
#          }[5m])) by (cluster_id,instance)) ))
#          /
#          ((100 * avg(sum(rate(node_cpu_seconds_total{
#            mode!="idle",
#            component="tiflash",
#            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
#            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
#          }[5m])) by (cluster_id))
#          /
#          avg(sum(rate(node_cpu_seconds_total{
#            component="tiflash",
#            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
#            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
#          }[5m])) by (cluster_id))))) > 1.5
#           and (Max by (cluster_id)((100 * sum(rate(node_cpu_seconds_total{
#            mode!="idle",
#            component="tiflash",
#            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
#            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
#          }[5m])) by (cluster_id,instance))
#          /
#          (sum(rate(node_cpu_seconds_total{
#            component="tiflash",
#            tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209256793|.*1372813089209261054",
#            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
#          }[5m])) by (cluster_id,instance)) ))>50
#        for: 5m
#        labels:
#          severity: major
#          component: tiflash
#          stability_governance: kernel-governance
#        annotations:
#          summary: "TiFlash instance CPU usage is significantly higher than cluster average"
#          description: "One TiFlash node's CPU usage exceeds 50% and is 1.5x higher than the cluster's 15-minute average."
      - alert: TiCDCCPUCoreUsageOver80ForBig3
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, value: {{ $value }}'
          summary: 'TiCDC single node CPU usage over 80%'
          message: >
            TiCDC single node CPU usage exceeds 80% of total core usage.
        expr: |
          (
                rate(process_cpu_seconds_total{
                  component="ticdc",
                  tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                  project!~"1372813089454536384|1372813089454561820",
                  cluster_id=~".*10615475230796159612"
                }[2m])
                /
                ticdc_server_go_max_procs{
                  component="ticdc",
                  tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054",
                  project!~"1372813089454536384|1372813089454561820",
                  cluster_id=~".*10615475230796159612"
                }
              ) * 100 > 80
        for: 5m
        labels:
          severity: major
          component: ticdc-kernel
          stability_governance: kernel-governance
      - alert: TiDBServerDowntimeOver30MinForBig3
        expr: |
          up{
            component=~"tidb",
            job=~".*tidb",
            tenant=~"1372813089209061633|1372813089209238420|1372813089209256793|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
          } == 0
        for: 30m
        labels:
          tier: dedicated
          severity: critical
          component: infra-provider
          stability_governance: kernel-governance
        annotations:
          description: 'Cluster: {{ $labels.cluster_id }}, Instance: {{ $labels.instance }} (Component: {{ $labels.component }}) is down for more than 30 minutes.'
          summary: '{{ $labels.component }} server has been down for over 30 minutes'
          message: >
            {{ $labels.component }} instance {{ $labels.instance }} has been down for over 30 minutes.
            Please investigate.
      - alert: TiKVDowntimeOver30MinForBig3
        expr: |
          up{
            component=~"tikv",
            job=~".*tikv",
            tenant=~"1372813089209061633|1372813089209238420|1372813089209256793|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
          } == 0
        for: 30m
        labels:
          tier: dedicated
          severity: critical
          component: infra-provider
          stability_governance: kernel-governance
        annotations:
          description: 'Cluster: {{ $labels.cluster_id }}, Instance: {{ $labels.instance }} (Component: {{ $labels.component }}) is down for more than 30 minutes.'
          summary: '{{ $labels.component }} server has been down for over 30 minutes'
          message: >
            {{ $labels.component }} instance {{ $labels.instance }} has been down for over 30 minutes.
            Please investigate.
      - alert: TiFlashDowntimeOver30MinForBig3
        expr: |
          up{
            component=~"tiflash",
            job=~".*tiflash",
            tenant=~"1372813089209061633|1372813089209238420|1372813089209256793|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
          } == 0
        for: 30m
        labels:
          tier: dedicated
          severity: critical
          component: infra-provider
          stability_governance: kernel-governance
        annotations:
          description: 'Cluster: {{ $labels.cluster_id }}, Instance: {{ $labels.instance }} (Component: {{ $labels.component }}) is down for more than 30 minutes.'
          summary: '{{ $labels.component }} server has been down for over 30 minutes'
          message: >
            {{ $labels.component }} instance {{ $labels.instance }} has been down for over 30 minutes.
            Please investigate.
      - alert: PDDowntimeOver30MinForBig3
        expr: |
          up{
            component=~"pd",
            job=~".*pd",
            tenant=~"1372813089209061633|1372813089209238420|1372813089209256793|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
          } == 0
        for: 30m
        labels:
          tier: dedicated
          severity: critical
          component: resilience
          stability_governance: kernel-governance
        annotations:
          description: 'Cluster: {{ $labels.cluster_id }}, Instance: {{ $labels.instance }} (Component: {{ $labels.component }}) is down for more than 30 minutes.'
          summary: '{{ $labels.component }} server has been down for over 30 minutes'
          message: >
            {{ $labels.component }} instance {{ $labels.instance }} has been down for over 30 minutes.
            Please investigate.
      - alert: TiCDCDowntimeOver30MinForBig3
        expr: |
          up{
            component=~"ticdc",
            job=~".*ticdc",
            tenant=~"1372813089209061633|1372813089209238420|1372813089209256793|.*1372813089209261054",
            project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"
          } == 0
        for: 30m
        labels:
          tier: dedicated
          severity: critical
          component: ticdc
          stability_governance: kernel-governance
        annotations:
          description: 'Cluster: {{ $labels.cluster_id }}, Instance: {{ $labels.instance }} (Component: {{ $labels.component }}) is down for more than 30 minutes.'
          summary: '{{ $labels.component }} server has been down for over 30 minutes'
          message: >
            {{ $labels.component }} instance {{ $labels.instance }} has been down for over 30 minutes.
            Please investigate.
      - alert: TiKVMemoryCoreUsageOver85ForBig3
        annotations:
          message: >
            TiKV node memory utilization over 85%. value: {{ $value }}
            source_tcoc: TCOC-2913
        expr: |-
          max by(cluster_id)(
          100 * (1 - (
              node_memory_MemAvailable_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820",cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
              /
              node_memory_MemTotal_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820",cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
            )) 
          )> 85
        for: 10m
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-2913
      # bingo 的三套集群包括 prod-dc Prod-Jili Prod-Others 都有类似的问题. 可能和 bingo 遇到的其他内存异常和 Bingoplus TiKV 内存问题调查记录 类似，目前不确定 root cause，如果后续周期性一直上涨明显接近 OOM 的话，只能先走 evict-leader + restart 来处理了.
      - alert: TiKVMemoryCoreUsageOver90ForBingo3Clusters
        annotations:
          message: >
            TiKV node memory utilization over 90%. value: {{ $value }}
            source_tcoc: TCOC-2913
        expr: |-
          100 * (1 - (
              node_memory_MemAvailable_bytes{component="tikv", cluster_id=~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
              /
              node_memory_MemTotal_bytes{component="tikv", cluster_id=~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
            )) > 90
        for: 10m
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-2913
      - alert: TotalTiKVMemoryUsageOver80ForBig3
        annotations:
          message: |
            Total TiKV memory utilization over 80%.  value: {{ $value }}
          description: Total TiKV memory utilization over 80% for 10 minutes
        expr: |-
          1 - avg by (tidb_cluster_id) (
            (
              node_memory_MemFree_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"} +
              node_memory_Buffers_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"} +
              node_memory_Cached_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
            ) 
            / 
            node_memory_MemTotal_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
          ) > 0.8
        for: 10m
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-2913
      - alert: TiKVCoreMemoryUsageOverAvgTotalForBig3
        annotations:
          message: |
            TiKV single node memory utilization over (Total TiKV node memory utilization + 5).  value: {{ $value }}
            source_tcoc: TCOC-2913
          description: TiKV single node memory utilization over (Total TiKV node memory utilization + 5)
        expr: |-
          (100 * (1 - (
              node_memory_MemAvailable_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
              /
              node_memory_MemTotal_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
            ))) > (100 * (1 - avg by (tidb_cluster_id) (
            (
              node_memory_MemFree_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"} +
              node_memory_Buffers_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"} +
              node_memory_Cached_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
            ) 
            / 
            node_memory_MemTotal_bytes{component="tikv", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10264132336816574236|.*10890423899572033652|.*10594996726066304073"}
          ))+5)
        for: 10m
        labels:
          severity: major
          component: tikv
          stability_governance: kernel-tcocp
          source_tcoc: TCOC-2913
      - alert: TiDBP999LatencySurgeForBig3
        expr: |
          histogram_quantile(0.999, sum(rate(tidb_server_handle_query_duration_seconds_bucket{component="tidb", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10615475230796159612"}[1m])) by (le, cluster_id))
          >
          10 * histogram_quantile(0.999, avg_over_time(
            sum by (le, cluster_id) (
              rate(tidb_server_handle_query_duration_seconds_bucket{component="tidb", tenant=~".*1372813089209061633|.*1372813089209238420|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10615475230796159612"}[5m])
            )[24h:5m]
          ))
        for: 2m
        labels:
          tier: dedicated
          severity: critical
          component: resilience
          stability_governance: kernel-tcocp
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, current 2-minutes P999 latency is {{ $value }}s, which exceeds 10x of the 24h P999 baseline'
          summary: TiDB query P999 latency significantly increased in short term
          message: >
            TiDB query P999 latency within the past 2 minutes exceeds 10 times the 24-hour P999 baseline.
            This may indicate query slowness, hotspots, resource contention, or downstream degradation.
            SOP: https://pingcap.feishu.cn/wiki/CbEqwrDpaivA0bkxd5xcmpaQnDb
      - alert: TiDBP999LatencySurgeForBingoMessage
        expr: |
          histogram_quantile(0.999, sum(rate(tidb_server_handle_query_duration_seconds_bucket{component="tidb", cluster_id=~".*10615475230796159612"}[1m])) by (le, cluster_id))
          >
          25 * histogram_quantile(0.999, avg_over_time(
            sum by (le, cluster_id) (
              rate(tidb_server_handle_query_duration_seconds_bucket{component="tidb", cluster_id=~".*10615475230796159612"}[5m])
            )[24h:5m]
          ))
        for: 2m
        labels:
          tier: dedicated
          severity: critical
          component: resilience
          stability_governance: kernel-tcocp
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, current 2-minutes P999 latency is {{ $value }}s, which exceeds 25x of the 24h P999 baseline'
          summary: TiDB query P999 latency significantly increased in short term
          message: >
            TiDB query P999 latency within the past 2 minutes exceeds 25 times the 24-hour P999 baseline.
            This may indicate query slowness, hotspots, resource contention, or downstream degradation.
            SOP: https://pingcap.feishu.cn/wiki/CbEqwrDpaivA0bkxd5xcmpaQnDb
      - alert: TiDBP95LatencySurgeForBig3
        expr: |
          histogram_quantile(0.95, sum(rate(tidb_server_handle_query_duration_seconds_bucket{component="tidb",tenant=~"1372813089209061633|1372813089209238420|1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10615475230796159612|.*10890423899572033652"}[1m])) by (le, cluster_id))
          >
          5 * histogram_quantile(0.95, avg_over_time(
            sum by (le, cluster_id) (
              rate(tidb_server_handle_query_duration_seconds_bucket{component="tidb",tenant=~"1372813089209061633|1372813089209238420|1372813089209261054", project!~"1372813089454536384|1372813089454561820", cluster_id!~".*10615475230796159612|.*10890423899572033652"}[5m])
            )[24h:5m]
          ))
        for: 5m
        labels:
          tier: dedicated
          severity: critical
          component: resilience
          stability_governance: kernel-tcocp
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, current 5-minutes P95 latency is {{ $value }}s, which exceeds 5x of the 24h P95 baseline'
          summary: TiDB query P95 latency significantly increased in short term
          message: >
            TiDB query P95 latency within the past 5 minutes exceeds 5 times the 24-hour P95 baseline.
            This may indicate query slowness, hotspots, resource contention, or downstream degradation.
            SOP: https://pingcap.feishu.cn/wiki/CbEqwrDpaivA0bkxd5xcmpaQnDb
      - alert: TiDBP95LatencySurgeForBingoProdDC
        expr: |
          histogram_quantile(0.95, sum(rate(tidb_server_handle_query_duration_seconds_bucket{component="tidb",tenant=~"1372813089209238420", project!~"1372813089454561820", cluster_id=~".*10890423899572033652"}[1m])) by (le, cluster_id))
          >
          7 * histogram_quantile(0.95, avg_over_time(
            sum by (le, cluster_id) (
              rate(tidb_server_handle_query_duration_seconds_bucket{component="tidb",tenant=~"1372813089209238420", project!~"1372813089454561820", cluster_id=~".*10890423899572033652"}[5m])
            )[24h:5m]
          ))
        for: 5m
        labels:
          tier: dedicated
          severity: critical
          component: resilience
          stability_governance: kernel-tcocp
        annotations:
          description: 'cluster: {{ $labels.cluster_id }}, current 5-minutes P95 latency is {{ $value }}s, which exceeds 7x of the 24h P95 baseline'
          summary: TiDB query P95 latency significantly increased in short term
          message: >
            TiDB query P95 latency within the past 5 minutes exceeds 7 times the 24-hour P95 baseline.
            This may indicate query slowness, hotspots, resource contention, or downstream degradation.
            SOP: https://pingcap.feishu.cn/wiki/CbEqwrDpaivA0bkxd5xcmpaQnDb
      - alert: RequestErrorRateOver10ForBig3
        expr: |-
          (
            sum(increase(tidb_server_execute_error_total{tenant=~"1372813089209061633|1372813089209238420|1372813089209256793|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"}[5m])) by (cluster_id)
            /
            sum(increase(tidb_executor_statement_total{tenant=~"1372813089209061633|1372813089209238420|1372813089209256793|.*1372813089209261054", project!~"1372813089454536384|1372813089454561820|1372813089454576822|1372813089454574660"}[5m])) by (cluster_id)
          ) > 0.1
        for: 0s
        labels:
          severity: critical
          component: tidb-optimizer
          stability_governance: kernel-governance
        annotations:
          message: >
            Cluster {{ $labels.cluster_id }}  the error rate is above 10%. Please check the recent errors and overall statement execution status for this cluster.
