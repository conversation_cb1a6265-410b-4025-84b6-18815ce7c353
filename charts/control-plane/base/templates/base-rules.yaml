groups:
# EKS and GKE needn't to monitor k8s control-plane
{{ range $path, $_ :=  .Files.Glob  "rules/common/k8s/**.rules.yaml" }}
{{- if and (ne $path "rules/common/k8s/controller-manager.rules.yaml") (ne $path "rules/common/k8s/kube-apiserver.rules.yaml") (ne $path "rules/common/k8s/kube-scheduler.rules.yaml") -}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}
# base metadb
{{ range $path, $_ :=  .Files.Glob  "rules/cloud-platform/metadb/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path) }}
{{ end }}
# central
{{ range $path, $_ :=  .Files.Glob  "rules/cloud-platform/central/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# account
{{ range $path, $_ :=  .Files.Glob  "rules/cloud-platform/account/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# api-gateway
{{ range $path, $_ :=  .Files.Glob  "rules/cloud-platform/api-gateway/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# dataservice
{{ range $path, $_ :=  .Files.Glob  "rules/cloud-platform/dataservice/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# billing
{{ range $path, $_ :=  .Files.Glob  "rules/cloud-platform/billing/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# aipower
{{ range $path, $_ :=  .Files.Glob  "rules/cloud-platform/aipower/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# cert-manager
{{ range $path, $_ :=  .Files.Glob  "rules/common/cert-manager/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# loki
{{ range $path, $_ :=  .Files.Glob  "rules/autonomous/loki/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# base node-exporter
{{ range $path, $_ :=  .Files.Glob  "rules/common/node-exporter/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# infra-api cluster operation
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/cluster-operation/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# nginx-ingress
{{ range $path, $_ :=  .Files.Glob  "rules/common/nginx-ingress/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# base kube-state-metrics
{{ range $path, $_ :=  .Files.Glob  "rules/common/kube-state-metrics/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# kube-dns
{{ range $path, $_ :=  .Files.Glob  "rules/common/kube-dns/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# only monitor cluster-autoscaler when use eks
{{ if contains "eks" .Values.stack }}
{{ range $path, $_ :=  .Files.Glob  "rules/common/cluster-autoscaler/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}
# config-manager
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/config-manager/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# gardener-etcd-br
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/etcd-br/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# infra etcd
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/infra-etcd/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# infra apiserver
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/infra-apiserver/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# infra provider
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/infra-provider/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# infra cost provider
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/cost-provider/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# ecosystem
{{ range $path, $_ :=  .Files.Glob  "rules/ecosystem-service/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
# metrics-server
{{ range $path, $_ :=  .Files.Glob  "rules/autonomous/metrics-server/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# o11y-infra
{{ range $path, $_ :=  .Files.Glob  "rules/autonomous/o11y-infra/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# dedicated k8s components
{{ range $path, $_ :=  .Files.Glob  "rules/dedicated/k8s/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# data-platform
{{ range $path, $_ :=  .Files.Glob  "rules/data-platform/base/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tidb-service core-svc
{{ range $path, $_ :=  .Files.Glob  "rules/tidb-service/core-svc/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# autonomous gitops
{{ range $path, $_ :=  .Files.Glob  "rules/autonomous/gitops/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# serverless-service
{{ range $path, $_ :=  .Files.Glob  "rules/serverless-service/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# autonomous tidb-management-service
{{ range $path, $_ :=  .Files.Glob  "rules/autonomous/tidb-management-service/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# resilience incident
{{ range $path, $_ :=  .Files.Glob  "rules/resilience/incident/control-plane/base/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# slo
{{ range $path, $_ :=  .Files.Glob  "rules/resilience/slo/control-plane/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# special cases for customer alert
{{ range $path, $_ :=  .Files.Glob  "rules/customer/base/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# metering server for instance
{{ range $path, $_ :=  .Files.Glob  "rules/autonomous/metering-server/base/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
