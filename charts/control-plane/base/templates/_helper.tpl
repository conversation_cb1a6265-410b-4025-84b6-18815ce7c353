{{- define "rulesIndentFormat" -}}
{{/* append parent dir of file to group name, to avoid duplicate*/}}
{{- $template := print "- name: " (regexReplaceAll "(.*\\/)(\\S+)\\/.*" .path "${2}") "/${1}" }}
{{/* some yaml files may contain indent, some are not.
  Finding rules that contains indent and trim them */}}
{{- if .files.Get .path | contains "  - name:" -}}
{{ range .files.Lines .path }}
{{ regexReplaceAll "- name: (.+)" (.| trimPrefix "  " | replace "groups:" "") $template }}{{ end }}
{{- else -}}
{{ regexReplaceAll "- name: (.+)" (.files.Get .path |replace "groups:" "") $template}}
{{ end }}
{{- end }}
