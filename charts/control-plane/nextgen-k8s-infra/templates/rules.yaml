groups:
# k8s common
{{ range $path, $_ :=  .Files.Glob  "rules/common/k8s/**.rules.yaml" }}
{{- if and (ne $path "rules/common/k8s/controller-manager.rules.yaml") (ne $path "rules/common/k8s/kube-apiserver.rules.yaml") (ne $path "rules/common/k8s/kube-scheduler.rules.yaml") (ne $path "rules/common/k8s/o11y-agent-absent.rules.yaml") -}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}

# node-exporter
{{ range $path, $_ :=  .Files.Glob  "rules/common/node-exporter/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# kube-state-metrics
{{ range $path, $_ :=  .Files.Glob  "rules/common/kube-state-metrics/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# kube-dns
{{ range $path, $_ :=  .Files.Glob  "rules/common/kube-dns/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# monitor cluster-autoscaler only when use eks
{{ if contains "eks" .Values.stack }}
{{ range $path, $_ :=  .Files.Glob  "rules/common/cluster-autoscaler/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
{{ end }}

# cert-manager
{{ range $path, $_ :=  .Files.Glob "rules/common/cert-manager/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tidb-operator
{{ range $path, $_ :=  .Files.Glob "rules/cluster-next-gen/tidb-operator/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tiflash control-plane
{{ range $path, $_ :=  .Files.Glob "rules/cluster-next-gen/tiflash/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tidb control-plane
{{ range $path, $_ :=  .Files.Glob "rules/cluster-next-gen/tidb/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tikv control-plane
{{ range $path, $_ :=  .Files.Glob "rules/cluster-next-gen/tikv/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# tiproxy control-plane
{{ range $path, $_ :=  .Files.Glob "rules/cluster-next-gen/tiproxy/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# dedicated k8s components
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/k8s/**.rules.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# cluster cluster-next-gen data-plane
{{ range $path, $_ :=  .Files.Glob  "rules/cluster-next-gen/additional/**.yaml" }}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}

# ticdc control-plane
{{ range $path, $_ :=  .Files.Glob "rules/cluster-next-gen/ticdc/control-plane/**.rules.yaml"}}
{{ include "rulesIndentFormat" (dict "files" $.Files "path" $path)}}
{{ end }}
