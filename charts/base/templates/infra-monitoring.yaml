{{- if .Values.enableInfraMonitor }}
# infra apiserver
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: infra-apiserver
  name: infra-apiserver
  namespace: infra
spec:
  endpoints:
    - interval: 30s
      port: kube-apiserver
      scheme: https
      bearerTokenSecret:
        name: prometheus-token
        key: token
      tlsConfig:
        insecureSkipVerify: true
        serverName: api-server
        ca:
          secret:
            name: api-server-admin
            key: ca.crt
  jobLabel: component
  namespaceSelector:
    matchNames:
      - infra
  selector:
    matchLabels:
      app.kubernetes.io/component: kube-apiserver
      app.kubernetes.io/name: api-server
---
## infra etcd
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: infra-etcd
  namespace: infra
spec:
  endpoints:
    - interval: 30s
      scrapeTimeout: 10s
      scheme: https
      port: client
      tlsConfig:
        insecureSkipVerify: false
        serverName: 127.0.0.1
        ca:
          secret:
            name: etcd-client
            key: ca.crt
        cert:
          secret:
            name: etcd-client
            key: tls.crt
        keySecret:
          name: etcd-client
          key: tls.key
  namespaceSelector:
    matchNames:
      - infra
  selector:
    matchLabels:
      app.kubernetes.io/instance: etcd
      app.kubernetes.io/name: etcd

---
apiVersion: v1
kind: Secret
metadata:
  name: prometheus-token
  namespace: infra
type: Opaque
data:
  token: {{ .Values.infraMonitoringToken }}
---
# only monitor cluster-autoscaler when use eks
{{ if contains "eks" .Values.stack }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}cluster-autoscaler
  namespace: kube-system
  labels:
    app: cluster-autoscaler
spec:
  selector:
    app: cluster-autoscaler
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8085
    targetPort: 8085
    protocol: TCP
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Release.Name }}-cluster-autoscaler
  namespace: kube-system
spec:
  endpoints:
  - interval: 30s
    scrapeTimeout: 10s
    path: /metrics
    port: http
  namespaceSelector:
    matchNames:
      - 'kube-system'
  selector:
    matchLabels:
      app: cluster-autoscaler
{{ end }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .Release.Name }}-garden-etcd-br-metrics
  namespace: garden
  labels:
    app: garden-etcd-br
spec:
  selector:
    app: garden-etcd-main
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    protocol: TCP
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ .Release.Name }}-garden-etcd-br
  namespace: garden
spec:
  endpoints:
  - interval: 30s
    scrapeTimeout: 10s
    path: /metrics
    port: http
  namespaceSelector:
    matchNames:
      - 'garden'
  selector:
    matchLabels:
      app: garden-etcd-br
{{- end }}
