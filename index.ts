import * as aws from "@pulumi/aws";
import * as docker from "@pulumi/docker";
import * as k8s from "@pulumi/kubernetes";
import * as pulumi from "@pulumi/pulumi";
import * as fs from "fs";

const config = new pulumi.Config();
const eksStackPrefix = "sre-bot/eks/";
const gkeStackPrefix = "sre-bot/gke/";
const pagerdutyKey = config.requireSecret("pagerduty_key");
const dbaasPlatformPagerdutyKey = config.requireSecret(
  "dbaas_platform_pagerduty_key",
);

const freetierPagerdutyKey = config.requireSecret("freetierPagerdutyKey");
const sharedtierPagerdutyKey = config.requireSecret("sharedtierPagerdutyKey");
const dpPagerdutyKey = config.requireSecret("dpPagerDutyKey");
const computePagerdutyKey = config.requireSecret("computePagerDutyKey");
const storagePagerdutyKey = config.requireSecret("storagePagerDutyKey");
const jpTamPagerdutyKey = config.requireSecret("jpPagerDutyKey");
const autonomousPagerdutyKey = config.requireSecret("autonomousPagerDutyKey");
const ecosystemPagerKey = config.requireSecret("ecosystemPagerDutyKey");
const gatewayTlsPagerKey = config.requireSecret("gatewayTlsPagerDutyKey");

const env = config.require("env");
const enableAlertmanager = config.get("enableAlertmanager") || false;
const enableInfra = config.get("enableInfra") || false;
const enableCumstomerAlert = config.get("enableCumstomerAlert") || false;
const infraMonitoringToken = config.get("infraMonitoringToken") || "";
const alertDnsZone =
  config.get("alert-dns-zone") || `${env}.shared.aws.tidbcloud.com`;
const slackUrl = config.requireSecret("slack_url");
const oauthProxyDnsZone =
  config.get("oauth-proxy-dns-zone") || `oauth.${alertDnsZone}`;
const grafanaAuth0Domain = config.get("grafanaAuth0Domain");
const grafanaAuth0ClientId = config.get("grafanaAuth0ClientId");
const grafanaAuth0ClientSecret = config.getSecret("grafanaAuth0ClientSecret");

const baseStack = config.require("infra-base-stack");
const seedStacks = config.requireObject<string[]>("infra-seed-stacks");
const freetierStacks = config.getObject<string[]>("freetier-stacks");

const o11ySeedStackWhitelist =
  config.getObject<string[]>("o11y-seed-stack-whitelist") || [];

const needDisabledGardenNamespace =
  config.getObject<string[]>("need-disabled-garden-namespace") || [];

const portalNamespace = config.get("portal-namespace") || env;
let repeatInterval = "3h";
if (env === "dev") {
  repeatInterval = "5m";
}

let baseProvider: k8s.Provider;

if (process.env.TEST_MODE) {
  baseProvider = new k8s.Provider(
    `${baseStack.replace(eksStackPrefix, "")}-provider`,
    {
      renderYamlToDirectory: `./render-${baseStack}`,
    },
  );
} else {
  const base = new pulumi.StackReference(`${baseStack}`);
  baseProvider = new k8s.Provider("k8s", {
    kubeconfig: base.requireOutput("kubeconfig"),
  });
}

if (enableAlertmanager) {
  const alertManagerConfig = new k8s.core.v1.Secret(
    "alertmanager-main",
    {
      metadata: {
        name: "alertmanager-main",
        namespace: "monitoring",
      },
      stringData: {
        "alertmanager.yaml": pulumi
          .all([
            ecosystemPagerKey,
            gatewayTlsPagerKey,
            pagerdutyKey,
            dbaasPlatformPagerdutyKey,
            freetierPagerdutyKey,
            sharedtierPagerdutyKey,
            dpPagerdutyKey,
            computePagerdutyKey,
            storagePagerdutyKey,
            jpTamPagerdutyKey,
            autonomousPagerdutyKey,
            slackUrl,
            portalNamespace,
            repeatInterval,
          ])
          .apply(
            ([
              ecosystemKey,
              gatewayTlsKey,
              key,
              dbaasPlatformKey,
              freetierKey,
              sharedtierKey,
              dpKey,
              computeKey,
              storageKey,
              jpKey,
              autonomousKey,
              url,
              internalPortalNamespace,
            ]) =>
              `
global:
templates:
- '/etc/alertmanager/*.tmpl'
route:
  receiver: slack-for-warning
  group_by: ['alertname', 'level', 'severity', 'priority', 'rule', 'cluster', 'tenant', 'cluster_id']
  group_wait: 10s
  repeat_interval: 30m
  routes:
  # [2022/11/29 Deprecation Notice]: the alert stack is migrating to o11y infra,
  # changes to alert routes under runbooks will no longer take effect.
  # If you want to add new component or change the existing routes, please
  # contact @just1900 or @charlesmpc first.
    - receiver: junkyard
      repeat_interval: ${repeatInterval}
      group_by: ['alertname', 'cluster_id']
      continue: false
      match:
        alerttype: 'customer'
    - receiver: junkyard
      group_wait: 10s
      group_interval: 30s
      repeat_interval: 15s
      group_by: ['alertname', 'level', 'severity', 'priority', 'rule']
      match:
        alertname: 'Watchdog'#
    # Jiralert Service Routes
    #- receiver: jiralert-${env}
    #  group_by: ['...']
    #  continue: true
    #  group_wait: 10s
    #  group_interval: 10m
    #  repeat_interval: 2h
    #  match_re:
    #    severity: critical|emergency|blocker|major|warning
    #  routes:
    #    - receiver: jiralert-${env}-major
    #      match_re:
    #        severity: major
    #    - receiver: jiralert-${env}-warning
    #      match_re:
    #        severity: warning##
    # PagerDuty Routes
    - receiver: junkyard
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1m
      continue: true
      match_re:
        severity: blocker|emergency|critical
      routes:
      # Fan-out all alerts to JP TAM team
      #- receiver: jp_tam
      #  match_re:
      #    severity: blocker|emergency|critical
      #  continue: true
      - receiver: junkyard
        match_re:
          provider_type: aws-free-tier
          severity: blocker|emergency|critical
        continue: false
      - receiver: junkyard
        match_re:
          namespace: free-tier.*
          severity: blocker|emergency|critical
        continue: false
      - receiver: junkyard
        match_re:
          provider_type: aws-shared-tier
          severity: blocker|emergency|critical
        continue: false
      - receiver: junkyard
        match_re:
          namespace: shared-tier.*
          severity: blocker|emergency|critical
        continue: false
      - receiver: junkyard
        match_re:
          component: 'alertmanager|thanos|o11y|metrics-server|vmagent|loki'
        continue: false
        # for pod level alert like crashloop and volume filling up
      - receiver: junkyard
        matchers:
        - namespace = monitoring
        - service != quota-monitor
        continue: false
      - receiver: junkyard
        match_re:
          component: 'br|lightning|ticdc|data-migration'
        continue: false
        routes:
        - receiver: junkyard
          match_re:
            alerttype: 'event'
      - receiver: junkyard
        continue: false
        match_re:
          component: 'api-gateway|billing-server|account-server|metadb'
      - receiver: junkyard
        continue: false
        match_re:
          component: 'ecosystem-server'
      - receiver: junkyard
        continue: false
        match_re:
          component: 'gateway_TLS'
      #- receiver: kernel_sql
      #  continue: false
      #  match_re:
      #    component: 'tidb'
      #    tier: 'dedicated'
      #- receiver: kernel_storage
      #  continue: false
      #  match_re:
      #    component: 'tikv|pd|tiflash'
      #    tier: 'dedicated'
      - receiver: junkyard
        continue: false
        match_re:
          severity: blocker|emergency|critical

    # Slack Routes
    - receiver: slack
      match_re:
        severity: critical|emergency|blocker|warning
      group_wait: 10s
      continue: true
      group_interval: 10m
      repeat_interval: 2h
      routes:
      - receiver: slack-for-warning
        match_re:
          severity: warning
receivers:
- name: junkyard
- name: slack
  slack_configs:
  - api_url: ${url}
    send_resolved: true
    channel: 'dbaas-alerts-${env}'
    color: '{{ if eq .Status "firing" }}{{ if or (eq .CommonLabels.severity "warning") (eq .CommonLabels.level "warning")}}warning{{ else if or (eq .CommonLabels.severity "critical")  (eq .CommonLabels.level "critical") (eq .CommonLabels.level "emergency")}}danger{{ else }}
    #439FE0{{ end }}{{ else }}good{{ end }}'
    username: '{{ template "slack.default.username" . }}'
    icon_emoji: ':tidb:'
    icon_url: https://avatars3.githubusercontent.com/u/3380462
    title: ':fire:[{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{- end -}}] {{ .CommonLabels.alertname }}{{ .CommonLabels.rule }}'
    text: |-
      {{ range .Alerts }}
      *Alert:* {{ .Annotations.summary }} {{ .Annotations.message }} {{ .Annotations.info }}
      {{- if .Annotations.description }}
      *Description:* {{ .Annotations.description }}
      {{- end }}
      *Details:*
      {{ range .Labels.SortedPairs }} • *{{ .Name }}:* \`{{ .Value }}\`
      {{ end }}
      {{ end }}
    actions:
      - type: button
        text: 'Query :mag:'
        url: 'https://thanos-query.oauth.${alertDnsZone}/{{ (index .Alerts 0).GeneratorURL | reReplaceAll "http://prometheus-k8s-0:9090" "" | reReplaceAll "https://" ""}}'
        name: query
      - name: runbook
        type: button
        text: 'Runbook :green_book:'
        url: '{{ (index .Alerts 0).Annotations.runbook_url }}'
      - name: grafana
        type: button
        text: 'Grafana :grafana:'
        url: 'https://grafana.oauth.${alertDnsZone}/{{ (index .Alerts 0).Annotations.dashboard }}'
      - name: logging
        type: button
        text: 'Logs :logs:'
        url: 'https://grafana.oauth.${alertDnsZone}/explore?{{ (index .Alerts 0).Annotations.logging }}'

- name: slack-for-warning
  slack_configs:
  - api_url: ${url}
    send_resolved: true
    channel: 'dbaas-alerts-warning-${env}'
    color: '{{ if eq .Status "firing" }}{{ if or (eq .CommonLabels.severity "warning") (eq .CommonLabels.level "warning")}}warning{{ else if or (eq .CommonLabels.severity "critical")  (eq .CommonLabels.level "critical") (eq .CommonLabels.level "emergency")}}danger{{ else }}
    #439FE0{{ end }}{{ else }}good{{ end }}'
    username: '{{ template "slack.default.username" . }}'
    icon_emoji: ':tidb:'
    icon_url: https://avatars3.githubusercontent.com/u/3380462
    title: ':fire:[{{ .Status | toUpper }}{{ if eq .Status "firing" }}:{{ .Alerts.Firing | len }}{{- end -}}] {{ .CommonLabels.alertname }}{{ .CommonLabels.rule }}'
    text: |-
      {{ range .Alerts }}
      *Alert:* {{ .Annotations.summary }} {{ .Annotations.message }} {{ .Annotations.info }}
      {{- if .Annotations.description }}
      *Description:* {{ .Annotations.description }}
      {{- end }}
      *Details:*
      {{ range .Labels.SortedPairs }} • *{{ .Name }}:* \`{{ .Value }}\`
      {{ end }}
      {{ end }}
    actions:
      - type: button
        text: 'Query :mag:'
        url: 'https://thanos-query.oauth.${alertDnsZone}/{{ (index .Alerts 0).GeneratorURL | reReplaceAll "http://prometheus-k8s-0:9090" "" | reReplaceAll "https:/" ""}}'
        name: query
      - name: runbook
        type: button
        text: 'Runbook :green_book:'
        url: '{{ (index .Alerts 0).Annotations.runbook_url }}'
      - name: grafana
        type: button
        text: 'Grafana :grafana:'
        url: 'https://grafana.oauth.${alertDnsZone}/{{ (index .Alerts 0).Annotations.dashboard }}'
      - name: logging
        type: button
        text: 'Logs :logs:'
        url: 'https://grafana.oauth.${alertDnsZone}/explore?{{ (index .Alerts 0).Annotations.logging }}'

- name: autonomous_team
  pagerduty_configs:
  - service_key: ${autonomousKey}
    send_resolved: true
- name: jp_tam
  pagerduty_configs:
  - service_key: ${jpKey}
    send_resolved: true
- name: tidb_service
  pagerduty_configs:
  - service_key: ${key}
    send_resolved: true
- name: kernel_sql
  pagerduty_configs:
  - service_key: ${computeKey}
    send_resolved: true
- name: kernel_storage
  pagerduty_configs:
  - service_key: ${storageKey}
    send_resolved: true
- name: cloud_platform
  pagerduty_configs:
  - service_key: ${dbaasPlatformKey}
    send_resolved: true
- name: data_platform
  pagerduty_configs:
  - service_key: ${dpKey}
    send_resolved: true
- name: data_platform_event
  pagerduty_configs:
  - service_key: ${dpKey}
    # event type alert should not send resolved
    send_resolved: false
- name: freetier
  pagerduty_configs:
  - service_key: ${freetierKey}
    send_resolved: true
- name: sharedtier
  pagerduty_configs:
  - service_key: ${sharedtierKey}
    send_resolved: true
- name: ecosystem
  pagerduty_configs:
  - service_key: ${ecosystemKey}
    send_resolved: true
- name: gatewayTls
  pagerduty_configs:
  - service_key: ${gatewayTlsKey}
    send_resolved: true
# dead-mans-switch
- name: dead-mans-switch
  webhook_configs:
  - url: http://dead-mans-switch:8080/webhook
# alert for customer
- name: customer
  webhook_configs:
  # send alert to internal-portal-server-http server
  - url: http://internal-portal-server-http.${internalPortalNamespace}.svc.cluster.local:8081/api/useralerts/v1/alert

- name: 'jiralert-${env}'
  webhook_configs:
  - url: 'http://54.219.132.198:9097/alert'
    send_resolved: true
- name: 'jiralert-${env}-major'
  webhook_configs:
  - url: 'http://54.219.132.198:9097/alert'
    send_resolved: true
- name: 'jiralert-${env}-warning'
  webhook_configs:
  - url: 'http://54.219.132.198:9097/alert'
    send_resolved: true

# Mute falco priority <= Error alert
inhibit_rules:
- target_match_re:
    source: falco
    priority: Notice|Warning|Error
  source_match:
    alertname: 'Watchdog'`,
          ),
      },
    },
    { provider: baseProvider },
  );

  const alertManagerTemplate = new k8s.core.v1.ConfigMap(
    "alertmanager-templates-tmpl",
    {
      metadata: {
        namespace: "monitoring",
        name: "alertmanager-templates-tmpl",
      },
      data: {
        "default.tmpl": fs.readFileSync("tmpl/default.tmpl").toString(),
        "slack.tmpl": fs.readFileSync("tmpl/slack.tmpl").toString(),
      },
    },
    { provider: baseProvider },
  );
}
new k8s.helm.v3.Chart(
  `${baseStack.replace(eksStackPrefix, "")}`,
  {
    path: "./charts/base",
    values: {
      stack: baseStack,
      alertDnsZone: alertDnsZone,
      enableAlertmanager: enableAlertmanager,
      enableInfra: enableInfra,
      infraMonitoringToken: infraMonitoringToken,
      env: env,
      enableCumstomerAlert: enableCumstomerAlert,
    },
  },
  { provider: baseProvider },
);

new k8s.core.v1.ConfigMap(
  "grafana-ini",
  {
    metadata: {
      name: "grafana-ini-v2",
      namespace: "monitoring",
    },
    data: {
      "grafana.ini": pulumi.interpolate`
[database]
url = mysql://root:password@mysql:3306/grafana
[auth.generic_oauth]
enabled = true
allow_sign_up = true
team_ids =
allowed_organizations =
name = PingCAP Auth
client_id = ${grafanaAuth0ClientId}
client_secret = ${grafanaAuth0ClientSecret}
scopes = openid profile email
auth_url = https://soc2-auth-static.shared.aws.tidbcloud.com/signin
token_url = https://${grafanaAuth0Domain}/oauth/token
api_url = https://${grafanaAuth0Domain}/userinfo
[server]
# The public facing domain name used to access grafana from a browser
domain = grafana.${oauthProxyDnsZone}
# Redirect to correct domain if host header does not match domain
# Prevents DNS rebinding attacks
enforce_domain = true
# The full public facing url you use in browser, used for redirects and emails
# If you use reverse proxy and sub path specify full url (with sub path)
root_url = https://grafana.${oauthProxyDnsZone}
[auth]
disable_login_form = true
# Set to true because we only have google oauth provider.
oauth_auto_login = true
signout_redirect_url = https://${grafanaAuth0Domain}/v2/logout?client_id=${grafanaAuth0ClientId}&returnTo=https://grafana.${oauthProxyDnsZone}
[users]
auto_assign_org = true
auto_assign_org_id = true
# Only allow people in the soc2 gmail group to log in, so we can grant to editor permission
auto_assign_org_role = Viewer
viewers_can_edit = true
default_theme = light
[unified_alerting]
enabled = true
[alerting]
enabled = false
[feature_toggles]
promQueryBuilder = true
`,
    },
  },
  { provider: baseProvider },
);

// Create a private ECR repository.
const repo = new aws.ecr.Repository("grafana");

// Get registry info (creds and endpoint).
const imageName = repo.repositoryUrl;
const registryInfo = repo.registryId.apply(async (id) => {
  const credentials = await aws.ecr.getCredentials({ registryId: id });
  const decodedCredentials = Buffer.from(
    credentials.authorizationToken,
    "base64",
  ).toString();
  const [username, password] = decodedCredentials.split(":");
  if (!password || !username) {
    throw new Error("Invalid credentials");
  }
  return {
    server: credentials.proxyEndpoint,
    username: username,
    password: password,
  };
});

// Build Grafana Image
const image = new docker.Image("grafana-image", {
  build: "grafana",
  imageName,
  registry: registryInfo,
  skipPush: process.env.SKIP_PUSH === "true",
});

export const grafanaImage = image.imageName;

// grafana dashboard files
const grafana = new k8s.yaml.ConfigGroup(
  "grafana",
  { files: "./grafana/*.yaml" },
  {
    provider: baseProvider,
    transformations: [
      (obj: any) => {
        // Set grafana image
        if (
          obj.props.kind === "Deployment" &&
          obj.props.metadata.name === "grafana-v2"
        ) {
          obj.props.spec.template.spec.containers[0].image = image.imageName;
          return obj;
        }
      },
    ],
  },
);

seedStacks.forEach((seedStack, _) => {
  const stackRegion = seedStack
    .replace(eksStackPrefix, "")
    .replace(gkeStackPrefix, "");

  let seedProvider: k8s.Provider;
  if (process.env.TEST_MODE) {
    seedProvider = new k8s.Provider(`${stackRegion}-provider`, {
      renderYamlToDirectory: `./render-${seedStack}`,
    });
  } else {
    const seed = new pulumi.StackReference(`${seedStack}`);
    seedProvider = new k8s.Provider(`${stackRegion}-provider`, {
      kubeconfig: seed.requireOutput("kubeconfig"),
    });
  }
  // To migrate to the new o11y infra.
  // If whitelist includes the current stack, then disable the tidb metric federation scrape job.
  new k8s.helm.v3.Chart(
    `${stackRegion}`,
    {
      path: "./charts/seed",
      values: {
        stack: seedStack,
        env: env,
        disableTiDBFederation: o11ySeedStackWhitelist.includes(seedStack)
          ? true
          : false,
        disableGarden: needDisabledGardenNamespace.includes(seedStack)
          ? true
          : false,
      },
    },
    { provider: seedProvider },
  );
});

freetierStacks?.forEach((stack, _) => {
  const stackRegion = stack.replace("sre-bot/eks-freetier/", "");

  let freetierProvider: k8s.Provider;
  if (process.env.TEST_MODE) {
    freetierProvider = new k8s.Provider(`${stackRegion}-provider`, {
      renderYamlToDirectory: `./render-${stack}`,
    });
  } else {
    const seed = new pulumi.StackReference(`${stack}`);
    freetierProvider = new k8s.Provider(`${stackRegion}-provider`, {
      kubeconfig: seed.requireOutput("kubeconfig"),
    });
  }
  new k8s.helm.v3.Chart(
    `${stackRegion}`,
    {
      path: "./charts/freetier",
      values: {
        stack: stack,
      },
    },
    { provider: freetierProvider },
  );
});
