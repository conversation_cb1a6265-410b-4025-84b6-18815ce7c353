// #!groovy

// import groovy.transform.Field

// @Field
// def pulumiPod = '''
// apiVersion: v1
// kind: Pod
// spec:
//   containers:
//   - name: main
//     image: gcr.io/pingcap-public/runbooks-pulumi:v3.38.0
//     command:
//     - cat
//     tty: true
//     resources:
//       requests:
//         memory: "4000Mi"
//         cpu: 1000m
//       limits:
//         memory: "10000Mi"
//         cpu: 2000m
//     env:
//     - name: DOCKER_HOST
//       value: tcp://localhost:2375
//     - name: AWS_WEB_IDENTITY_TOKEN_FILE
//       value: /identity/token
//     volumeMounts:
//     - mountPath: /identity
//       name: identity
//   - name: dind
//     image: docker:18.09-dind
//     securityContext:
//       privileged: true
//   - name: auth
//     image: gcr.io/pingcap-gardener/jenkins/gcp-web-identity:20201201
//     env:
//     - name: A<PERSON>_WEB_IDENTITY_TOKEN_FILE
//       value: /identity/token
//     volumeMounts:
//     - mountPath: /identity
//       name: identity
//   volumes:
//   - emptyDir: {}
//     name: identity
//   restartPolicy: Never
// '''
// pipeline {
//     agent any

//     options {
//         disableConcurrentBuilds()
//     }
//     environment {
//         PULUMI_ACCESS_TOKEN = credentials("dbaas-pulumi-access-token")
//         GOPATH = "/home/<USER>/agent/workspace/go"
//         AWS_REGION = "us-west-2"
//         GITHUB_BOT = credentials("github-sre-bot")
//     }
//     stages {
//         stage('Check'){
//             when {
//                 beforeAgent true
//                 changeRequest()
//             }
//             environment {
//                 AWS_ROLE_ARN = credentials("dbaas-dev-aws-role")
//             }
//             agent {
//                 kubernetes {
//                     yaml pulumiPod
//                     defaultContainer "main"
//                     customWorkspace "/home/<USER>/agent/workspace/go/src/github.com/tidbcloud/runbooks"
//                 }
//             }
//             steps{
//                 sh '''
//                 unset HUDSON_URL
//                 npm install
//                 echo "check alert rules"
//                 make check
//                 echo "check alertmanager config"
//                 make check-amc
//                 '''
//             }
//         }
//         stage('Delivery') {
//             environment {
//                 GIT_COMMIT_MSG = sh(returnStdout: true, script: "git log --oneline -n 1 --pretty=%B HEAD | head -n 1").trim()
//             }
//             agent {
//                 kubernetes {
//                     yaml pulumiPod
//                     defaultContainer "main"
//                     customWorkspace "/home/<USER>/agent/workspace/go/src/github.com/tidbcloud/runbooks"
//                 }
//             }
//             stages {
//                 // stage ('Preview'){
//                 //     parallel{
//                 //         stage('Preview dev') {
//                 //             environment {
//                 //                     ENV = "dev"
//                 //                     AWS_ROLE_ARN = credentials("dbaas-dev-aws-role")
//                 //                     PULUMI_CONFIG_PASSPHRASE = credentials("dbaas-dev-pulumi-pwd")
//                 //             }
//                 //             when {
//                 //                 beforeAgent true
//                 //                 allOf {
//                 //                     changeRequest()
//                 //                 }
//                 //             }
//                 //             steps {
//                 //                 runPulumi("preview --diff", "runbooks-dev")
//                 //             }
//                 //         }
//                 //         stage('Preview prod') {
//                 //             environment {
//                 //                     ENV = "prod"
//                 //                     AWS_ROLE_ARN = credentials("dbaas-prod-aws-role")
//                 //                     PULUMI_CONFIG_PASSPHRASE = credentials("dbaas-prod-pulumi-pwd")
//                 //             }
//                 //             when {
//                 //                 beforeAgent true
//                 //                 allOf {
//                 //                     changeRequest()
//                 //                 }
//                 //             }
//                 //             steps {
//                 //                 runPulumi("preview --diff", "runbooks-prod")
//                 //             }
//                 //         }
//                 //     }
//                 // }
//                 stage('Apply rules to dev') {
//                     environment {
//                             ENV = "dev"
//                             AWS_ROLE_ARN = credentials("dbaas-dev-aws-role")
//                             PULUMI_CONFIG_PASSPHRASE = credentials("dbaas-dev-pulumi-pwd")
//                     }
//                     when {
//                         beforeAgent true
//                         allOf {
//                             not { changeRequest() }
//                         }
//                     }
//                     stages {
//                         stage('Pulumi Up Dev') {
//                             steps {
//                                 runPulumi("up --yes", "runbooks-dev")
//                             }
//                             post {
//                                 unsuccessful {
//                                     script {
//                                         sendNotification("#runbooks-ci", 'FAILURE')
//                                     }
//                                 }
//                                 success {
//                                     script {
//                                         sendNotification("#runbooks-ci", 'SUCCESS')
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 }
//                 stage('Apply rules to staging') {
//                     environment {
//                             ENV = "staging"
//                             AWS_ROLE_ARN = credentials("dbaas-staging-aws-role")
//                             PULUMI_CONFIG_PASSPHRASE = credentials("dbaas-staging-pulumi-pwd")
//                     }
//                     when {
//                         beforeAgent true
//                         allOf {
//                             not { changeRequest() }
//                         }
//                     }
//                     stages {
//                         stage('Pulumi Up Staging') {
//                             steps {
//                                 runPulumi("up --yes", "runbooks-staging")
//                             }
//                             post {
//                                 unsuccessful {
//                                     script {
//                                         sendNotification("#runbooks-ci", 'FAILURE')
//                                     }
//                                 }
//                                 success {
//                                     script {
//                                         sendNotification("#runbooks-ci", 'SUCCESS')
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 }
//                 stage('Apply rules to prod') {
//                     environment {
//                             ENV = "prod"
//                             AWS_ROLE_ARN = credentials("dbaas-prod-aws-role")
//                             PULUMI_CONFIG_PASSPHRASE = credentials("dbaas-prod-pulumi-pwd")
//                     }
//                     when {
//                         beforeAgent true
//                         allOf {
//                             not { changeRequest() }
//                         }
//                     }
//                     stages {
//                         stage('Pulumi Up Prod') {
//                             steps {
//                                 runPulumi("up --yes", "runbooks-prod")
//                             }
//                             post {
//                                 unsuccessful {
//                                     script {
//                                         sendNotification("#runbooks-ci", 'FAILURE')
//                                     }
//                                 }
//                                 success {
//                                     script {
//                                         sendNotification("#runbooks-ci", 'SUCCESS')
//                                     }
//                                 }
//                             }
//                         }
//                     }
//                 }
//             }
//         }
//     }
// }

// def runPulumi(SUB_COMMAND, STACK) {
//     // unset KUBECONFIG to avoid creating resources without provider specified
//     // unset HODSON_URL so that pulumi can detect the CI environment correctly
//     sh """
//     unset KUBECONFIG
//     unset HUDSON_URL
//     npm install
//     npm run check
//     npm run lint
//     pulumi login https://api.pulumi.com
//     pulumi stack select --create --secrets-provider=passphrase ${STACK}
//     pulumi ${SUB_COMMAND}
//     """
// }

// def sendNotification(CHANNEL="#dbaas-infra-cd", STATUS="SUCCESS") {
//     def GIT_AUTHOR_NAME = sh(returnStdout: true, script: "git log -1 --pretty=format:'%an'").trim()
//     def slackmsg = "[<${env.RUN_DISPLAY_URL}|Pulumi up ${env.STAGE_NAME} to ${env.ENV}>] `${STATUS}`" + "\n" +
//             "Commit: <https://github.com/tidbcloud/runbooks/commit/${env.GIT_COMMIT}|${env.GIT_COMMIT_MSG}>" + "\n" +
//             "Branch: `${env.GIT_BRANCH}`, Authored by: `${GIT_AUTHOR_NAME}`"
//     def COLOR = 'good'
//     if (STATUS != "SUCCESS") {
//         COLOR = 'danger'
//     }
//     slackSend channel: CHANNEL, color: COLOR, teamDomain: 'pingcap', tokenCredentialId: 'slack-pingcap-token', message: "${slackmsg}"
// }
