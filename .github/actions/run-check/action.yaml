name: "Trigger Check Run for Automated PR"
description: "Trigger Check Run for Automated PR with Empty Commit"
inputs:
  pr-link:
    description: "Link of pr, starts with https"
    required: true
  token:
    description: "Github token to push the commit, don't use github.token"
    required: true
outputs: {}
runs:
  using: "composite"
  steps:
    - name: Set up github cli
      uses: sersoft-gmbh/setup-gh-cli-action@v2
      with:
        github-token: ${{ inputs.token }}
        version: stable
    - name: Get PR Head Ref
      id: gr
      env:
        GH_TOKEN: ${{ inputs.token }}
      shell: bash
      run: echo "ref=$(gh pr view --json headRefName ${{ inputs.pr-link }} | jq -r '.headRefName')" >> $GITHUB_OUTPUT
    - name: Checkout with token
      uses: actions/checkout@v3
      with:
        token: ${{ inputs.token }}
        ref: ${{ steps.gr.outputs.ref }}
    - name: Append Empty Commit to Trigger Check
      shell: bash
      run: |
        git config --local user.email "${{ github.actor }}@users.noreply.github.com"
        git config --local user.name "${{ github.actor }}"
        git commit --allow-empty -m "Run Status Check"
        git push origin HEAD:"${{ steps.gr.outputs.ref }}"
    