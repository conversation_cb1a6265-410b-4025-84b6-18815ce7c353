name: Apply New Rules

on:
  push:
    branches:
    - 'master'

  workflow_dispatch:
    inputs:
      env:
        description: deploy environment, e.g. dev/staging/prod
        required: true
      region:
        description: deploy region name, e.g. us-east-1/us-east1/eastus
        required: false
      vendor:
        description: cloud provider info, e.g. aws/gcp/azure
        required: false

jobs:
  apply-rules:
    runs-on: self-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.sha }}

      - name: Setup environment
        run: |
          chmod +x ./scripts/*.sh

      - name: Get Auth Token
        id: get-token
        run: |
          echo "TOKEN=$(./scripts/get-token.sh)" >> $GITHUB_OUTPUT
        env:
          AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
          AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}

      - name: install helm
        uses: azure/setup-helm@v3

      - name: Set up yq
        uses: vegardit/gha-setup-yq@v1
        with:
          version: "4.44.1"

      - name: Apply Dev & Staging Rules - EKS
        run: |
          STACK=eks make o11y-rules
          ./scripts/apply-rules.sh "dev" "eks"
        env:
          TOKEN: ${{ steps.get-token.outputs.TOKEN }}

      - name: Apply Dev & Staging Rules - Rest Vendors
        run: |
          STACK=none-eks make o11y-rules
          ./scripts/apply-rules.sh "dev" "gke"
          ./scripts/apply-rules.sh "dev" "azure"
          ./scripts/apply-rules.sh "dev" "alicloud"
        env:
          TOKEN: ${{ steps.get-token.outputs.TOKEN }}

      - name: Apply Prod Rules - EKS
        if: github.ref == 'refs/heads/master'
        run: |
          STACK=eks make o11y-rules
          ./scripts/apply-rules.sh "prod" "eks"
        env:
          TOKEN: ${{ steps.get-token.outputs.TOKEN }}

      - name: Apply Prod Rules - Rest Vendors
        if: github.ref == 'refs/heads/master'
        run: |
          STACK=non-eks make o11y-rules
          ./scripts/apply-rules.sh "prod" "gke"
          ./scripts/apply-rules.sh "prod" "azure"
          ./scripts/apply-rules.sh "prod" "alicloud"
        env:
          TOKEN: ${{ steps.get-token.outputs.TOKEN }}

      - name: Send Notification
        if: always()
        run: |
          STATUS="${{ job.status }}"
          COMMIT_MSG=$(git log --oneline -n 1 --pretty=%B HEAD | head -n 1)
          AUTHOR=$(git log -1 --pretty=format:'%an')
          ./scripts/send-notification.sh "$STATUS" "$COMMIT_MSG" "$AUTHOR" "${{ github.ref }}" "${{ github.sha }}"
        env:
          WEBHOOK_URL: 'https://open.feishu.cn/open-apis/bot/v2/hook/1482ebac-fcf1-4e1a-9f5b-78ff9ede9854'
