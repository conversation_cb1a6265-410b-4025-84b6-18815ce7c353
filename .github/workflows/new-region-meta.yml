name: Deploy new region meta

on:
  workflow_dispatch:
    inputs:
      vendor:
        description: "which cloud provider: aws or gcp"
        required: true
      env:
        description: "which env: dev, staging or prod"
        required: true
      region:
        description: "deploy to which region"
        required: true
      logicalRegionName:
        description: "logical region name"
        required: true
      tenantID:
        description: "tenant id related to logical region"
        required: false
      jobId:
        description: "job id"
        required: false
      autoMerge:
        description: "enable auto merge"
        required: false
        default: "true"
      author:
        description: "the person who invokes this action"
        required: false
        default: tidbcloud-bot

jobs:
  deployRegion:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v3
      - name: Add new region manifests
        if: ${{ github.event.inputs.env == 'prod' }}
        run: |
          bash scripts/manage-regions.sh \
            register \
            ${{ github.event.inputs.vendor }} \
            ${{ github.event.inputs.region }} \
            ${{ github.event.inputs.logicalRegionName }} \
            ${{ github.event.inputs.tenantID }}

      - name: Create Pull Request
        id: cpr
        uses: peter-evans/create-pull-request@v3
        with:
          token: ${{ github.token }}
          commit-message: Deploy regional meta in ${{ github.event.inputs.vendor }} ${{ github.event.inputs.env }} ${{ github.event.inputs.region }}
          committer: GitHub <<EMAIL>>
          author: ${{ github.actor }} <${{ github.actor }}@users.noreply.github.com>
          signoff: false
          branch: "create-pull-request/deploy-meta-${{ github.event.inputs.vendor }}-${{ github.event.inputs.env }}-${{ github.event.inputs.region }}"
          branch-suffix: timestamp
          delete-branch: true
          assignees: ${{ github.event.inputs.author }}
          title: "Deploy regional meta in ${{ github.event.inputs.vendor }} ${{ github.event.inputs.env }} ${{ github.event.inputs.region }}"
          body: |
            This PR is triggered by:
            - deploy job ${{ github.event.inputs.jobId }}
          labels: |
            automated pr
      - name: Trigger Check Run
        uses: ./.github/actions/run-check
        if: steps.cpr.outputs.pull-request-operation == 'created'
        with:
          pr-link: ${{ steps.cpr.outputs.pull-request-url }}
          token: ${{ secrets.GIT_ACTION_BOT }}

      - name: Enable Pull Request Automerge
        if: steps.cpr.outputs.pull-request-operation == 'created'
        uses: peter-evans/enable-pull-request-automerge@v1
        with:
          token: ${{ secrets.GIT_ACTION_BOT }}
          pull-request-number: ${{ steps.cpr.outputs.pull-request-number }}
          merge-method: squash

      - name: Approve the PR
        if: steps.cpr.outputs.pull-request-operation == 'created'
        uses: hmarr/auto-approve-action@v2
        with:
          github-token: ${{ secrets.GIT_ACTION_BOT }}
          pull-request-number: ${{ steps.cpr.outputs.pull-request-number }}
