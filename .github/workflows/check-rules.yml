name: Check Rules

on:
  pull_request:
    branches:
      - master

jobs:
  check-rules:
    runs-on: self-hosted

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install & Validate rules
        run: |
          curl -LO https://github.com/FUSAKLA/promruval/releases/download/v3.8.0/promruval_3.8.0_linux_amd64.tar.gz
          tar -xzf promruval_3.8.0_linux_amd64.tar.gz
          sudo mv promruval /usr/local/bin/promruval
          make check-rules

      - name: Comment on PR if check fails
        if: failure()
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GIT_ACTION_BOT }}
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '❌ Rule validation failed. Please check the CI logs and search `Validation FAILED` for details and fix any errors in your rules.'
            })
