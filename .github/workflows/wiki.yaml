name: Push to GH Wiki

on:
  push:
    branches:
      - main
    paths:
      - docs/**

jobs:
  sync:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v2
      - uses: actions/checkout@v2
        with:
          repository: ${{ github.repository }}.wiki
          path: wiki

      - name: sync wiki
        run: rsync -av --delete --exclude=README.md --exclude=.git docs/ wiki/

      - name: Commit files
        run: |
          git config --local user.name 'github-actions[bot]'
          git config --local user.email 'github-actions[bot]@users.noreply.github.com'
          git add .
          git diff-index --quiet HEAD || git commit -m "Automatically publish wiki" && git push
        working-directory: wiki
