name: Destroy new region meta

on:
  workflow_dispatch:
    inputs:
      vendor:
        description: "which cloud provider: aws or gcp"
        required: true
      env:
        description: "which env: dev, staging or prod"
        required: true
      region:
        description: "destroy which region"
        required: true
      logicalRegionName:
        description: "logical region name"
        required: true
      jobId:
        description: "job id"
        required: false
      autoMerge:
        description: "enable auto merge"
        required: false
        default: "true"
      author:
        description: "the person who invokes this action"
        required: false
        default: tidbcloud-bot

jobs:
  destroyRegion:
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v3
      - name: Add new region manifests
        if: ${{ github.event.inputs.env == 'prod' }}
        run: |
          bash scripts/manage-regions.sh \
            unregister \
            ${{ github.event.inputs.vendor }} \
            ${{ github.event.inputs.logicalRegionName }}

      - name: Create Pull Request
        id: cpr
        uses: peter-evans/create-pull-request@v3
        with:
          token: ${{ github.token }}
          commit-message: Destroy regional meta in ${{ github.event.inputs.vendor }} ${{ github.event.inputs.env }} ${{ github.event.inputs.region }}
          committer: GitHub <<EMAIL>>
          author: ${{ github.actor }} <${{ github.actor }}@users.noreply.github.com>
          signoff: false
          branch: "create-pull-request/destroy-meta-${{ github.event.inputs.vendor }}-${{ github.event.inputs.env }}-${{ github.event.inputs.region }}"
          branch-suffix: timestamp
          delete-branch: true
          assignees: ${{ github.event.inputs.author }}
          title: "Destroy regional meta in ${{ github.event.inputs.vendor }} ${{ github.event.inputs.env }} ${{ github.event.inputs.region }}"
          body: |
            This PR is triggered by:
            - deploy job ${{ github.event.inputs.jobId }}
          labels: |
            automated pr

      - name: Install GH CLI
        uses: dev-hanz-ops/install-gh-cli-action@v0.1.0
        with:
          gh-cli-version: 2.32.0

      - name: Enable Pull Request Automerge
        if: ${{ steps.cpr.outputs.pull-request-operation == 'created' && github.event.inputs.autoMerge == 'true' }}
        run: gh pr merge -s "${{ steps.cpr.outputs.pull-request-number }}" --admin
        env:
          GH_TOKEN: ${{ secrets.GIT_ACTION_BOT }}
