# Alert rules are owned by service developers

/rules/** @tidbcloud/autonomous @tidbcloud/tidbcloud-service @tidbcloud/dedicated-cluster-service
# customer's alert rules are managed by autonomous
/rules/customer/** @tidbcloud/autonomous @tidbcloud/escalation

# services are oncalled by service owners directly
# /rules/cloud-platform/** @tidbcloud/platform
/rules/data-platform/** @tidbcloud/data-platform @tidbcloud/autonomous @tidbcloud/dedicated-cluster-service
/rules/tidb-service/** @tidbcloud/tidbcloud-service @tidbcloud/core-svc  @tidbcloud/dedicated-cluster-service
/rules/dedicated/pd/** @tidbcloud/escalation @tidbcloud/autonomous
/rules/dedicated/tikv/** @tidbcloud/escalation @tidbcloud/autonomous
/rules/dedicated/tidb/** @tidbcloud/escalation @tidbcloud/autonomous
/rules/dedicated/tiflash/** @tidbcloud/escalation @tidbcloud/autonomous
/rules/dedicated/tiproxy/** @tidbcloud/tidb @tidbcloud/autonomous
/rules/autonomous/** @tidbcloud/autonomous
/rules/cloud-platform/aipower/** @tidbcloud/billing
/rules/cloud-platform/billing/** @tidbcloud/billing
/rules/cloud-platform/api-gateway/** @tidbcloud/dataapps
/rules/cloud-platform/dataservice/** @tidbcloud/dataapps
/rules/serverless/** @tidbcloud/serverless-engine
/rules/serverless-service/** @tidbcloud/serverless-engine
/rules/resilience/** @tidbcloud/escalation

# addon components of dedicated tier are oncalled by tidbcloud-service
/rules/dedicated/cluster-operation/** @tidbcloud/tidbcloud-service  @tidbcloud/dedicated-cluster-service
/rules/dedicated/dbaas-proxy/** @tidbcloud/tidbcloud-service  @tidbcloud/dedicated-cluster-service
/rules/dedicated/dbaas-reporter/** @tidbcloud/tidbcloud-service
/rules/dedicated/tidb-monitor/** @tidbcloud/tidbcloud-service
/rules/dedicated/tidb-operator/** @tidbcloud/tidbcloud-service  @tidbcloud/dedicated-cluster-service
/rules/dedicated/tidb-auditlog/** @tidbcloud/tidbcloud-service  @tidbcloud/dedicated-cluster-service
/rules/dedicated/k8s/** @tidbcloud/tidbcloud-service
/rules/dedicated/swat/** @tidbcloud/tidbcloud-service

# cluster ng
/rules/cluster-next-gen/** @tidbcloud/tidbcloud-service @tidbcloud/autonomous

# Alert rule deployment is served by tidbcloud-service and autonomous team
/charts/** @tidbcloud/tidbcloud-service @tidbcloud/autonomous
