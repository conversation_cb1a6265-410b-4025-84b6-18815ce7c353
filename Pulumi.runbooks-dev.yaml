encryptionsalt: v1:GVfiDmxSxBs=:v1:YVKdzvttApQ4uERM:9ZKoP6TNjlncM5N5EYBcpx6UML+/Mw==
config:
  aws:region: us-west-2
  runbooks:autonomousPagerDutyKey:
    secure: v1:6rR0NbZwWIoNne5T:YXajUJ5uupnbmywlyka+Zg4WHQTR5DBhKnrxK2ApuqCfC05RRXMWJxS4zEzU2sYO
  runbooks:computePagerDutyKey:
    secure: v1:gLIGbF2o1yCak6OX:PCE0Ujeor+PO4uzFj3uw5bo7kcuTvmlI3Ag7/JNbEhS6kqBBvSunuj1ytpYRRG7T
  runbooks:dbaas_platform_pagerduty_key:
    secure: v1:b2QR6e+H7Ws1Q8h+:LUf9QMO0yfUiUmwVQhyro3e3ehRtt8K33iXuIJ9xDUijlslFr0LvcgJKW3Uc1I/+
  runbooks:dpPagerDutyKey:
    secure: v1:HpkDISdhTWElAvH4:TNkFrb/psNdE8v8rtjO0+QdTV6L9p2alSSCs42+ApMCyAMYX7HmLo09ZHtFbHZbR
  runbooks:ecosystemPagerDutyKey:
    secure: v1:1dyJq/RcguBkS/Vq:vboe/TAcjtrd8inDhLyyYM9+2LLdI+8p/ob4Vn99+Vwy9DZ1IAKpYtvpCoYSXvja
  runbooks:enableAlertmanager: "false"
  runbooks:enableCumstomerAlert: "true"
  runbooks:enableInfra: "true"
  runbooks:env: dev
  runbooks:freetier-stacks:
  - sre-bot/eks-freetier/dev-us-east-1-f01
  runbooks:freetierPagerdutyKey:
    secure: v1:y9ZYxbAMHoJWp8XE:Hj6drqQRv/xRVuwb/QSFDGnhtfIG3D0AE9JU6R3uM0i0+VsF6n9aLRx2QMIdQyX3
  runbooks:gatewayTlsPagerDutyKey:
    secure: v1:/ocPpFkBCVuVSsZi:stdW151A9SpKghQU9QIsF07k8E16ivxAe3bp4yZvFMxR7HJJjj5uIRSdu+GOS6Kk
  runbooks:grafanaAuth0ClientId: 50ViMxqHn7zMjjtQGHk2lFw6EgyyxCnq
  runbooks:grafanaAuth0ClientSecret:
    secure: v1:kdt4SGemWajdmTM3:IdL27eSQVI5YHevZezrh4k1rInKC8Ukr8Bg5AaDjaknoghbJ9iji+roWmwwOyjO/NYe4kM5A2zyMfl7EpDpfNCVJCZ0XFIvh8CjCZ8b1e9E=
  runbooks:grafanaAuth0Domain: tidb-soc2.us.auth0.com
  runbooks:infra-base-stack: sre-bot/eks/dev-base-us-west-2
  runbooks:infra-seed-stacks:
  - sre-bot/eks/dev-seed-us-west-2
  - sre-bot/eks/dev-seed-us-east-1
  - sre-bot/gke/dev-seed-us-west1
  - sre-bot/gke/dev-seed-us-west2
  runbooks:infraMonitoringToken: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  runbooks:jpPagerDutyKey:
    secure: v1:qCubzL7NDGEd4w5e:3+6p4wSygkVaZoXTUZBV8QrKH5L7lMzxrzJT4pC4ek8t4sLoy6Trt7pt9mpli1Pb
  runbooks:o11y-seed-stack-whitelist:
  - sre-bot/eks/dev-seed-us-west-2
  - sre-bot/gke/dev-seed-us-west1
  runbooks:pagerduty_key:
    secure: v1:574jIa8b1TGC7cxc:uyBojUQT7odMnYRLaNgN8hdbUR3RDa1wArWK2dORShRSOOAxEQfYLd+AP/8zKgbV
  runbooks:portal-namespace: nightly-ms
  runbooks:sharedtierPagerdutyKey:
    secure: v1:McDzvGG8r68M18kz:Nd4Z1Zt52cR4ekAP4lGjO5Wlc+RXi0+UHZVXvyTsTtUKoJGRuL3G3LU0PkP/mXhU
  runbooks:slack_url:
    secure: v1:+KkdgfDpyjvPehj7:SGIHByHbZu6tu0XpGgq9cRdDAh0KciXKa3vo5ssmIctbgM1lD8yeJtK5hB66uPfZqGUmJBIroZRXGCNNPpZFEfvmI30UDxfGAA0hDj0y6BC1ZV/L/Dijw1VQC8w5
  runbooks:storagePagerDutyKey:
    secure: v1:gtn6GtbQm6btBUuE:CzxxTu5l25pB8A7r6JngC33fXzFDSgbc1+71qJpar3Acl7VVOJzeCAxtmOpLS/iW
