## What Happening
ImportFail 是用户在使用 lighting 导入数据失败，这个报警是一个事件类型的报警，因为不会重试

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/PW4GBXN?utm_source=slack&utm_campaign=channel
https://pingcap.pagerduty.com/incidents/Q0KUIARWYDLH0Z

## Diagnosis
### 客户端行为导致的误报警
在报警的 Labels.msg 可以看到具体的失败原因，需要根据原因判断是客户端使用姿势不对还是 lightning 自身导致的错误，比如下面这条是因为客户端使用问题导致的，可以认为是一条误报警，需要修改报警规则让它不再继续报警
```
Labels:
 - msg = no schema create sql files found. Please either set `mydumper.no-schema` to true or add schema sql file for each database.

## Solution
### 客户端行为导致的误报警
可以先临时 silence 报警，然后修改下面的报警规则避免下次误报警
https://github.com/tidbcloud/runbooks/blob/76b4d499c4579caf8868d9f7fbedd78c363a14a4/rules/central/central.rules.yaml#L118-L125
```

```
Labels:
 - alertname = ImportFail
 - msg = [BR:ExternalStorage:ErrStorageInvalidConfig]invalid external storage config
Bucket wjktest123 is not accessible: AccessDenied: User: arn:aws:sts::380838443567:assumed-role/dbaas-shared-9e413da9-import/1637340101567390509 is not authorized to perform: sts:
```
> How to fixes the problem

## Post-Check
> Check issue fixes

