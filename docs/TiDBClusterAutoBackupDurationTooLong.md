## What Happening
TiDB 集群的自动备份超过 8 小时还没有完成

> 实现细节: 从用户的 schedule time 开始，如果当前时间减去 schedule time 大于 8 小时，且没有备份出现则触发报警。比如用户设置的 schedule time 是 2:00，但是备份是在 4:00 开始运行的，在 11:00 完成了，即使用户的真实使用的备份时间是 7 个小时，但是也会触发报警。因为用户希望的是在 10:00 能完成备份任务。

## Related Issues or Alerts
> Parse issue or alert link 

## Diagnosis
1. 查看 backcp CRD 的状态，backup 数据的大小确实是否符合预期
2. 查看 backcp pod 的日志定位慢的原因

## Solution

> How to fixes the problem

## Post-Check
当 backup 成功后报警被自动修复或有一个更晚创建的backup job 备份成功，报警被自动 resolve，自动备份只需要保证用户在当天存在一份更新的 backup

