## What Happening
TiDB 集群创建超时

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/P32UDMT?utm_source=slack&utm_campaign=channel
```
Labels:
 - alertname = TiDBClusterCreationDurationTooLong
 - cluster = base/eks/us-west-2
 - cluster_id = 1379661944579744065
 - endpoint = metrics
 - instance = **********:9090
 - job = central-metrics
 - name = og-test1
 - namespace = prod
 - pod = central-mipbivsb-54b8c458cc-6pgqp
 - prometheus = monitoring/k8s
 - provider = eks
 - region = us-west-2
 - service = central-metrics
 - severity = critical
 - status = preparing
 - tenant = 1372813089187581286
 - tenant_name = OnlineGiving.org
```

## Solution
创建 TiDB 集群需要分两种情况，第一种是用户是第一次创建 TiDB 集群，这个时候会创建 k8s 集群，即 shoot。然后再创建 tidbcluster.
1. 切换到 garden 集群，根据租户 id 和 region 定位到对应的 shoot, 查看当前 shoot 运行是否正常
```bash
kubectl get shoot -l tenant=1372813089187581286
```

2. 如果 shoot 运行正常，切换到对应的 shoot 集群，查看 tidbcloud 和 namespace=tidb1379661944579744065(namespace 对应的是 tidb${cluster_id}) 下的 pod 是否在正常 Running.
```bash
kubectl get tc -n tidb1379661944579744065
kubectl get po -n tidb1379661944579744065
```
2. 如果存在 pod 在 pending 的状态，查看 pod 的 event
3. 常见的情况有磁盘挂载不上，以及机器无法创建成功。如果是机器原因 event 只会显示无法调度，挂载不上的情况 event 会直接显示原因。
4. 如果是无法调度，可以通过 tidbcluster 上的调度 label 来 list 对应的机器是否正常创建
```yaml
kubectl get node -l label-key=label-value
```
5. 如果机器没有创建出来，则需要到 seed 集群中对应的 control-plane 中查看 machine controller 的日志以及 machinedeployment crd 的 event

## Post-Check
集群成功创建后报警会自动修复，可以查看 tidbcluster 的状态判断集群是否 ready
```bash
kubectl get tc -n tidb1379661944579744065 -w
```

