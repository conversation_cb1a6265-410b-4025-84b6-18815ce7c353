## What happening
Kubernetes pod always in pending status

## Related Issues or Alerts
<!-- Parse issue or alert link-->

## Diagnosis
### Auto-Scaling-Group max node group size reached
1. 通过 kubectl describe 命令查看 event
```sh
kubectl describe po kube-controller-manager-5b5f8fc7bd-szxf6
```

```bash
Events:
  Type     Reason             Age                    From                Message
  ----     ------             ----                   ----                -------
  Warning  FailedScheduling   60m (x22 over 65m)     default-scheduler   0/70 nodes are available: 1 node(s) had taints that the pod didn't tolerate, 12 Insufficient memory, 57 Insufficient cpu.
  Warning  FailedScheduling   40m (x60 over 65m)     default-scheduler   0/70 nodes are available: 12 Insufficient memory, 58 Insufficient cpu.
  Warning  FailedScheduling   30m (x23 over 38m)     default-scheduler   0/70 nodes are available: 11 Insufficient memory, 59 Insufficient cpu.
  Warning  FailedScheduling   25m (x5 over 26m)      default-scheduler   0/70 nodes are available: 1 node(s) had taints that the pod didn't tolerate, 10 Insufficient memory, 59 Insufficient cpu.
  Warning  FailedScheduling   4m44s (x41 over 26m)   default-scheduler   0/70 nodes are available: 10 Insufficient memory, 60 Insufficient cpu.
  Normal   NotTriggerScaleUp  4m36s (x349 over 65m)  cluster-autoscaler  pod didn't trigger scale-up (it wouldn't fit if a new node is added): 4 max node group size reached
  Warning  FailedScheduling   44s (x13 over 22m)     default-scheduler   0/70 nodes are available: 61 Insufficient cpu, 9 Insufficient memory.
```

## Solution

### Auto-Scaling-Group max node group size reached
1. increase Auto-Scaling-Group max node size, see https://github.com/tidbcloud/infra/pull/1020 for more detail

## Post-Check
<!-- Check issue fixes -->