## What Happening

TiKV 节点空间不足
## Related Issues or Alerts
> Parse issue or alert link 

## Diagnosis
> How to investigate the problem

## Solution

* 检查集群中的空间是否普遍不足。如果是，则需要扩容。
* 检查 Region balance 调度是否有问题。如果有问题，会导致数据分布不均衡。
  - 查看监控 PD/Statistics - balance 中的 Store Region score 和 Store Region count 监控，确认某个 tikv 是否存在不均衡
  - 如果 Region score 都比较均衡，但某个 tikv 的 available 容量较低，尝试降低该节点的 Region weight 来减少数据量。参考 PD Control 使用文档
    - 用 pd-ctl 设置，例如：>> store weight 1 5 10 #设置 store id 为 1 的 store 的 leader weight 为 5，Region weight 为 10
  - 如果 Region score 不均衡，用 pd-ctl 查看 region-schedule-limit 配置是否太低，导致调度太慢
  - 查看监控 PD/Scheduler 中的 Balance region movement ，查看是否有 Region 从不均衡的 TiKV 调出去，如果没有，请联系 PD 研发。
* 检查是否有文件占用了大量磁盘空间，比如日志、快照、core dump 等文件。
* 如果上述方案无法解决，请联系 PingCAP 研发。


## Post-Check
> Check issue fixes
