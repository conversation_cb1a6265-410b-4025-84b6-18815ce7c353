## What Happening
A certificate has not been ready to serve traffic for at least 10m

## Related Issues or Alerts
> Parse issue or alert link 

## Diagnosis
> How to investigate the problem

## Solution
A certificate has not been ready to serve traffic for at least 10m. Typically this means the cert is not yet signed. If the cert is being renewed or there is another valid cert, the ingress controller should be able to serve that instead. If not, need to investigate why the certificate is not yet ready.

Ensure cert-manager is configured correctly, no ACME/LetsEncypt rate limits are being hit. Ensure RBAC permissions are still correct for cert-manager.

## Post-Check
The alert will auto-resolve when issue fixed