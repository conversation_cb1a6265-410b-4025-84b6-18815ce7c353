## What Happening
TiDB 集群扩缩容超时

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/P6Z5V2N?utm_source=slack&utm_campaign=channel
```
Labels:
 - alertname = TiDBClusterScalingDurationMoreThan1h
 - cluster = base/eks/us-west-2
 - cluster_id = 1351028186028183552
 - created_at = 2021-01-18 04:46:35 +0000 UTC
 - endpoint = metrics
 - instance = **********:9090
 - job = central-metrics
 - name = cluster-whale
 - namespace = prod
 - pod = central-mipbivsb-54b8c458cc-6pgqp
 - prometheus = monitoring/k8s
 - provider = gcp
 - region = asia-northeast1
 - service = central-metrics
 - severity = critical
 - shoot = a0ce08f
 - stage = prod
 - status = scaling
 - tenant = 1351015692840734720
 - tenant_name = helloKitty
```


## Solution
目前 central 认为 failover 和扩缩都是 scaling 状态，分成两种情况。如上 helloKitty 租户其实是因为机器无法承受压力一直在 failover 导致的报警。还有一种是在正常扩缩容，但是 node 或者 pod 一直在 pending 的情况。

1. 查看 tidbcluster 所在的 namespace pod 是否在正常 running
2. 如果存在 pod 在 pending 的状态，查看 pod 的 event
3. 常见的情况有磁盘挂载不上，以及机器无法创建成功。如果是机器原因 event 只会显示无法调度，挂载不上的情况 event 会直接显示原因。
4. 如果是无法调度，可以通过 tidbcluster 上的调度 label 来 list 对应的机器是否正常创建
```yaml
kubectl get node -l label-key=label-value
```
5. 如果机器没有创建出来，则需要到 seed 集群中对应的 control-plane 中查看 machine controller 的日志以及 machinedeployment crd 的 event


## Post-Check
> Check issue fixes
