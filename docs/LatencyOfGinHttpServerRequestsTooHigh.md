This alert currently is for management-portal, metrics-server, billing-server.

When the latency is too high, we have to check whether the metadb QPS and latency in "DBaaS Controlplane" Grafana dashboard. Besides when it's the metrics-server, we should also check the cluster-proxy and infra-api to see whether they are CPU throttled in "Resource Overview" Grafana dashboard (base cluster, infra namespace).

When the problem exists in the dependency services (metadb, infra-api, cluster-proxy), we should try to recover them either by scaling up or find the root cause.