## What Happening
DBaaS Meta DB 的日常备份失败

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/P8XNY4X?utm_source=slack&utm_campaign=channel

## Diagnosis
1. 查看 backup pod 的日志定位问题
2. 检查 TiKV 是否 Running

## Solution
手动创建 Backup 来备份当天数据
```yaml
apiVersion: pingcap.com/v1alpha1
kind: Backup
metadata:
  name: tidb-backup-manual-2020-08-19t04-28-00
  namespace: prod
spec:
  backupType: full
  br:
    cluster: db
    sendCredToTikv: false
  from:
    host: db-tidb
    port: 4000
    secretName: backup-db-secret-jb8qxkrb
    user: backup
  resources: {}
  s3:
    bucket: prod-db-backup-fae114a
    prefix: daily-backup/db-pd.prod-2379-2020-08-19t04-28-00
    provider: aws
    region: us-west-2
  serviceAccount: tidb-backup-manager
```

## Post-Check
查看 backup 的运行结果直至运行完成
```bash
kubectl get backup tidb-backup-manual-2020-08-19t04-28-00
```
