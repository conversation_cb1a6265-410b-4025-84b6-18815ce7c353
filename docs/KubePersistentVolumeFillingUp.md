## What Happening
Kubernetes 磁盘快满了

## Related Issues or Alerts
> Parse issue or alert link

## Diagnosis
可以在 [grafana 监控面板](https://grafana.oauth.prod.aws.tidbcloud.com/d/919b92a8e8041bd567af9edab12c840c/kubernetes-persistent-volumes?orgId=1&refresh=10s) 中查看磁盘的监控信息

## Solution

### Volume resizing

1. 查看 PVC 所使用的 storageclass 是否支持 volume resizing. 可以运行以下指令进行检查

    ```bash
    kubectl get storageclass `kubectl -n <my-namespace> get pvc <my-pvc> -ojson | jq -r '.spec.storageClassName'`
    NAME                 PROVISIONER            RECLAIMPOLICY   VOLUMEBINDINGMODE   ALLOWVOLUMEEXPANSION   AGE
    standard (default)   kubernetes.io/gce-pd   Delete          Immediate           true                   28d
    ```

    如果 ALLOWVOLUMEEXPANSION is true, 我们可以直接通过修改 PVC 的 request 进行扩容。

2. 修改 pvc 的 spec.resources.requests.storage 的大小即可, 下面以 grafana 的 PersistentVolumeClaim 为例

    ```bash
    kubectl -n <my-namespace> edit pvc <my-pvc>
    ```

    ```yaml
    apiVersion: v1
    kind: PersistentVolumeClaim
    metadata:
    name: grafana
    namespace: monitoring
    spec:
    accessModes:
    - ReadWriteOnce
    resources:
        requests:
        storage: 10Gi
    storageClassName: gp2
    volumeMode: Filesystem
    volumeName: pvc-382be538-9c9a-489e-a818-6a66a38a8831
    ```

### Migrate data to a new, larger volume

TODO: contact <EMAIL>

## Post-Check
查看 pv 是否已经扩容到指定的大小
```sh
kubectl get pv pvc-382be538-9c9a-489e-a818-6a66a38a8831
```
