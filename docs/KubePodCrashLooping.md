
## What Happening
kubernetes pod 不断重启

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/PH992XQ?utm_source=slack&utm_campaign=channel

## Diagnosis
### OOM
1. 使用 kubectl get 命令查看容器的 status
```sh
$ kubectl get po kube-controller-manager-6cb7fd7f7-7l76j -o yaml
```

2. 查看 status.containerStatuses 字段，如果 exitCode 为 137，说明是因为 oom 导致重启
```yaml
status:
  containerStatuses:
  - lastState:
      terminated:
        exitCode: 137
```

### CPU Throtted
1. 使用 kubectl describe 命令查看 pod 的 event
```sh
$ kubectl describe po kube-controller-manager-6cb7fd7f7-7l76j
```
在 event 中可以看到 `Liveness probe failed` 的关键信息，这种情况可以确定是 CPU 不足导致
```
Liveness probe failed: Get https://10.60.3.70:10258/healthz: read tcp 10.60.3.1:55472->10.60.3.70:10258: read: connection reset by peer
```



## Solution
### OOM 
1. 修改 resources 字段，调高 limit.memory 恢复服务
2. 如果组件存在 VPA，且 VPA 使用 Recreate 或 Auto 策略，可以直接删除该 pod 重建

### CPU Throtted
1. 修改 resources 字段，调高 request.cpu 恢复服务

## Post-Check
Watch pod 的状态直至恢复
```sh
kubectl get po -w
```
