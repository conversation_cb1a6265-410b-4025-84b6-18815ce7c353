## What Happening

> The Kubernetes project is continually integrating new features, design updates, and bug fixes. The community releases new Kubernetes minor versions as generally available approximately every three months. Each minor version is supported for approximately twelve months after it's first released.

## Related Issues or Alerts

> On the end of support date, you can no longer create new Amazon EKS clusters with the unsupported version. Existing control planes are automatically updated by Amazon EKS to the oldest supported version through a gradual deployment process after the end of support date. After the automatic control plane update, you must manually update cluster add-ons and Amazon EC2 nodes.

> ReleaseCalander

- [EKS](https://docs.aws.amazon.com/eks/latest/userguide/kubernetes-versions.html#kubernetes-release-calendar)
- [GKE](https://cloud.google.com/kubernetes-engine/docs/release-notes)

## Diagnosis

> Cluster versions can be found in grafana dashboard

- [ProdVersion](https://grafana.oauth.prod.aws.tidbcloud.com/d/l73qzkhnz/cluster-overview?orgId=1)
- [StagingVersion](https://grafana.oauth.staging.shared.aws.tidbcloud.com/d/f6iXkE5nk/eks_workernode_info?orgId=1&search=open&folder=current)
- [DevVersion](https://grafana.oauth.dev.shared.aws.tidbcloud.com/d/u1HDIC5nk/eks-cluster-info?orgId=1&search=open&folder=current)

## Solution

> EksUpgrade

- Upgrade the cluster control plane
- Upgrade the nodes in your cluster
- Upgrade CoreDNS
- Upgrade autoscaler
- Upgrade kube-proxy
- Update Kubernetes manifests, as required

###### If you originally deployed your cluster on Kubernetes 1.17 or earlier, then you may need to remove a discontinued term from your CoreDNS manifest & update coredns version

https://docs.aws.amazon.com/eks/latest/userguide/managing-coredns.html#adding-coredns-eks-add-on

###### These phases are done in a dev/staging environment to completion, so that we can discover any issues in cluster configuration as well as application manifests in advance of upgrade operations on your production clusters. Start with the current Kubernetes version, with all of the production versions of add-ons, applications, and anything else that needs to be tested. Then run through the upgrade, deployment, and testing phases, making changes as needed.

###### Testing application's behavior against new Kubernetes versions in a non-production environment is always recommended before updating existing production environments.

###### Versions taken from git repo from respective master/prod/staging branches.

> GkeUpgrade

- Upgrade master node
- Upgrade NodePools

## Post-Check

> Validate after version is upgraaded

- [ProdVersion](https://grafana.oauth.prod.aws.tidbcloud.com/d/l73qzkhnz/cluster-overview?orgId=1)
- [StagingVersion](https://grafana.oauth.staging.shared.aws.tidbcloud.com/d/f6iXkE5nk/eks_workernode_info?orgId=1&search=open&folder=current)
- [DevVersion](https://grafana.oauth.dev.shared.aws.tidbcloud.com/d/u1HDIC5nk/eks-cluster-info?orgId=1&search=open&folder=current)
