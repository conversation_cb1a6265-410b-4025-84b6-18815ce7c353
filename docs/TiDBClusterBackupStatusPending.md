## What Happening
TiDB 集群的 backup 超过一个小时处于 pending 状态

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/P5XK9TI?utm_source=slack&utm_campaign=channel

## Solution
目前 backup cr 创建出来，不在 running、failed、prepare 都会认为是 pending 状态
1. 查看 backup custom resource 当前的状态
```bash
kubectl get backup -n tidb{cluster_id}
```
2. 确定 tidb-operator 在正常运行
```bash
kubectl get po -n tidb-admin
```

## Post-Check
该报警在处理后会自动修复