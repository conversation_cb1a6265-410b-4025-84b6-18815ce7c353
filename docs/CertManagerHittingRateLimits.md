## What Happening
Cert-manager is being rate-limited by the ACME provider

## Related Issues or Alerts
> Parse issue or alert link 

## Diagnosis
> How to investigate the problem

## Solution
Cert-manager is being rate-limited by the ACME provider. Let's Encrypt rate limits can last for up to a week. There could be up to a weeks delay in provisioning or renewing certificates, depending on the action that's being rate limited.

Let's Encrypt suggest the application process for extending rate limits can take a week. Other ACME providers could likely have different rate limits.

[Let's Encrypt Rate Limits](https://letsencrypt.org/docs/rate-limits/)

## Post-Check
The alert will auto-resolve when issue fixed