## What Happening
central 服务的 metrics 端口异常

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/PACCVHE?utm_source=slack&utm_campaign=channel

## Diagnosis
### metrics 端口异常
在 prometheus 的 Status -> Targets 页查看是否超时
https://prometheus.oauth.prod.aws.tidbcloud.com/targets#pool-prod/central/0

### Pod 没有在 Running 状态
根据 pod 的状态来定位，如果是 pending 则查看 central pod 的 event。如果是 crash 状态则通过日志来定位问题

## Solution

### metrics 端口异常
通过是超时导致，可以通过修改 central servicemonitor 的超时时间来解决

## Post-Check

修复后可以看到报警被 resolver

