## What Happening
出现该报警表明某个 region 的 promethues 挂了。目前每个 region 都有一个 prometheus 会往 alertmanager 发送 WatchDog 报警，如果 DeadMansSwitch 服务长时间没有接受到报警就会触发该报警

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/PTSRF6N?utm_source=slack&utm_campaign=channel

## Diagnosis
如下，报警的 labels 中会存在一个`labels = Watchdog base/eks/us-west-2 monitoring/k8s eks us-west-2 none` 信息，这里指的是 `base/eks/us-west-2` 的 prometheus 没有发送报警心跳
```
Labels:
 - alertname = DeadMansSwitchEvaluateFailedFor30m
 - cluster = base/eks/us-west-2
 - endpoint = http
 - instance = 10.0.120.65:8080
 - job = dead-mans-switch
 - labels = Watchdog base/eks/us-west-2 monitoring/k8s eks us-west-2 none
 - namespace = monitoring
 - pod = dead-mans-switch-6779fd9f6d-fdc65
 - prometheus = monitoring/k8s
 - provider = eks
 - region = us-west-2
 - service = dead-mans-switch
 - severity = critical
```

## Solution
1. 查看 prometheus pod 的运行状态
```bash
kubectl get po -n monitoring
```
2. 如果 pod 在 running，则需要查看 prometheus 的日志是否出现报警发送失败的情况
3. 如果报警发送失败，需要确定是客户端还是 server 端导致的
4. 如果是 server 端导致的错误，需要检查 ingress 是否在正常运行

> How to fixes the problem

## Post-Check
该报警在修复后会自动 resolve

