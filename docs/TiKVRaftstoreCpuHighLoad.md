
## What Happening

Raft Store CPU，raftstore 线程的 CPU 使用率过高

## Related Issues or Alerts
> Parse issue or alert link 

## Diagnosis
> How to investigate the problem

## Solution

* 如果一天内超过 80% 的时间都超过了阈值，且没有明显的热点情况，可以联系客户协商适当调整增加  raftstore.store-pool-size 
* 观察 Raft Propose 监控，看这个报警的 TiKV 节点是否明显有比其他 TiKV 高很多。如果是，表明这个 TiKV 上有热点，需要检查热点调度是否能正常工作
* 观察 Raft Process 监控，看 tick duration 是否很高。如果是，需要在raftstore.raft-base-tick-interval = “2s”

## Post-Check
> Check issue fixes

