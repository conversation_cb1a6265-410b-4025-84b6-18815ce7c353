**1. Create an API key for the tenant**

(currently no UI is available)

POST https://us-west-2.wenxuan.shared.aws.tidbcloud.com/api/v1/admin/api_key
Authorization: Bearer $opsPortalToken
```json
{
    "tenant_id": "1356140591045414912",
    "creator_user_id": "1",
    "key_name": "My test key"
}
```

Response:

```json
{
    "api_key": "APIKEY_I2B4JLRFO6OVRWDJ4AR4ILU6CVR72NRRLQBEN6EJB474WIEQTUQA"
}
```

**2. Optional: Use the API key to call measurements API**

The caller need to provide API key as a bearer token in the Authorization header, as: `<PERSON><PERSON> {APIKey}`.

Example request: 

```
GET https://us-west-2.wenxuan.shared.aws.tidbcloud.com/public_api/v1/clusters/1364429983438606336/monitoring/measurements?format=Prometheus
Authorization: Bearer APIKEY_I2B4JLRFO6OVRWDJ4AR4ILU6CVR72NRRLQBEN6EJB474WIEQTUQA
```

Example response:

```
# HELP tidbcloud_db_query_count Number of queries by statement type
# TYPE tidbcloud_db_query_count counter
tidbcloud_db_query_count{sql_type="Begin"} 1
tidbcloud_db_query_count{sql_type="Rollback"} 0
tidbcloud_db_query_count{sql_type="Set"} 2
tidbcloud_db_query_count{sql_type="Update"} 7
tidbcloud_db_query_count{sql_type="other"} 1
tidbcloud_db_query_count{sql_type="Commit"} 3
tidbcloud_db_query_count{sql_type="Delete"} 0
tidbcloud_db_query_count{sql_type="Insert"} 7
tidbcloud_db_query_count{sql_type="Replace"} 0
tidbcloud_db_query_count{sql_type="Select"} 3
tidbcloud_db_query_count{sql_type="Show"} 0
tidbcloud_db_query_count{sql_type="CreateDatabase"} 2
tidbcloud_db_query_count{sql_type="CreateTable"} 21
tidbcloud_db_query_count{sql_type="Use"} 1
# HELP tidbcloud_db_failed_query_count Number of failed queries
# TYPE tidbcloud_db_failed_query_count counter
# HELP tidbcloud_db_query_duration_seconds Queries durations of different quantiles aggregated in last minute by statement type
# TYPE tidbcloud_db_query_duration_seconds gauge
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Begin"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Execute"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Set"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Show"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="general"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Use"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Select"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="internal"} 0.012079999999999966
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Delete"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Insert"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Update"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Rollback"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="other"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Commit"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.99",sql_type="Replace"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Begin"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="other"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Commit"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Replace"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="internal"} 0.0066962962962962965
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Rollback"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Set"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Show"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Use"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Insert"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Update"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="general"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Delete"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Select"} NaN
tidbcloud_db_query_duration_seconds{quantile="0.80",sql_type="Execute"} NaN
# HELP tidbcloud_db_connection_count Current connection count
# TYPE tidbcloud_db_connection_count gauge
tidbcloud_db_connection_count 0
# HELP tidbcloud_process_cpu_seconds Elapsed CPU seconds by instances
# TYPE tidbcloud_process_cpu_seconds counter
tidbcloud_process_cpu_seconds{instance_name="db-tikv-2",component_type="tikv"} 5970.24
tidbcloud_process_cpu_seconds{instance_name="db-tidb-0",component_type="tidb"} 8493.81
tidbcloud_process_cpu_seconds{instance_name="db-tikv-0",component_type="tikv"} 6963.69
tidbcloud_process_cpu_seconds{instance_name="db-tikv-1",component_type="tikv"} 5561.52
# HELP tidbcloud_storage_used_bytes Current used storage size in bytes
# TYPE tidbcloud_storage_used_bytes gauge
tidbcloud_storage_used_bytes 149738513
# HELP tidbcloud_storage_capacity_bytes Current storage capacity in bytes
# TYPE tidbcloud_storage_capacity_bytes gauge
tidbcloud_storage_capacity_bytes 158130929664
```

**3. Collect from Prometheus**

Example config:

```yaml
global:
  scrape_interval: 30s

scrape_configs:
  - job_name: tidb_cloud
    scheme: https
    metrics_path: /public_api/v1/clusters/1364429983438606336/monitoring/measurements
    params:
      format: ["Prometheus"]
    bearer_token: I2B4JLRFO6OVRWDJ4AR4ILU6CVR72NRRLQBEN6EJB474WIEQTUQA
    static_configs:
    - targets: ["us-west-2.wenxuan.shared.aws.tidbcloud.com"]
```

**4: Optional: View metrics in Grafana**

Import the Grafana dashboard: https://gist.github.com/breeswish/fe00dd01a59a0f57dfb377dff2c423a0

![image](https://user-images.githubusercontent.com/1916485/109496525-ba734b80-7acb-11eb-8d60-77fb6fa67e6a.png)

Ref: [使用 Prometheus + Grafana 监控 TiDB Cloud 集群](https://github.com/tidbcloud/runbooks/wiki/Generate-Prometheus-API-Key-for-customer)