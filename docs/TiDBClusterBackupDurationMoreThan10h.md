## What Happening
TiDB 集群的备份时长超过 10 个小时

## Related Issues or Alerts
目前出现过 cloudchat 备份时长超过 8 小时的报警，已经确定是 br 的 issue
https://pingcap.pagerduty.com/incidents/PK0UAGB?utm_source=slack&utm_campaign=channel


## Solution
* 查看当前 backup pod 的运行状况，如果还在运行，需要通过监控查看对用户业务的影响，如果影响较大，可以考虑停止 backup(需要联系客户，通常客户会主动找上门)。
```bash
kubectl get backup -n tidb{cluster_id}
```

* 通过 backup pod 的日志定位问题
```bash
kubectl logs -f backup-* -n tidb{cluster_id}
```


## Post-Check
不能删除用户 backup 超时的记录，所以办法目前无法自动 resolve，可以先 ack，报警会在 24 小时后会停止报警。

