Contents
* [[ Home ]]
* '''Ops'''
** [[ Access the public monitoring API ]]
** [[ Troubleshoot cluster creation timeout ]]
* '''Infra'''
** kubernetes
*** [[ KubeNodeNotReady]]
*** [[ KubePodCrashLooping ]]
*** [[ KubePodNotReady ]]
*** [[ KubePersistentVolumeFillingUp ]]
*** [[ KubeletDown ]]
*** [[ KubeletCertRotate ]]
** cert manager
*** [[ CertManagerDown ]]
*** [[ CertManagerCertExpirySoon ]]
*** [[ CertManagerCertNotReady ]]
*** [[ CertManagerHittingRateLimits ]]
** blackbox
*** [[ HttpStatusCode ]]
*** [[ SslCertificateWillExpireSoon ]]
*** [[ DnsSlowLookup ]]

* '''Platform'''
** [[ CentralDown ]]
** [[ BillingDown ]]
** [[ DBaaSMetaDBDailyBackupFailed ]]
** [[ QPSOfMetaDBIsZero ]]
** [[ DBaaSSalesforceSyncLeadsFailed ]]
** [[ ImportFail ]]
** [[ TiDBClusterCreationDurationTooLong ]]
** [[ TiDBClusterStatusUnavailable ]]
** [[ TiDBClusterScalingDurationMoreThan1h ]]
** [[ TiDBClusterBackupDurationMoreThan10h ]]
** [[ TiDBClusterBackupStatusFailed ]]
** [[ TiDBClusterBackupStatusUnknown ]]
** [[ TiDBClusterBackupStatusPending ]]
** [[ TiDBClusterRestoreStatusFailed ]]
** [[ LatencyOfGinHttpServerRequestsTooHigh ]]

** [[ TODO: TiDBClusterReplicasMismatch ]]
** [[ TiDBClusterManualBackupStatusFailed ]]
** [[ TiDBClusterAutoBackupStatusFailed ]]
** [[ TiDBClusterManualBackupDurationTooLong ]]
** [[ TiDBClusterAutoBackupDurationTooLong ]]
** [[ TODO: TiDBClusterTerminatingDurationTooLong ]]
** [[ TODO: TiDBClusterResourcesNotReleased ]]

* '''TiDBCluster'''
** PD
*** [[ PDServerIsDown ]]
*** [[ PDDiscoverOfflineTikv ]]
*** [[ PDDiscoverLowSpaceStore ]]
*** [[ PDRegionDownPeerTooLong ]]
*** [[ PDRegionMissPeerTooLong ]]
*** [[ TiKVRegionPendingPeerTooLong ]]
** TiDB
*** [[ TiDBServerIsDown ]]
*** [[ TiDBDiscoveredTimeJumpBack ]]
*** [[ TiDBHighTokenUsage ]]
*** [[ TiDBQueryUnexpectedlyFailed ]]
*** [[ TiDBFrequentlyUnavailable ]]
*** [[ TiFlashFrequentlyUnavailable ]]
*** [[ AllServerIsDown ]]
** TiKV
*** [[ TiKVServerIsDown ]]
*** [[ TiKVServerDownMoreThan2 ]]
*** [[ TiKVServerDownMoreThan1AZ ]]
*** [[ TiKVLowSpace ]]
*** [[ TiKVVeryLowSpace ]]
*** [[ TiKVRaftstoreCpuHighLoad ]]
*** [[ TiKVApplyWorkerPoolCpuHighLoad ]]
*** [[ TiKVSchedulerWorkerCpuHighLoad ]]
*** [[ TiKVGrpcPollHighLoad ]]
*** [[ TiKVUnifiedReadPoolHighLoad ]]
*** [[ TiKVHighPriorityStorageReadPoolHighLoad ]]
*** [[ TiKVNormalPriorityStorageReadPoolHighLoad ]]
*** [[ TiKVLowPriorityStorageReadPoolHighLoad ]]
*** [[ TiKVHighPriorityCopCpuHighLoad ]]
*** [[ TiKVNormalPriorityCopCpuHighLoad ]]
*** [[ TiKVLowPriorityCopCpuHighLoad ]]

* '''Observability'''
** [[ WatchDogDown ]]
** [[ DeadMansSwitchEvaluateFailedFor30m ]]
** [[ VMagentTooManyWriteErrors ]]
** [[ VMagentTooManyRemoteWriteErrors ]]
** [[ VMagentTooManyScrapeTimeoutErrors ]]
** [[ VMAgentJobMissing ]]
** [[ VMagentSeriesLimitHourReached ]]
** [[ VMagentPersistentQueueIsDroppingData ]]
** [[ VectorTooManyHTTPError ]]
** [[ VectorTooManyComponentError ]]

** AlertManager
*** [[ AlertmanagerClusterCrashlooping ]]
*** [[ AlertmanagerClusterDown ]]
*** [[ AlertmanagerClusterFailedToSendAlerts ]]
*** [[ AlertmanagerConfigInconsistent ]]
*** [[ AlertmanagerFailedReload ]]
*** [[ AlertmanagerFailedToSendAlerts ]]
*** [[ AlertmanagerMembersInconsistent ]]

* '''KubernetesUpgrade'''
** [[ KubernetesUpgrade ]]

