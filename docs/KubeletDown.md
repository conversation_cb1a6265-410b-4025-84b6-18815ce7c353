## What Happening
prometheus 无法访问 kubelet 的 metrics 端口

## Related Issues or Alerts
https://pingcap.pagerduty.com/incidents/P2C8H1H?utm_source=slack&utm_campaign=channel

## Diagnosis
1. 检查 apiserver 是否 ready
2. 检查 apiserver 和 kubelet 之间的网络是否正常
3. 检查 prometheus 和 kubelet 之间的网络是否正常
4. 检查机器是否正常

## Solution
目前 KubeletDown 的报警遇到比较少，上述[报警](https://pingcap.pagerduty.com/incidents/P2C8H1H?utm_source=slack&utm_campaign=channel)是因为 seed 中 apiserver 异常导致，让 apiserver 变成 running 状态即可修复

## Post-Check
修复后报警会自动 resolver

