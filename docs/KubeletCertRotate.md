(可以参考 https://kubernetes.io/zh/docs/tasks/tls/certificate-rotation/)

当收到 Kubelet 证书即将过期的告警时, 可以按以下步骤来处理:
1. 到对应集群上 approve 所有 CertificateSigningRequest:
```bash
kubectl get csr --no-headers --context=<context-id> |grep -v Approved|awk '{system ("kubectl --context=<context-id> certificate approve " $1)}'
```
2. 进行巡检, 检查还有没有其他集群存在类似问题:
```bash
kubectl config get-contexts --no-headers=true|awk '{system ("echo " $1)}'|awk '{system ("echo " $1 " && kubectl get csr --context=" $1 " |grep -v Approved")}'
```