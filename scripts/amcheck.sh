#!/usr/bin/env bash
set -o errexit
set -o nounset
set -o pipefail

WORKSPACE=$(unset CDPATH && cd $(dirname "${BASH_SOURCE[0]}")/.. && pwd)

STACK=${1:-runbooks-dev}
MANIFESTS_PATH=${WORKSPACE}/render-sre-bot

if command -v amtool >/dev/null; then
    AMTOOL_BIN=$(command -v amtool)
else
    AMTOOL_BIN=${WORKSPACE}/bin/amtool
fi

unset KUBECONFIG
export TEST_MODE=true
export SKIP_PUSH=true
export PULUMI_CONFIG_PASSPHRASE=wearepingcap
pulumi login --local
pulumi stack select --create --secrets-provider=passphrase ${STACK}
echo "Check AMC: Generate kubernetes manifests files"
pulumi up -y
AMC_PATH=$(find ${MANIFESTS_PATH} -name 'secret-monitoring-alertmanager-main.yaml')
echo "Check AMC: Get the content of alertmanager.yaml and lint it with amtool."
grep -A100000 -e 'alertmanager.yaml:' ${AMC_PATH} | tail -n+2 | ${AMTOOL_BIN} check-config
