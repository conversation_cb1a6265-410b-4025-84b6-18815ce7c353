#!/bin/bash
set -e

# Script to manage regions in regions_metadata.json
# Usage: ./manage-regions.sh <register|unregister> <aws|gcp|azure|alicloud> <region>

ACTION=$1
CLOUD_PROVIDER=$2
REGION=$3
LOGICAL_REGION=$4
TENANT_ID=$5

WORKSPACE=$(unset CDPATH && cd $(dirname "${BASH_SOURCE[0]}") && pwd)
METADATA_FILE="$WORKSPACE/regions_metadata.json"

if [ ! -f "$METADATA_FILE" ]; then
  echo "Error: Metadata file $METADATA_FILE not found."
  exit 1
fi

# Validate action
if [ "$ACTION" != "register" ] && [ "$ACTION" != "unregister" ]; then
  echo "Error: Invalid action. Must be 'register' or 'unregister'."
  echo "Usage: $0 <register|unregister> <aws|gcp|azure|alicloud> <region>"
  exit 1
fi

# Validate cloud provider
if [[ ! "$CLOUD_PROVIDER" =~ ^(aws|gcp|azure|alicloud)$ ]]; then
  echo "Error: Invalid cloud provider. Must be 'aws', 'gcp', 'azure', or 'alicloud'."
  echo "Usage: $0 <register|unregister> <aws|gcp|azure|alicloud> <region>"
  exit 1
fi

# Validate region
if [ -z "$REGION" ]; then
  echo "Error: Region not specified."
  echo "Usage: $0 <register|unregister> <aws|gcp|azure|alicloud> <region>"
  exit 1
fi

# Perform action
MODIFIED_CONTENT=""
JQ_EXIT_CODE=0
OUTPUT_MESSAGE=""

if [ "$ACTION" == "register" ]; then
  # Check if region already exists
  if jq -e --arg key "$CLOUD_PROVIDER" --arg logical_region "$LOGICAL_REGION" '.[$key][] | select(.logical_region == $logical_region)' "$METADATA_FILE" > /dev/null; then
    echo "Logical Region '$LOGICAL_REGION' already exists for '$CLOUD_PROVIDER'."
    exit 0
  fi
  # Create a new region object and add it to the array
  MODIFIED_CONTENT=$(jq --arg key "$CLOUD_PROVIDER" --arg region "$REGION" --arg logical_region "$LOGICAL_REGION" --arg tenant "$TENANT_ID" '.[$key] += [{"region": $region, "logical_region": $logical_region, "tenantID": $tenant}]' "$METADATA_FILE")
  JQ_EXIT_CODE=$?
  OUTPUT_MESSAGE="Registered region '$LOGICAL_REGION' for '$CLOUD_PROVIDER'."
elif [ "$ACTION" == "unregister" ]; then
  # Check if region exists
  if ! jq -e --arg key "$CLOUD_PROVIDER" --arg logical_region "$LOGICAL_REGION" '.[$key][] | select(.logical_region == $logical_region)' "$METADATA_FILE" > /dev/null; then
    echo "Logical Region '$LOGICAL_REGION' does not exist for '$CLOUD_PROVIDER'."
    exit 0
  fi
  # Remove the region object from the array
  MODIFIED_CONTENT=$(jq --arg key "$CLOUD_PROVIDER" --arg logical_region "$LOGICAL_REGION" '.[$key] = [.[$key][] | select(.logical_region != $logical_region)]' "$METADATA_FILE")
  JQ_EXIT_CODE=$?
  OUTPUT_MESSAGE="Unregistered region '$LOGICAL_REGION' from '$CLOUD_PROVIDER'."
fi

if [ $JQ_EXIT_CODE -ne 0 ]; then
  echo "Error: jq command failed to process the metadata file."
  echo "jq output (if any): $MODIFIED_CONTENT"
  exit 1
fi

if [ -z "$MODIFIED_CONTENT" ]; then
    echo "Error: jq command produced empty output. This might indicate an issue with the input file or the query."
    exit 1
fi

# Write the modified content back to the file
echo "$MODIFIED_CONTENT" > "$METADATA_FILE"
WRITE_EXIT_CODE=$?

if [ $WRITE_EXIT_CODE -ne 0 ]; then
  echo "Error: Failed to write updated content to $METADATA_FILE."
  exit 1
fi

echo "$OUTPUT_MESSAGE"
echo "Successfully updated $METADATA_FILE."
