#!/usr/bin/env bash

set -euo pipefail

usage() {
    cat << EOF
"""
EXAMPLE:
# ssh to bastion
$ scripts/bastion.sh "exaplain why to login bastion"

Available Environment:
  ENV      dev or prod, default is prod
"""
EOF
}

reason=${1:-""}
[ -z "$reason" ] && echo """You must specify a reason to login to bastion machine, eg:
./scripts/bastion.sh 'troubleshoot TCOC-xxx'""" && exit 1

ENV=${ENV:-prod}

iamUser=$(aws-vault exec ${ENV} -- aws sts get-caller-identity --output yaml | awk '/Arn/{print $2}' | awk -F'/' '{print $2}')
instanceId=$(aws-vault exec ${ENV} --region us-west-2 -- aws ec2 describe-instances \
                        --filters "Name=tag:Name,Values=bastion-${iamUser}" \
                        --query "Reservations[].Instances[?State.Name == 'running'].InstanceId[]" \
                        --output text)

aws-vault exec ${ENV} --region us-west-2 -- aws ssm start-session --target ${instanceId} --reason "${reason}"
