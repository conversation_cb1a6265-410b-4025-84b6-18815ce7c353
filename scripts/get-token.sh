#!/bin/bash

set -e
AUTH0_URL="https://tidb-soc2.us.auth0.com/oauth/token"
AUTH0_AUDIENCE="https://tidb-soc2.us.auth0.com/api/v2/"
# Get authentication token from Auth0
response=$(curl --silent --location --request POST "${AUTH0_URL}" \
  --header 'content-type: application/x-www-form-urlencoded' \
  --data-urlencode "grant_type=client_credentials" \
  --data-urlencode "client_id=${AUTH0_CLIENT_ID}" \
  --data-urlencode "client_secret=${AUTH0_CLIENT_SECRET}" \
  --data-urlencode "audience=${AUTH0_AUDIENCE}")

# Extract access token
access_token=$(echo "$response" | jq -r '.access_token')

# Output token
echo "$access_token"
