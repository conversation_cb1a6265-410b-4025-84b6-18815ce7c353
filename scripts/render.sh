#!/usr/bin/env bash
set -o errexit
set -o nounset
set -o pipefail

stack=${1:-eks}
WORKSPACE=$(unset CDPATH && cd $(dirname "${BASH_SOURCE[0]}")/.. && pwd)


mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/base ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/base ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/base
helm template ${WORKSPACE}/charts/control-plane/base --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/base/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/base --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/base/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/base --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/base/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/dedicated-tier ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/dedicated-tier ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/dedicated-tier
helm template ${WORKSPACE}/charts/control-plane/dedicated-k8s-infra --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/dedicated-tier/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/dedicated-k8s-infra --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/dedicated-tier/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/dedicated-k8s-infra --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/dedicated-tier/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/dedicated-seed ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/dedicated-seed ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/dedicated-seed
helm template ${WORKSPACE}/charts/control-plane/dedicated-seed --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/dedicated-seed/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/dedicated-seed --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/dedicated-seed/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/dedicated-seed --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/dedicated-seed/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/serverless ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/serverless ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/serverless
helm template ${WORKSPACE}/charts/control-plane/serverless --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/serverless/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/serverless --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/serverless/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/serverless --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/serverless/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/clinic ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/clinic ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/clinic
helm template ${WORKSPACE}/charts/control-plane/clinic --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/clinic/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/clinic --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/clinic/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/clinic --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/clinic/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/nextgen-k8s-infra ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/nextgen-k8s-infra ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/nextgen-k8s-infra
helm template ${WORKSPACE}/charts/control-plane/nextgen-k8s-infra --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/control-plane/nextgen-k8s-infra/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/nextgen-k8s-infra --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/control-plane/nextgen-k8s-infra/rules.yaml
helm template ${WORKSPACE}/charts/control-plane/nextgen-k8s-infra --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/control-plane/nextgen-k8s-infra/rules.yaml

#####################################################################################################
###                                         Start data plane                                      ###
#####################################################################################################

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/dedicated-tier ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/dedicated-tier ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/dedicated-tier
helm template ${WORKSPACE}/charts/data-plane/dedicated --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/dedicated-tier/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/dedicated --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/dedicated-tier/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/dedicated --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/dedicated-tier/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/dev-tier ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/dev-tier ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/dev-tier
helm template ${WORKSPACE}/charts/data-plane/dev-tier --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/dev-tier/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/dev-tier --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/dev-tier/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/dev-tier --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/dev-tier/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/serverless ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/serverless ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/serverless
helm template ${WORKSPACE}/charts/data-plane/serverless --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/serverless/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/serverless --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/serverless/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/serverless --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/serverless/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/data-migration ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/data-migration ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/data-migration
helm template ${WORKSPACE}/charts/data-plane/data-migration | tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/data-migration/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/data-migration | tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/data-migration/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/data-migration | tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/data-migration/rules.yaml

mkdir -p ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/nextgen ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/nextgen ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/nextgen
helm template ${WORKSPACE}/charts/data-plane/nextgen --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/dev/tidb-cloud/data-plane/nextgen/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/nextgen --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/staging/tidb-cloud/data-plane/nextgen/rules.yaml
helm template ${WORKSPACE}/charts/data-plane/nextgen --set stack=${stack}| tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/data-plane/nextgen/rules.yaml

#####################################################################################################
###                                       Start logging                                           ###
#####################################################################################################

mkdir -p ${WORKSPACE}/newrules/prod/tidb-cloud/logging
helm template ${WORKSPACE}/charts/logging | tail -n +3 > ${WORKSPACE}/newrules/prod/tidb-cloud/logging/rules.yaml
