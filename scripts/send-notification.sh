#!/bin/bash

# This script sends a notification to <PERSON><PERSON><PERSON>
# Usage: ./send-notification.sh <status> <commit_msg> <author> <branch> <commit_sha>

STATUS=$1
COMMIT_MSG=$2
AUTHOR=$3
BRANCH=${4#refs/heads/}
COMMIT_SHA=$5

# Create JSON payload
DATA=$(cat <<EOF
{
    "msg_type": "post",
    "content": {
        "post": {
            "zh_cn": {
                "title": "Update runbooks rules template in branch [${BRANCH}]: ${STATUS}",
                "content": [
                    [{
                        "tag": "text",
                        "text": "Commit: "
                    },
                    {
                        "tag": "a",
                        "text": "${COMMIT_MSG}",
                        "href": "https://github.com/tidbcloud/runbooks/commit/${COMMIT_SHA}"
                    }],[{
                        "tag": "text",
                        "text": "Authored by: ${AUTHOR}"
                    }]
                ]
            }
        }
    }
}
EOF
)

# Remove newlines for curl
DATA=$(echo "$DATA" | tr -d '\n' | tr -d '\r')

# Send notification
curl -X POST -H 'Content-Type: application/json' -d "$DATA" "$WEBHOOK_URL"