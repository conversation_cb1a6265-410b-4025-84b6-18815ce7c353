#!/bin/bash
set -e
# This script applies rules for different environments and stacks
# Usage: ./apply-rules.sh <env> <stack>
# env: dev or prod
# stack: eks,gke,azure,alicloud

ENV=$1
STACK=$2

WORKSPACE=$(unset CDPATH && cd $(dirname "${BASH_SOURCE[0]}") && pwd)

# regions metadata
# Get all region objects as JSON strings
AWS_REGIONS=($(jq -c '.aws[]' "$WORKSPACE/regions_metadata.json"))
GCP_REGIONS=($(jq -c '.gcp[]' "$WORKSPACE/regions_metadata.json"))
AZURE_REGIONS=($(jq -c '.azure[]' "$WORKSPACE/regions_metadata.json"))
ALICLOUD_REGIONS=($(jq -c '.alicloud[]' "$WORKSPACE/regions_metadata.json"))

# Paths
DATA_PATH_DEV='newrules/dev/tidb-cloud/data-plane/'
CONTROL_PATH_DEV='newrules/dev/tidb-cloud/control-plane/'
DATA_PATH_PROD='newrules/prod/tidb-cloud/data-plane/'
CONTROL_PATH_PROD='newrules/prod/tidb-cloud/control-plane/'
LOGGING_PATH_PROD='newrules/prod/tidb-cloud/logging'

# rule IDs
DATA_PLANE_DEV_TIER_RULES_ID='tidb-cloud-data-plane-dev-tier-rule'
DATA_PLANE_DEDICATED_TIER_RULES_ID='95072d4e-5676-4084-bdf1-09aca85881f2'
DATA_PLANE_DATA_MIGRATION_RULES_ID='data-migration-data-plane-rule'
DATA_PLANE_SERVERLESS_RULES_ID='tidb-cloud-data-plane-serverless-rule'
DATA_PLANE_NEXTGEN_RULES_ID='tidb-cloud-data-plane-nextgen-rule'
CONTROL_PLANE_DEV_TIER_RULES_ID='tidb-cloud-control-plane-dev-tier-rule'
CONTROL_PLANE_DEDICATED_TIER_RULES_ID='d5025c09-d861-417c-9cfe-797bd97ce0c3'
CONTROL_PLANE_DEDICATED_SEED_RULES_ID='tidb-cloud-control-plane-dedicated-seed-rule'
CONTROL_PLANE_BASE_RULES_ID='tidb-cloud-control-plane-base-rule'
CONTROL_PLANE_CLINIC_RULES_ID='clinic-control-plane-clinic-rule'
CONTROL_PLANE_NEXTGEN_K8S_INFRA_RULES_ID='tidb-cloud-control-plane-nextgen-k8s-infra-rule'

GLOBAL_DOMAIN="www.gs.us-west-2.aws.observability.tidbcloud.com"
# GLOBAL_DOMAIN="www.gs.us-west-2.aws.observability-dev.pingcap.cloud"

# Function to get rules from path
get_rules() {
  local path=$1
  local rules=""
  local files=$(find "$path" -name "*.yaml")

  for file in $files; do
    content=$(yq eval -o=json "$file" | jq -c '.groups')
    if [ -n "$content" ]; then
      rules="${rules}${content},"
    fi
  done

  echo "${rules%,}" | jq -s '{groups: add} '
}

# Function to write rules
write_rule() {
  local vendor=$1
  local region_obj=$2
  local rule_id=$3
  local rules=$4

  # Extract region info from the region object
  local region=$(echo "$region_obj" | jq -r '.region')
  local logical_region=$(echo "$region_obj" | jq -r '.logical_region')
  local tenant_id=$(echo "$region_obj" | jq -r '.tenantID')

  echo "Writing rules: ${rule_id} to ${vendor}, region: ${region}, logical_region: ${logical_region}"

  # Create JSON payload using region
  data=$(cat <<EOF
{
  "vendor": "${vendor}",
  "region": "${region}",
  "templateID": "${rule_id}",
  "monitorPromRules": ${rules}
}
EOF
)
  if [[ $rule_id == "logging" ]];then
    data=$(cat <<EOF
{
  "vendor": "${vendor}",
  "region": "${region}",
  "rulesConfig": ${rules}
}
EOF
)
  fi

  # Write data to temporary file to avoid "Argument list too long" error
  tmp_file=$(mktemp)
  echo "${data}" > "${tmp_file}"

  # Build curl headers
  headers=(
    --header "Authorization: Bearer ${TOKEN}"
    --header "Content-Type: application/json"
  )

  # Add tenant-specific headers if tenantID is not null/empty
  if [ "$tenant_id" != "null" ] && [ -n "$tenant_id" ]; then
    headers+=(--header "X-Org-Id: ${tenant_id}")
    headers+=(--header "X-Biz-Type: byoc")
    echo "Adding tenant headers for tenant ID: ${tenant_id}, biztype: byoc"
  fi

  url="http://${GLOBAL_DOMAIN}/api/v1/alertrule-template"
  if [[ $rule_id == "logging" ]];then
    url="http://${GLOBAL_DOMAIN}/api/v1/alertrule-template/logging"
  fi

  # Send HTTP request using file input instead of direct data
  response=$(curl --silent --location --connect-timeout 10 -m 20 --request POST $url \
    "${headers[@]}" \
    --data @"${tmp_file}")

  # Clean up temporary file
  rm "${tmp_file}"

  # Check response
  code=$(echo "$response" | jq -r .code)
  if [ "$code" != "0" ]; then
    echo "Error writing rule: $response"
    return 1
  fi

  echo "Write rule: ${rule_id} to ${vendor}, region: ${region}, logical_region: ${logical_region}, done"
}

# Apply rules based on environment and stack
if [ "$ENV" == "dev" ]; then
  # Dev environment
  DATA_PATH="${DATA_PATH_DEV}"
  CONTROL_PATH="${CONTROL_PATH_DEV}"
  SUFFIX="-dev"
else
  # Prod environment
  DATA_PATH="${DATA_PATH_PROD}"
  CONTROL_PATH="${CONTROL_PATH_PROD}"
  LOGGING_PATH="${LOGGING_PATH_PROD}"
  SUFFIX=""
fi

# Apply rules for the specified stack
if [ "$STACK" == "eks" ]; then
  VENDOR="aws"
  REGIONS=("${AWS_REGIONS[@]}")
# azure and alicloud
elif [ "$STACK" == "azure" ]; then
  VENDOR="azure"
  REGIONS=("${AZURE_REGIONS[@]}")
elif [ "$STACK" == "alicloud" ]; then
  VENDOR="alicloud"
  REGIONS=("${ALICLOUD_REGIONS[@]}")
else
  VENDOR="gcp"
  REGIONS=("${GCP_REGIONS[@]}")
fi

# Get rules for each component
DATA_PLANE_DEV_TIER_RULES=$(get_rules "${DATA_PATH}dev-tier")
DATA_PLANE_DEDICATED_TIER_RULES=$(get_rules "${DATA_PATH}dedicated-tier")
DATA_PLANE_DATA_MIGRATION_RULES=$(get_rules "${DATA_PATH}data-migration")
DATA_PLANE_SERVERLESS_RULES=$(get_rules "${DATA_PATH}serverless")
DATA_PLANE_NEXTGEN_RULES=$(get_rules "${DATA_PATH}nextgen")
# control plane
CONTROL_PLANE_DEV_TIER_RULES=$(get_rules "${CONTROL_PATH}serverless")
CONTROL_PLANE_DEDICATED_TIER_RULES=$(get_rules "${CONTROL_PATH}dedicated-tier")
CONTROL_PLANE_DEDICATED_SEED_RULES=$(get_rules "${CONTROL_PATH}dedicated-seed")
CONTROL_PLANE_BASE_RULES=$(get_rules "${CONTROL_PATH}base")
CONTROL_PLANE_CLINIC_RULES=$(get_rules "${CONTROL_PATH}clinic")
CONTROL_PLANE_NEXTGEN_RULES=$(get_rules "${CONTROL_PATH}nextgen-k8s-infra")
# logging
LOGGING_RULES=$(get_rules "${LOGGING_PATH}")

echo "get all rules done"

# Initialize arrays to track success and failure
SUCCESSFUL_REGIONS=()
FAILED_REGIONS=()

# # Apply rules to all regions
for region_obj in "${REGIONS[@]}"; do
  region=$(echo "$region_obj" | jq -r '.region')
  logical_region=$(echo "$region_obj" | jq -r '.logical_region')
  region_failed=false

  echo "Processing region: $region (logical: $logical_region)"

  # Track failures for this region
  # start apply dataplane
  write_rule "$VENDOR" "$region_obj" "${DATA_PLANE_DEV_TIER_RULES_ID}${SUFFIX}" "$DATA_PLANE_DEV_TIER_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${DATA_PLANE_DEDICATED_TIER_RULES_ID}${SUFFIX}" "$DATA_PLANE_DEDICATED_TIER_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${DATA_PLANE_DATA_MIGRATION_RULES_ID}${SUFFIX}" "$DATA_PLANE_DATA_MIGRATION_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${DATA_PLANE_SERVERLESS_RULES_ID}${SUFFIX}" "$DATA_PLANE_SERVERLESS_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${DATA_PLANE_NEXTGEN_RULES_ID}${SUFFIX}" "$DATA_PLANE_NEXTGEN_RULES" || region_failed=true

  # start apply controlplane
  write_rule "$VENDOR" "$region_obj" "${CONTROL_PLANE_DEV_TIER_RULES_ID}${SUFFIX}" "$CONTROL_PLANE_DEV_TIER_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${CONTROL_PLANE_DEDICATED_TIER_RULES_ID}${SUFFIX}" "$CONTROL_PLANE_DEDICATED_TIER_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${CONTROL_PLANE_DEDICATED_SEED_RULES_ID}${SUFFIX}" "$CONTROL_PLANE_DEDICATED_SEED_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${CONTROL_PLANE_BASE_RULES_ID}${SUFFIX}" "$CONTROL_PLANE_BASE_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${CONTROL_PLANE_CLINIC_RULES_ID}${SUFFIX}" "$CONTROL_PLANE_CLINIC_RULES" || region_failed=true
  write_rule "$VENDOR" "$region_obj" "${CONTROL_PLANE_NEXTGEN_K8S_INFRA_RULES_ID}${SUFFIX}" "$CONTROL_PLANE_NEXTGEN_RULES" || region_failed=true

  if [[ $ENV == "prod" ]]; then
    # start apply logging
    write_rule "$VENDOR" "$region_obj" "logging" "$LOGGING_RULES" || region_failed=true
  fi

  # Add to appropriate array
  if [ "$region_failed" = true ]; then
    FAILED_REGIONS+=("$logical_region")
  else
    SUCCESSFUL_REGIONS+=("$logical_region")
  fi
done

# Print summary
echo ""
echo "========================================="
echo "DEPLOYMENT SUMMARY"
echo "========================================="
echo "Environment: $ENV"
echo "Stack: $STACK"
echo "Vendor: $VENDOR"
echo ""

if [ ${#SUCCESSFUL_REGIONS[@]} -gt 0 ]; then
  echo "✅ SUCCESSFUL REGIONS (${#SUCCESSFUL_REGIONS[@]}):"
  for region in "${SUCCESSFUL_REGIONS[@]}"; do
    echo "  - $region"
  done
  echo ""
fi

if [ ${#FAILED_REGIONS[@]} -gt 0 ]; then
  echo "❌ FAILED REGIONS (${#FAILED_REGIONS[@]}):"
  for region in "${FAILED_REGIONS[@]}"; do
    echo "  - $region"
  done
  echo ""
  echo "Exit code: 1 (some regions failed)"
  exit 1
else
  echo "🎉 All regions deployed successfully!"
  echo "Exit code: 0"
fi
