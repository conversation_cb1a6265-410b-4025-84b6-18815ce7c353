.PHONY: check

all: format check-pulumi check-rules

check: check-rules

check-rules: promruval
	@echo "🔍 Validating all rules with promruval..."
	@echo ""
	@echo "📁 Validating Loki rules..."
	bash -c 'shopt -s globstar && $(PROMRUVAL) validate --config-file rules/validation-general.yml --config-file rules/logging/validation.yml --support-loki rules/logging/**/*.yaml'
	@echo ""
	@echo "📁 Validating Prometheus rules..."
	find rules -mindepth 1 -maxdepth 1 -type d ! -name "logging" | xargs -I {} bash -c 'shopt -s globstar && $(PROMRUVAL) validate --config-file rules/validation-general.yml --config-file rules/validation-promql.yml {}/**/*.yaml'
	@echo "🎉 All rule validations completed successfully!"

check-rules-legacy: promtool
	@echo "🔧 Legacy validation with promtool (Prometheus rules only)..."
	@find rules/ -name "*.yaml" -not -path "rules/logging/*" -exec $(PROMTOOL) check rules {} \; 2>/dev/null || echo "No Prometheus rules found"

check-rules-all: check-rules check-rules-legacy
	@echo "✅ All rule validations completed"

format:
	npm run fmt

base:
	helm template ./charts/base --set stack=sre-bot/eks/dev-base-us-west-2

seed:
	helm template ./charts/seed -x ./templates/seed-rules.yaml --set stack=sre-bot/eks/dev-seed-us-west-2

shoot:
	helm template ./charts/seed -x ./templates/shoot-rules.yaml --set stack=sre-bot/eks/dev-seed-us-west-2

o11y-rules:
	./scripts/render.sh $(STACK)

LOCALBIN ?= $(shell pwd)/bin
$(LOCALBIN):
	mkdir -p $(LOCALBIN)

PROMTOOL ?= $(LOCALBIN)/promtool
PROMTOOL_VERSION ?= 41f1a8125e664985dd30674e5bdf6b683eff5d32# pseudo version of v2.32.1

PROMRUVAL ?= $(LOCALBIN)/promruval
PROMRUVAL_VERSION ?= 3.8.0

.PHONY: promtool
promtool: $(PROMTOOL) # promtool doesn't fit well with go install, if you're using go1.18, please downgrade to 1.16 or lower.
$(PROMTOOL): $(LOCALBIN)
ifeq (, $(shell which promtool))
	@GOBIN=$(LOCALBIN) GO111MODULE=on go get github.com/prometheus/prometheus/cmd/promtool@$(PROMTOOL_VERSION)
else
PROMTOOL=$(shell which promtool)
endif

.PHONY: amtool
amtool: $(AMTOOL)
$(AMTOOL): $(LOCALBIN)
ifeq (, $(shell which amtool))
	@GOBIN=$(LOCALBIN) GO111MODULE=on go install github.com/prometheus/alertmanager/cmd/amtool@$(AMTOOL_VERSION)
else
AMTOOL=$(shell which amtool)
endif

.PHONY: promruval
promruval: $(PROMRUVAL)
$(PROMRUVAL): $(LOCALBIN)
ifeq (, $(shell which promruval))
	@GOBIN=$(LOCALBIN) GO111MODULE=on go install github.com/fusakla/promruval/v3@latest
else
PROMRUVAL=$(shell which promruval)
endif
